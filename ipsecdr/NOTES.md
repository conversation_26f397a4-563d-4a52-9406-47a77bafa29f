Does the order of payloads in IKEv2 really matters, can I send configuration payload before or after AUTH payload without any problem? As as far I can recall the RFC do not stipulate the impossibility of it.


For all payloads strict checking and Warning shall be coded

While rewriting forge_inkev2_auth

dropping test-eap argument

Allow to select whether or not we send optional payloads such as IDr when in initiator mode

Logs performed on  a file per SA, all needed pcap path keys and logs


In ikev2 payloads optimize gen_authentication_dat to have fewer arguments, notably group auth_metho with ikev2_crypto think also abou id

Lisser le calcul de l integrite directement dans la classe crypto pour eviter de verifier si aead a chaque fois

Only 1 TS at the time could update

improve testing both initiator and responder etc

Implements other modes that Initator and Response
remnoved test ah in forge create child sa
in forge auth nat_t not used


In server class, notify_list, notify_options or even cookie parameter, will be used as config parameters, we cannot give it as a function argument because handle() function of the server must remain generic, in order to avoid having the need of writing different server implementations.
So we will and need to introduce these new configs options in server mode:
init_opts: {
    cookie: bool=False,
    notify_list: list[dict],
    notify_options: list[dict],
} 
auth_opts: {
    notify_list: list[dict]=None,
    notify_options: list[dict]=None,
} 
cchild_opts: {
    notify_list: list[dict]=None,
    notify_options: list[dict]=None,
} 
Two way of passing them either via a .ini config file or either via overlay config. But they MUST be defined for a correct behaviour.


Make a decorator to check if SPI are matching, crypto and keys defined before entering in any packet processing 
(For client sure, server can define it as checks in IPsecRequestHandler)

Evaluate the faisability of handling cooki / notify etc options via overlay config,
It means that for a test, an overlay config will come to override default config and add fields

STATIC TEST CASE TO DEV:

    - verify well being of multi proposal for init auth and child ( need to update handling of esn in Security Association payload seemingly)

    - server / client static tests
