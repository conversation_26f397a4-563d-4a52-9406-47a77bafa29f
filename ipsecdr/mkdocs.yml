# mkdocs.yml

site_name: IPsecDR Documentation
repo_url: https://git.services.lan/l123/ipsec-evaluator
site_description: IPsecDR documentation site
site_author: unknown developer

docs_dir: docs


theme:
  name: material
  palette:
    scheme: slate  # Use 'slate' for dark mode
    toggle:
      icon: material/weather-sunny  # Sun icon for light mode
      name: Switch to light mode  # Tooltip text for light mode
    primary: indigo
    accent: pink


plugins:
  - search
  - mkdocstrings:
      handlers:
        python:
          options:
            docstring_style: sphinx
          setup_commands:
            - import sys
            - sys.path.append("ipsecdr")
            - sys.path.append("tests")

nav:
  - Home: index.md
  - API Reference:
      - IPsecDR Package:
          - ipsecdr: api/ipsecdr.md
          - ipsecdr-utils: api/ipsecdr-utils.md
          - ipsecdr-engine: api/ipsecdr-engine.md
          - ipsecdr-core: 
              - IKEv2: api/ipsecdr-core-IKEv2.md
              - ESP: api/ipsecdr-core-ESP.md
              - crypto: api/ipsecdr-core-crypto.md
      - Tests:
          - tests: api/tests.md
  - User Guide:
      - 'Guide Section':
        - Introduction: guide/introduction.md
        - Installation: guide/installation.md
        - Usage: guide/usage.md
        - Project-Management: guide/project-management.md
  - Developer Guide:
      - 'Guide Section':
        - Practices: dev-guide/practices.md
        - Roadmap: dev-guide/roadmap.md
        - Testing: dev-guide/testing.md
        - 'Writing Scenarios': dev-guide/scenarios.md

extra_css:
  - _extras/css/extra.css

extra_javascript:
  - _extras/js/extra.js