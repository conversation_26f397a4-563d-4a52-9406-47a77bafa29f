{"outcomes": [{"test_id": 1, "test_name": "T.01_SRV_ENUM_INIT_ALGO", "result": "Passed", "reason": "Server refused invalid SA", "details": {"info": ""}}, {"test_id": 2, "test_name": "T.01_SRV_ENUM_INIT_ALGO", "result": "Passed", "reason": "Server refused invalid SA", "details": {"info": ""}}, {"test_id": 3, "test_name": "T.01_SRV_ENUM_INIT_ALGO", "result": "Passed", "reason": "Server refused invalid SA", "details": {"info": ""}}, {"test_id": 4, "test_name": "T.01_SRV_ENUM_INIT_ALGO", "result": "Passed", "reason": "Server refused invalid SA", "details": {"info": ""}}, {"test_id": 5, "test_name": "T.01_SRV_ENUM_INIT_ALGO", "result": "Passed", "reason": "Server refused invalid SA", "details": {"info": ""}}, {"test_id": 6, "test_name": "T.01_SRV_ENUM_INIT_ALGO", "result": "Passed", "reason": "Server refused invalid SA", "details": {"info": ""}}, {"test_id": 7, "test_name": "T.01_SRV_ENUM_INIT_ALGO", "result": "Passed", "reason": "Server refused invalid SA", "details": {"info": ""}}, {"test_id": 8, "test_name": "T.01_SRV_ENUM_INIT_ALGO", "result": "Passed", "reason": "Server refused invalid SA", "details": {"info": ""}}, {"test_id": 9, "test_name": "T.01_SRV_ENUM_INIT_ALGO", "result": "Passed", "reason": "Server refused invalid SA", "details": {"info": ""}}, {"test_id": 10, "test_name": "T.01_SRV_ENUM_INIT_ALGO", "result": "Passed", "reason": "We sent a DR SA and peer sent a DR SA", "details": {"info": ""}}, {"test_id": 11, "test_name": "T.01_SRV_ENUM_INIT_ALGO", "result": "Passed", "reason": "Server refused invalid SA", "details": {"info": ""}}, {"test_id": 12, "test_name": "T.01_SRV_ENUM_INIT_ALGO", "result": "Passed", "reason": "Server refused invalid SA", "details": {"info": ""}}, {"test_id": 13, "test_name": "T.01_SRV_ENUM_INIT_ALGO", "result": "Passed", "reason": "Server refused invalid SA", "details": {"info": ""}}, {"test_id": 14, "test_name": "T.01_SRV_ENUM_INIT_ALGO", "result": "Passed", "reason": "Server refused invalid SA", "details": {"info": ""}}, {"test_id": 15, "test_name": "T.01_SRV_ENUM_INIT_ALGO", "result": "Passed", "reason": "Server refused invalid SA", "details": {"info": ""}}, {"test_id": 16, "test_name": "T.01_SRV_ENUM_INIT_ALGO", "result": "Passed", "reason": "Server refused invalid SA", "details": {"info": ""}}]}