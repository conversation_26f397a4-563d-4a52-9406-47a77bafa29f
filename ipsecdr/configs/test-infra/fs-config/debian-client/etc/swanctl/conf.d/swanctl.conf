connections {
   home-infra {
      remote_addrs = strongswan-gw.lan
      vips = 0.0.0.0

      local {
         auth = ecdsa
         certs = /etc/certs/debian-client-cert
         id = debian-client.ext
      }
      remote {
         auth = ecdsa
         id = strongswan-gw.lan
      }
      children {
         home-infra {
            remote_ts = 10.0.0.0/24
            start_action = start
            esp_proposals = aes256gcm16-ecp256bp-ecp256-esn,aes256ctr-sha256-ecp256bp-ecp256-esn
            mode = tunnel
         }
      }
      version = 2
      proposals = aes256gcm16-prfsha256-prfsha512-ecp256bp-ecp256,aes256ctr-sha256-prfsha256-prfsha512-ecp256bp-ecp256

   }
}

proposals {
    default {
        ike = aes256gcm16-aesctr256-prfsha256-prfsha512-ecp256r1-secp256r1
        esp = aes256gcm16-ecp256bp-ecp256-esn,aes256ctr-sha256-ecp256bp-ecp256-esn
    }
}
