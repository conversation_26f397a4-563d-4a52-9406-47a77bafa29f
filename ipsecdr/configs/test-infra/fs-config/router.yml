actions:
- action: '#!/bin/sh

    echo "{{ image.name }}" > /etc/hostname

    echo ''********    strongswan-gw.lan''>>/etc/hosts

    passwd root -d

    #!/bin/sh

    echo "net.ipv4.ip_forward = 1" > /etc/sysctl.conf'
  trigger: post-packages
- action: '#!/bin/sh

    systemctl enable inetd

    echo ''pts/0

    pts/1

    pts/2

    pts/3'' >> /etc/securetty'
  trigger: post-packages
- action: '#!/bin/bash

    sed -i ''/^auth\s\+required\s\+pam_securetty.so/d'' /etc/pam.d/login'
  trigger: post-packages
- action: '#!/bin/sh

    chmod +x /sbin/inet-forward.sh && systemctl enable net-setup

    #!/bin/sh

    echo "router" > /etc/hostname'
  trigger: post-files
files:
- generator: copy
  path: /etc/certs/ca_cert.pem
  source: /home/<USER>/Desktop/Works/taf/own/ipsec-dr.v3/ipsecdr/configs/test-infra/fs-config/router/etc/certs/ca_cert.pem
- generator: copy
  path: /etc/certs/ca_key.pem
  source: /home/<USER>/Desktop/Works/taf/own/ipsec-dr.v3/ipsecdr/configs/test-infra/fs-config/router/etc/certs/ca_key.pem
- generator: copy
  path: /etc/certs/router-cert
  source: /home/<USER>/Desktop/Works/taf/own/ipsec-dr.v3/ipsecdr/configs/test-infra/fs-config/router/etc/certs/router-cert
- generator: copy
  path: /etc/certs/router-key
  source: /home/<USER>/Desktop/Works/taf/own/ipsec-dr.v3/ipsecdr/configs/test-infra/fs-config/router/etc/certs/router-key
- generator: copy
  path: /etc/network/interfaces
  source: /home/<USER>/Desktop/Works/taf/own/ipsec-dr.v3/ipsecdr/configs/test-infra/fs-config/router/etc/network/interfaces
- generator: copy
  path: /etc/systemd/system/net-setup.service
  source: /home/<USER>/Desktop/Works/taf/own/ipsec-dr.v3/ipsecdr/configs/test-infra/fs-config/router/etc/systemd/system/net-setup.service
- generator: copy
  path: /sbin/inet-forward.sh
  source: /home/<USER>/Desktop/Works/taf/own/ipsec-dr.v3/ipsecdr/configs/test-infra/fs-config/router/sbin/inet-forward.sh
image:
  architecture: amd64
  description: Debian Minimal with systemd
  distribution: debian
  expiry: 30d
  name: debian-base
  release: buster
  variant: minimal
packages:
  cleanup: true
  manager: apt
  sets:
  - action: install
    packages:
    - systemd
    - curl
    - telnet
    - telnetd
    - tcpdump
    - openssh-server
    - iptables
    - dnsmasq
  - action: remove
    packages:
    - nano
  update: true
source:
  downloader: debootstrap
  url: https://deb.debian.org/debian
targets:
  lxc:
    config:
    - content: 'lxc.mount.auto = proc:mixed sys:mixed

        lxc.mount.entry = none dev/pts devpts defaults,create=dir 0 0

        lxc.tty.max = 0

        lxc.init.cmd = /lib/systemd/systemd

        lxc.init.uid = 0

        lxc.init.gid = 0'
      type: user
    create_message: '{{ image.description }}'
