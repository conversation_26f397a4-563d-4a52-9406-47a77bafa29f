actions:
- action: '#!/bin/sh

    echo "{{ image.name }}" > /etc/hostname

    echo ''********    strongswan-gw.lan''>>/etc/hosts

    passwd root -d'
  trigger: post-packages
- action: '#!/bin/sh

    systemctl enable inetd

    echo ''pts/0

    pts/1

    pts/2

    pts/3'' >> /etc/securetty'
  trigger: post-packages
- action: '#!/bin/bash

    sed -i ''/^auth\s\+required\s\+pam_securetty.so/d'' /etc/pam.d/login'
  trigger: post-packages
- action: '#!/bin/sh

    cp /etc/certs/ca_cert.pem /etc/swanctl/x509ca/

    #!/bin/sh

    cp /etc/certs/ca_key.pem /etc/swanctl/private/

    #!/bin/sh

    cp /etc/certs/strongswan-gw-cert /etc/swanctl/x509/

    #!/bin/sh

    cp /etc/certs/debian-client-cert /etc/swanctl/x509/

    #!/bin/sh

    cp /etc/certs/strongswan-gw-key /etc/swanctl/private/

    #!/bin/sh

    ip r a ***********/24 via ******** dev eth0

    #!/bin/sh

    echo "ipsecdr-client" > /etc/hostname'
  trigger: post-files
files:
- generator: copy
  path: /etc/certs/ca_key.pem
  source: /home/<USER>/Desktop/Works/taf/own/ipsec-dr.v3/ipsecdr/configs/test-infra/fs-config/ipsecdr-client/etc/certs/ca_key.pem
- generator: copy
  path: /etc/certs/ca_cert.pem
  source: /home/<USER>/Desktop/Works/taf/own/ipsec-dr.v3/ipsecdr/configs/test-infra/fs-config/ipsecdr-client/etc/certs/ca_cert.pem
- generator: copy
  path: /etc/certs/ipsecdr-client-cert
  source: /home/<USER>/Desktop/Works/taf/own/ipsec-dr.v3/ipsecdr/configs/test-infra/fs-config/ipsecdr-client/etc/certs/ipsecdr-client-cert
- generator: copy
  path: /etc/certs/ipsecdr-client-key
  source: /home/<USER>/Desktop/Works/taf/own/ipsec-dr.v3/ipsecdr/configs/test-infra/fs-config/ipsecdr-client/etc/certs/ipsecdr-client-key
image:
  architecture: amd64
  description: Debian Minimal with systemd
  distribution: debian
  expiry: 30d
  name: debian-base
  release: buster
  variant: minimal
packages:
  cleanup: true
  manager: apt
  sets:
  - action: install
    packages:
    - systemd
    - curl
    - telnet
    - telnetd
    - tcpdump
    - openssh-server
    - iptables
    - strongswan
    - charon-systemd
    - strongswan-swanctl
    - strongswan-charon
    - strongswan-libcharon
    - libstrongswan-standard-plugins
    - libstrongswan-extra-plugins
  - action: remove
    packages:
    - nano
  update: true
source:
  downloader: debootstrap
  url: https://deb.debian.org/debian
targets:
  lxc:
    config:
    - content: 'lxc.mount.auto = proc:mixed sys:mixed

        lxc.mount.entry = none dev/pts devpts defaults,create=dir 0 0

        lxc.tty.max = 0

        lxc.init.cmd = /lib/systemd/systemd

        lxc.init.uid = 0

        lxc.init.gid = 0'
      type: user
    create_message: '{{ image.description }}'
