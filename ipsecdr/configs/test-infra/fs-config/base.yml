image:
  distribution: debian
  release: buster
  architecture: amd64
  variant: minimal
  description: "Debian Minimal with systemd"
  name: debian-base
  expiry: 30d

source:
  downloader: debootstrap
  url: https://deb.debian.org/debian

targets:
  lxc:
    create_message: "{{ image.description }}"
    config:
      - type: user
        content: |-
          lxc.mount.auto = proc:mixed sys:mixed
          lxc.mount.entry = none dev/pts devpts defaults,create=dir 0 0
          lxc.tty.max = 0
          lxc.init.cmd = /lib/systemd/systemd
          lxc.init.uid = 0
          lxc.init.gid = 0

packages:
  manager: apt
  update: true
  cleanup: true
  sets:
    - packages:
        - systemd
        - curl
        - telnet
        - telnetd
        - tcpdump 
        - openssh-server
      action: install
    - packages:
        - nano
      action: remove


actions:
  - trigger: post-packages
    action: |-
      #!/bin/sh
      echo "{{ image.name }}" > /etc/hostname
      echo '********    strongswan-gw.lan'>>/etc/hosts
      passwd root -d
  - trigger: post-packages
    action: |-
      #!/bin/sh
      systemctl enable inetd
      echo 'pts/0
      pts/1
      pts/2
      pts/3' >> /etc/securetty
  - trigger: post-packages
    action: |-
      #!/bin/bash
      sed -i '/^auth\s\+required\s\+pam_securetty.so/d' /etc/pam.d/login
