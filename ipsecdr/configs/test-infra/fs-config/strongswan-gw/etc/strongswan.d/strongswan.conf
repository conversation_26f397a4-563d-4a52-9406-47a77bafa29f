# /etc/strongswan.conf - strongSwan configuration file

charon {
  load = random nonce aes sha1 sha2 openssl x509  pem pkcs1 curve25519 gmp x509 curl revocation hmac kdf vici kernel-netlink socket-default updown
}

charon {
    # Log options for the IKE daemon.
    filelog {
        /var/log/charon.log {
            time_format = %b %e %T  # Optional: formats the timestamp
            default = 3             # Sets the default log level to 4 (debug)
            flush_line = yes        # Ensures each log entry is flushed immediately
            ike = 4
            cfg = 4
            asn = 4
        }
    }

    # Optionally, set specific subsystems to debug level
    # Subsystems include: ike, cfg, asn, knl, net, enc, tls, etc.
    # Example:
    #    ike = 4
    #    cfg = 4
    #    asn = 4  # ASN.1 parser (useful for certificate issues)
}
