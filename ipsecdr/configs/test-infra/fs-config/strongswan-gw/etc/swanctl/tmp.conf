connections {
    version=2
    proposals="aes256gcm16-prfsha256-ecp256bp,aes256gcm16-prfsha256-ecp256,aes256ctr-sha256-prfsha256-ecp256bp,aes256ctr-sha256-prfsha256-ecp256,"
    encap=true
    mobike=false
    dpd_delay=30
    childless="force"
    send_certreq=true
    send_certreq_ocsp=false
    send_cert="ifasked"
    unique="never"
    pools=rw_pool
    rw {
        local {
          auth="ecdsa-256-sha256-ecdsabp-256-sha256-ike:ecdsa-256-sha256-ike:ecdsabp-256-sha256"
          certs = "strongswan-gw-cert"
          id = "strongswan-gw.lan"
        }
        remote {
          auth="ecdsa-256-sha256-ecdsabp-256-sha256-ike:ecsdsa-256-sha256-ike:ecsdsabp-256-sha256-ike:ecdsa-256-sha256-ike:ecdsabp-256-sha256"
        }
        children {
          rw {
            esp_proposals="aes256gcm16-esn-ecp256bp,aes256gcm16-esn-ecp256,"
            mode="tunnel"
          }
        }
    }
    ipsec-dr {
        version=2
        local_addrs="********,"
        local_port=4500
        remote_addrs="***********,"
        remote_port=4500
        proposals="aes256gcm16-prfsha256-ecp256bp,aes256gcm16-prfsha256-ecp256,aes256ctr-sha256-prfsha256-ecp256bp,aes256ctr-sha256-prfsha256-ecp256,"
        encap=true
        send_certreq=true
        local {
            auth="ecdsa-256-sha256-ecdsabp-256-sha256-ike:ecdsa-256-sha256-ike:ecdsabp-256-sha256"
            certs = "strongswan-gw-cert"
            id = "strongswan-gw.lan"
        }
        remote {
            auth="ecdsa-256-sha256-ecdsabp-256-sha256-ike:ecsdsa-256-sha256-ike:ecsdsabp-256-sha256-ike:ecdsa-256-sha256-ike:ecdsabp-256-sha256"
            id="debian-client.lan"
        }
        children {
        ipsec-dr {
            esp_proposals="aes256gcm16-esn-ecp256bp,aes256gcm16-esn-ecp256,"
            local_ts="0.0.0.0/0[/0-65535],"
            remote_ts="***********/24[/0-65535],"
            priority=1
            mode="tunnel"
            replay_window=1024
            ipcomp="no"
        }
        }
        unique="never"
        pools=""
  }
  }

pools {
    rw_pool {
      addrs = ********/16
    }
}



4FC195F44C5C5DF99615AA9A52A2965903D4FB267E4CD2A5A8B22C51859B3B1180908143
F7A5DA96EF95531FCBF97BCB3F0D00DA5F37B87D062EA669871677329A0F983BD1CE5A7B     


7B874709EDEF4B7FE4B26032A927512036514DC10624B3CA94575DAB50BBF6FD76E48739
BC6ABA3C558788628B82D24D82E54AD6B92E7ADC14335BC4EEA9145D238C7B28708DCF43
