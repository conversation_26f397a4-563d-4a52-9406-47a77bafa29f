connections {
   rw {
      local_addrs = 30.0.0.2
      local {
         auth = "ecdsa-256-sha256-ecdsabp-256-sha256-ike:ecdsa-256-sha256-ike:ecdsabp-256-sha256"
         certs = /etc/certs/strongswan-gw-cert
         id = strongswan-gw.lan
      }
      remote {
         auth = "ecdsa-256-sha256-ecdsabp-256-sha256-ike:ecsdsa-256-sha256-ike:ecsdsabp-256-sha256-ike:ecdsa-256-sha256-ike:ecdsabp-256-sha256"
         id = debian-client
         certs = /etc/swanctl/x509/debian-client-cert
      }
      children {
         rw {
            local_ts = 10.0.0.0/24
            esp_proposals = aes256gcm16-ecp256bp-ecp256-esn,aes256ctr-sha256-ecp256bp-ecp256-esn
            mode = tunnel
         }
      }
      proposals = "aes256gcm16-prfsha256-ecp256bp,aes256gcm16-prfsha256-ecp256,aes256ctr-sha256-prfsha256-ecp256bp,aes256ctr-sha256-prfsha256-ecp256,"
      send_certreq=true
      send_cert="ifasked"
   }
   version = 2 
   encap = yes
   childless = force
   unique = keep
}

pools {
    rw_pool {
      addrs = 10.3.0.0/16
    }
}

proposals {
    default {
        ike = aes256gcm16-aesctr256-prfsha256-prfsha512-ecp256r1-secp256r1
        esp = aes256gcm16-ecp256bp-ecp256-esn,aes256ctr-sha256-ecp256bp-ecp256-esn
    }
}
