[ARGS]
timeout = 15
verbose = True
logfile = /tmp/ipsec.log
# Chemin du binaire tcpreplay
tcpreplay = /usr/bin/tcpreplay
# Dossier de stockage pour les fichiers pcap
pcap_path = /tmp/pcap

[IF]
# Interface d'émission/réception
interface = br-ext
# IP du chiffreur émission
ipsec_src = ***********
# IP du chiffreur distant
ipsec_dst = ********
# IP d'émission inital
ip_src = ***********
# IP de destination final
ip_dst = *********
# Port d'émission
port_src = 4500
# Port de destination
port_dst = 4500

[NAT_T]
nat_t = True
nat_port_src = 4500
nat_port_dst = 4500

[Test_NAT_T]
src_port_no_NAT = 500
des_port_no_NAT = 500 

[IKE_SA]
# Les algorithmes pour la SA IKEV2
#sa_encr = ENCR_AES_CTR
sa_encr = ENCR_AES_GCM_16
sa_encr_size = 256
sa_prf = PRF_HMAC_SHA2_256
sa_integ = AUTH_HMAC_SHA2_256_128
#sa_groupdesc = brainpoolP256r1
sa_groupdesc = 256randECPgr


[AUTH]
#auth_method = ECDSA_BP256R1_SHA256
auth_method = ECDSA_SECP256R1_SHA256
#auth_method = DIGITAL_SIGNATURE
#auth_method = ECSDSA_SECP256R1_SHA256
#auth_method = ECSDSA_BP256R1_SHA256

[CHILD_SA]
# Les algorithmes pour les Child SA 
#child_sa_encr = ENCR_AES_CTR
child_sa_encr = ENCR_AES_GCM_16
child_sa_encr_size = 256
child_sa_integ = AUTH_HMAC_SHA2_256_128
#child_sa_groupdesc = brainpoolP256r1
child_sa_groupdesc = 256randECPgr
child_sa_esn = ESN
#child_sa_esn = NO_ESN

[CA]
# *** Seul le format PEM est accepté *** #
ca_root = /home/<USER>/Desktop/OWN/taf/own/ipsec_dr-scapy26/cfg/test-infra/fs-config/debian-client/etc/certs/ca_cert.pem
#ca_int1 = /etc/ipsec.d/cacerts/ca_int1.pem
#ca_int2 = /etc/ipsec.d/cacerts/ca_int2.pem
pub_key = /home/<USER>/Desktop/OWN/taf/own/ipsec_dr-scapy26/cfg/test-infra/fs-config/debian-client/etc/certs/debian-client-cert
prv_key = /home/<USER>/Desktop/OWN/taf/own/ipsec_dr-scapy26/cfg/test-infra/fs-config/debian-client/etc/certs/debian-client-key
#trust_chain = %(pub_key)s;%(ca_int2)s;%(ca_int1)s
trust_chain = %(pub_key)s

[TS]
# Traffic selector client
tsi_ip_range   = ***********-************
tsi_port_range = 0-65535
# Traffic selector serveur
tsr_ip_range   = 10.0.0.0-**********
tsr_port_range = 0-65535

[ID]
idi_type = ID_FQDN
idi_data = debian-client.lan
idr_type = ID_FQDN
idr_data = strongswan-gw.lan

[TEST_SP]
ip_dst_interdit = ***********
