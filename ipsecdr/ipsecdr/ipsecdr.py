#!/usr/bin/python
import sys
import argparse
import logging
from ipsecdr.utils.logger import logger

from ipsecdr.utils.configuration import IPsecDRConfigParser
from ipsecdr.engine.scenario import TestConfiguration
from ipsecdr.engine.orchestration import Orchestrator


def main():
    """
    Main entry point for the IPsecDR tool.

    This function parses command-line arguments, initializes the configuration,
    and starts the orchestrator to execute the tests.

    :returns: None
    """
    parser = argparse.ArgumentParser(description="IPsecDR Tool")
    parser.add_argument(
        "-c",
        "--config",
        required=True,
        metavar="config_file",
        type=str,
        help="Path to the main configuration file",
    )
    parser.add_argument(
        "-v", "--verbose", action="store_true", help="Enable verbose mode"
    )
    parser.add_argument(
        "--test-list",
        metavar="test_list_file",
        type=str,
        help="Path to a txt test list file",
    )
    parser.add_argument(
        "--tests",
        metavar="test_names",
        type=str,
        nargs="+",
        help="List of tests to execute (overrides test list file)",
    )
    parser.add_argument(
        "--max-testers",
        metavar="N",
        type=int,
        default=1,
        help="Maximum number of concurrent testers (default: 1)",
    )
    parser.add_argument(
        "--max-checkers",
        metavar="N",
        type=int,
        default=1,
        help="Maximum number of concurrent checkers (default: 1)",
    )
    mode_group = parser.add_mutually_exclusive_group(required=True)
    mode_group.add_argument(
        "--client", action="store_true", help="Launch in CLIENT MODE"
    )
    mode_group.add_argument(
        "--server", action="store_true", help="Launch in SERVER MODE"
    )
    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    else:
        logger.setLevel(logging.INFO)

    logger.info("Starting IPsecDR Tool")

    mode = "client" if args.client else "server"

    config = IPsecDRConfigParser(args.config)

    test_list = ""
    if args.tests:
        test_list = args.tests
    elif args.test_list:
        with open(args.test_list, "r") as f:
            test_list = [line.strip() for line in f if line.strip()]
    else:
        logger.error("No tests specified. Use --tests or --test-list.")
        sys.exit(1)
    config = TestConfiguration(
        global_config=config.global_config,
        network_config=config.network,
        pki_config=config.pki,
        ipsec_config=config.ipsec,
    )
    if test_list != "":
        # Initialize the Orchestrator
        orchestrator = Orchestrator(
            config=config,
            test_list=test_list,
            max_testers=args.max_testers,
            max_checkers=args.max_checkers,
            mode=mode,
        )

        orchestrator.run()

    logger.info("IPsecDR Tool execution completed.")


if __name__ == "__main__":
    main()
