from rich.logging import RichHandler
import logging

rich_handler = RichHandler(
    rich_tracebacks=True,
    markup=True,
    show_time=True,
    show_level=True,
    show_path=True,
)

rich_formatter = logging.Formatter(
    "%(message)s",
    datefmt="[%Y-%m-%d %H:%M:%S]",
)
rich_handler.setFormatter(rich_formatter)

file_handler = logging.FileHandler("ipsec-dr.log")
file_formatter = logging.Formatter(
    "%(asctime)s [%(levelname)s] %(name)s - %(funcName)s: %(message)s",
    datefmt="[%Y-%m-%d %H:%M:%S]",
)
file_handler.setFormatter(file_formatter)

logging.basicConfig(
    level=logging.INFO,
    handlers=[rich_handler, file_handler],
)


logger = logging.getLogger("ipsecdr-logger")
