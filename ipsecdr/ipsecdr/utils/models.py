import os
import binascii
from typing import Any, Dict, List, Optional, Tuple, Union
from pydantic import (
    BaseModel,
    Field,
    IPvAnyAddress,
    field_validator,
    model_validator,
    TypeAdapter,
)
from ipsecdr.core.IKEv2.utils import get_notify_id, get_notify_name
from ipsecdr.core.IKEv2.constants import (
    get_transform_id,
    get_id_type_id,
    CIPHERS_TFM_ID,
    PRF_TFM_ID,
    INTEG_TFM_ID,
    DH_TFM_ID,
    ESN_TFM_ID,
)

from ipsecdr.utils.logger import logger

BoolAdapter = TypeAdapter(bool)
IntAdapter = TypeAdapter(int)


class BaseModelAssign(BaseModel):
    model_config = {
        "validate_assignment": True,
        "arbitrary_types_allowed": True,
    }


class GlobalConfig(BaseModelAssign):
    timeout: int = Field(15, description="Timeout in seconds")
    verbose: bool = Field(False, description="Enable verbose logging")
    logfile: str = Field("/tmp/ipsec.log", description="Path to the log file")
    tcpreplay: str = Field(
        "/usr/bin/tcpreplay", description="Path to tcpreplay binary"
    )
    pcap_path: str = Field("/tmp/pcap", description="Directory for pcap files")

    @field_validator("timeout", mode="before")
    def timeout_positive(cls, v):
        v = IntAdapter.validate_python(v)
        if v <= 0:
            raise ValueError("Timeout must be positive")
        return v

    @field_validator("verbose", mode="before")
    def validate_verbose(cls, v):
        return BoolAdapter.validate_python(v)


class NetworkConfig(BaseModelAssign):
    interface: str = Field("br-ext", description="Network interface")
    ipsec_src: IPvAnyAddress = Field(
        "***********", description="IPsec source IP"
    )
    ipsec_dst: IPvAnyAddress = Field(
        "********", description="IPsec destination IP"
    )
    ip_src: IPvAnyAddress = Field("***********", description="Source IP")
    ip_dst: IPvAnyAddress = Field("*********", description="Destination IP")

    port_src: int = Field(4500, description="Source port")
    port_dst: int = Field(4500, description="Destination port")

    @field_validator("port_src", "port_dst", mode="before")
    def port_in_range(cls, v):
        v = IntAdapter.validate_python(v)
        if not (0 <= v <= 65535):
            raise ValueError("Port must be between 0 and 65535")
        return v


class PKIConfig(BaseModelAssign):
    ca_root: str = Field("", description="Root CA certificate path")
    pub_key: str = Field("", description="Public key certificate path")
    prv_key: str = Field("", description="Private key path")
    trust_chain: List[str] = Field(
        default_factory=list, description="Trust chain certificates"
    )

    @field_validator("ca_root", "pub_key", "prv_key", mode="after")
    def file_must_exist(cls, v):
        if not os.path.isfile(v):
            raise ValueError(f"File does not exist: {v}")
        return v

    @field_validator("trust_chain", mode="before")
    def validate_trustchain(cls, v):
        if not isinstance(v, list):
            if not os.path.isfile(v):
                raise ValueError(f"File does not exist: {v}")
            return [v]
        return v


class IKESAConfig(BaseModelAssign):
    encr: List[Union[str, int]] = Field(
        [20], description="Encryption algorithm(s)"
    )  # Default to ENCR_AES_GCM_16
    encr_size: List[Union[str, int]] = Field(
        [256], description="Encryption key size(s)"
    )
    prf: List[Union[str, int]] = Field(
        [5], description="Pseudo-random function(s)"
    )  # Default to PRF_HMAC_SHA2_256
    integ: List[Union[str, int]] = Field(
        [12], description="Integrity algorithm(s)"
    )  # Default to AUTH_HMAC_SHA2_256_128
    groupdesc: List[Union[str, int]] = Field(
        [19], description="Diffie-Hellman group(s)"
    )  # Accepts a list of DH group names or IDs

    @field_validator("encr", "prf", "integ", "groupdesc", mode="before")
    def validate_transform_fields(cls, v, info):
        # Convert single value to list if needed
        if not isinstance(v, list):
            v = [v]
        return [get_transform_id(info.field_name, item) for item in v]

    @field_validator("encr_size", mode="before")
    def validate_sa_encr_size(cls, v):
        if isinstance(v, str):
            v = IntAdapter.validate_python(v)
        if not isinstance(v, list):
            v = [v]
        return v

    @staticmethod
    def possible_encr():
        return list(CIPHERS_TFM_ID.keys())

    @staticmethod
    def possible_encr_size(possible_encr_values=None):
        key_sizes = set()
        if possible_encr_values is None:
            possible_encr_values = IKESAConfig.possible_encr()
        encr_names = []
        for encr in possible_encr_values:
            if isinstance(encr, int):
                # Convert ID to name
                encr_name = None
                for name, value in CIPHERS_TFM_ID.items():
                    if value[0] == encr:
                        encr_name = name
                        break
                if encr_name is None:
                    continue  # Unknown encryption ID
            else:
                encr_name = encr
            encr_names.append(encr_name)
        for encr_name in encr_names:
            if encr_name in CIPHERS_TFM_ID:
                key_lengths = CIPHERS_TFM_ID[encr_name][4]
                logger.debug(
                    f"Encryption '{encr_name}': Key sizes {key_lengths}"
                )
                key_sizes.update(key_lengths)
        logger.debug(
            f"Possible key sizes for {possible_encr_values}: {sorted(key_sizes)}"
        )
        return sorted(key_sizes)

    @staticmethod
    def possible_prf():
        return list(PRF_TFM_ID.keys())

    @staticmethod
    def possible_integ():
        return list(INTEG_TFM_ID.keys())

    @staticmethod
    def possible_dh():
        return list(DH_TFM_ID.keys())


class AuthConfig(BaseModelAssign):
    auth_method: int = Field(
        9, description="Authentication method"
    )  # Default to ECDSA_SECP256R1_SHA256

    @field_validator("auth_method", mode="before")
    def validate_auth_method(cls, v):
        return get_transform_id("auth_method", v)


class ChildSAConfig(BaseModelAssign):
    encr: List[Union[str, int]] = Field(
        [20], description="Child SA encryption algorithm(s)"
    )  # Default to ENCR_AES_GCM_16
    encr_size: List[Union[str, int]] = Field(
        [256], description="Child SA encryption key size(s)"
    )
    integ: List[Union[str, int]] = Field(
        [12], description="Child SA integrity algorithm(s)"
    )  # Default to AUTH_HMAC_SHA2_256_128
    groupdesc: List[Union[str, int]] = Field(
        [19], description="Child SA Diffie-Hellman group(s)"
    )  # Accepts a list of DH group names or IDs
    esn: List[Union[str, int]] = Field(
        [1], description="Child SA extended sequence numbers"
    )  # Default to ESN

    @field_validator("encr", "integ", "groupdesc", mode="before")
    def validate_child_transform_fields(cls, v, info):
        # Convert single value to list if needed
        if not isinstance(v, list):
            v = [v]
        return [get_transform_id(info.field_name, item) for item in v]

    @field_validator("encr_size", mode="before")
    def validate_child_sa_encr_size(cls, v):
        if isinstance(v, str):
            v = IntAdapter.validate_python(v)
        if not isinstance(v, list):
            v = [v]
        return v

    @field_validator("esn", mode="before")
    def validate_child_sa_lists(cls, v):
        for k, val in ESN_TFM_ID.items():
            if v == k:
                v = val
                continue
            if v == val:
                continue
        if v not in ESN_TFM_ID.values():
            raise ValueError(f"{v} is not a valid ESN value")
        if not isinstance(v, list):
            v = [v]
        return v

    @staticmethod
    def possible_encr():
        return list(CIPHERS_TFM_ID.keys())

    @staticmethod
    def possible_encr_size(possible_encr_values=None):
        key_sizes = set()
        if possible_encr_values is None:
            possible_encr_values = IKESAConfig.possible_encr()
        encr_names = []
        for encr in possible_encr_values:
            if isinstance(encr, int):
                # Convert ID to name
                encr_name = None
                for name, value in CIPHERS_TFM_ID.items():
                    if value[0] == encr:
                        encr_name = name
                        break
                if encr_name is None:
                    continue  # Unknown encryption ID
            else:
                encr_name = encr
            encr_names.append(encr_name)
        for encr_name in encr_names:
            if encr_name in CIPHERS_TFM_ID:
                key_lengths = CIPHERS_TFM_ID[encr_name][4]
                logger.debug(
                    f"Encryption '{encr_name}': Key sizes {key_lengths}"
                )
                key_sizes.update(key_lengths)
        logger.debug(
            f"Possible key sizes for {possible_encr_values}: {sorted(key_sizes)}"
        )
        return sorted(key_sizes)

    @staticmethod
    def possible_prf():
        return list(PRF_TFM_ID.keys())

    @staticmethod
    def possible_integ():
        return list(INTEG_TFM_ID.keys())

    @staticmethod
    def possible_dh():
        return list(DH_TFM_ID.keys())

    @staticmethod
    def possible_esn():
        return list(ESN_TFM_ID.keys())


class TSConfig(BaseModelAssign):
    tsi_ip_range: Tuple[str, str] = Field(
        ("***********", "************"),
        description="Traffic Selector Initiator IP range",
        min_length=2,
    )
    tsi_port_range: Tuple[int, int] = Field(
        (0, 65535),
        description="Traffic Selector Initiator port range",
        min_length=2,
    )
    tsr_ip_range: Tuple[str, str] = Field(
        ("10.0.0.0", "**********"),
        description="Traffic Selector Responder IP range",
        min_length=2,
    )
    tsr_port_range: Tuple[int, int] = Field(
        (0, 65535),
        description="Traffic Selector Responder port range",
        min_length=2,
    )

    @field_validator("tsi_port_range", "tsr_port_range", mode="before")
    def validate_port_range(cls, v):
        # Accept input as either a tuple of strings or integers and convert to integers
        if isinstance(v, tuple) and len(v) == 2:
            return tuple(int(val) for val in v)
        # Accept input as string of the form port-port
        if isinstance(v, str) and "-" in v:
            return tuple(int(p) for p in v.split("-"))
        raise ValueError(
            "Port range must be a tuple with two values or a string of the form port-port"
        )

    @field_validator("tsi_ip_range", "tsr_ip_range", mode="before")
    def validate_ip_range(cls, v):
        if (
            isinstance(v, tuple)
            and isinstance(v[0], str)
            and isinstance(v[1], str)
        ):
            return v
        if isinstance(v, str) and "-" in v:
            return tuple(v.split("-"))
        raise ValueError(
            "IP range is either Tuple[str, str] or IPa-IPb, your value seems incorrect"
        )


class IDConfig(BaseModelAssign):
    idi_type: int = Field("ID_FQDN", description="Initiator ID type")
    idi_data: str = Field("debian-client.lan", description="Initiator ID data")
    idr_type: int = Field("ID_FQDN", description="Responder ID type")
    idr_data: str = Field("strongswan-gw.lan", description="Responder ID data")

    @field_validator("idi_type", "idr_type", mode="before")
    def validate_id_type_fields(cls, v, info):
        if isinstance(v, int):
            return v
        return get_id_type_id(v)


class NATConfig(BaseModelAssign):
    nat_t: bool = Field(False, description="NAT_T enabled ?")
    nat_port_src: int = Field(4500, description="Port value for NAT src")
    nat_port_dst: int = Field(4500, description="Port value for NAT dst")

    @field_validator("nat_t", mode="before")
    def validate_nat_t(cls, v):
        return BoolAdapter.validate_python(v)


class NotifyPayload(BaseModelAssign):
    type: Union[str, int] = Field(
        ...,
        description="Notify type, can be either string or integer representing a notify type",
    )
    notify: Optional[bytes] = Field(
        b"", description="Optional bytes data for the notify payload"
    )

    @field_validator("type")
    def validate_type_field(cls, v):
        if isinstance(v, int):
            try:
                _ = get_notify_name(v)
            except KeyError:
                raise ValueError(f"Unknown notify type with ID {v}")
        elif isinstance(v, str):
            try:
                _ = get_notify_id(v)
            except KeyError:
                raise ValueError(f"Unknown notify type with name {v}")
        else:
            raise TypeError("Type must be either a string or an integer")
        return v

    @field_validator("notify", mode="before")
    def validate_notify_field(cls, v):
        if v is None:
            return None
        if isinstance(v, str):
            try:
                return binascii.unhexlify(v)
            except ValueError:
                raise ValueError(
                    "Notify payload must be a valid hex-encoded string"
                )
        elif not isinstance(v, (bytes, bytearray)):
            raise TypeError(
                "Notify field must be bytes or a hex-encoded string"
            )
        return v

    def __str__(self):
        return self.pretty_print()

    def pretty_print(self) -> str:
        output = f"Notify {self.type}: {binascii.hexlify(self.notify)}"
        return output


class IPsecConfig(BaseModelAssign):
    ike_sa: IKESAConfig = IKESAConfig()
    auth: AuthConfig = AuthConfig()
    child_sa: ChildSAConfig = ChildSAConfig()
    ts: TSConfig = TSConfig()
    id: IDConfig = IDConfig()
    nat_t: NATConfig = NATConfig()
    childless: bool = Field(True, description="Childless SA")
    include_idr: bool = Field(False, description="include IDr")
    include_init_contact: bool = Field(False, description="Initial contact")
    notify_extras_init: Optional[List[NotifyPayload]] = Field(
        None, description="List of notify payloads to be sent during INIT"
    )
    notify_options_auth: Optional[List[NotifyPayload]] = Field(
        None,
        description="List of notify payloads to be sent before SA during AUTH",
    )
    notify_extras_auth: Optional[List[NotifyPayload]] = Field(
        None, description="List of notify payloads to be sent during AUTH"
    )
    notify_options_child: Optional[List[NotifyPayload]] = Field(
        None,
        description="List of notify payloads to be sent before SA during CREATE_CHILD_SA",
    )
    notify_extras_child: Optional[List[NotifyPayload]] = Field(
        None,
        description="List of notify payloads to be sent during CREATE_CHILD_SA",
    )

    @field_validator(
        "childless", "include_idr", "include_init_contact", mode="before"
    )
    def validate_boolean_flags(cls, v):
        return BoolAdapter.validate_python(v)


class ESPTests(BaseModelAssign):
    crypto: bool = Field(
        False,
        description="Test checks for invalid encrypted data, ICV and ciphered data",
    )
    replay: bool = Field(False, description="Test for replay handling")
    sp: bool = Field(False, description="Test for security policy")
    nat_t: bool = Field(False, description="Test for nat traversal, UDPENCAP")

    @field_validator("crypto", "replay", "sp", "nat_t", mode="before")
    def validate_tests(cls, v):
        return BoolAdapter.validate_python(v)


class ESPConfig(BaseModelAssign):
    tests: ESPTests = ESPTests()
    when: Union[int, bool] = Field(
        12,
        description="When ESP testing begins, use is to be matched against the SN",
    )



class Scenario(BaseModelAssign):
    exchanges: List[str] = Field(
        default=[],
        description="A list of exchanges in the scenario, such as INIT, AUTH, CREATE_CHILD_SA, etc.",
    )
    overlay_configs: List[Optional[Dict[str, Any]]] = Field(
        default=[],
        description="A list of overlay configurations (as dictionaries) for each exchange.",
    )
    check_functions: List[Optional[str]] = Field(
        default=[],
        description="A list of check functions corresponding to each exchange, which can be None or a function name.",
    )

    @model_validator(mode="before")
    def ensure_list_lengths(cls, values):
        if not isinstance(values, dict):
            logger.error("Values must be a dictionary.")
            raise TypeError("Values must be a dictionary.")

        exchanges = values.get("exchanges", [])
        overlay_configs = values.get("overlay_configs", [])
        check_functions = values.get("check_functions", [])

        logger.debug(f"Exchanges: {exchanges}")
        logger.debug(f"Overlay Configs: {overlay_configs}")
        logger.debug(f"Check Functions: {check_functions}")

        exchanges_len = len(exchanges)
        overlay_configs_len = len(overlay_configs)
        check_functions_len = len(check_functions)

        if not (exchanges_len == overlay_configs_len == check_functions_len):
            logger.error(
                "List lengths for exchanges, overlay_configs, and check_functions must be equal."
            )
            raise ValueError(
                "List lengths for exchanges, overlay_configs, and check_functions must be equal."
            )

        return values


class Test(BaseModelAssign):
    name: str = Field(..., description="Name of the test")
    initiator_scenarios: Dict[str, List[Scenario]] = Field(
        default_factory=dict,
        description="Dictionary of initiator scenario names mapped to a list of Scenario objects",
    )
    responder_scenarios: Dict[str, List[Scenario]] = Field(
        default_factory=dict,
        description="Dictionary of responder scenario names mapped to a list of Scenario objects",
    )


class TestPoolEntry(BaseModel):
    test_id: int = Field(..., description="Unique identifier for the test.")
    test_name: str = Field(..., description="Name of the test.")
    mode: str = Field(
        ...,
        description="Execution mode for the test ('initiator' or 'responder').",
    )
    lock_status: int = Field(
        0,
        description="Status of the test execution: 0=not started, 1=in progress, etc.",
    )
    outcome: Optional[Outcome] = Field(
        None, description="The outcome of the test execution."
    )
    scenario_name: str = Field(..., description="Name of the scenario.")
    scenario: Scenario = Field(..., description="Scenario for the test")
    scenario_count: int = Field(
        ..., description="Number of scenarios under this test."
    )
    packet_pipe: Optional[Any] = Field(
        None,
        exclude=True,
        description="Queue for packets, excluded from serialization.",
    )
    tester: Optional[Any] = Field(
        None,
        exclude=True,
        description="Tester instance, excluded from serialization.",
    )
    checker: Optional[Any] = Field(
        None,
        exclude=True,
        description="Checker instance, excluded from serialization.",
    )
