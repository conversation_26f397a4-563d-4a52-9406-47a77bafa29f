import configparser
from typing import Any, Dict, List, Optional
from pydantic import (
    BaseModel,
    parse_obj_as,
)
from ipsecdr.utils.models import (
    GlobalConfig,
    NetworkConfig,
    PKIConfig,
    IPsecConfig,
    IKESAConfig,
    AuthConfig,
    ChildSAConfig,
    TSConfig,
    IDConfig,
    NATConfig,
    ESPConfig,
    ESPTests,
)
from ipsecdr.utils.logger import logger


class IPsecDRConfigParser:
    def __init__(
        self, config_files: Any, cli_args: Optional[Dict[str, Any]] = None
    ) -> None:
        if isinstance(config_files, str):
            config_files = [config_files]
        self.config = configparser.ConfigParser()
        self.config.read(config_files)
        self.global_config = self._load_section("ARGS", GlobalConfig)
        self.network = self._load_section("IF", NetworkConfig)
        self.pki = self._load_section("CA", PKIConfig)
        self.ipsec = IPsecConfig(
            ike_sa=self._load_section("IKE_SA", IKESAConfig),
            auth=self._load_section("AUTH", AuthConfig),
            child_sa=self._load_section("CHILD_SA", ChildSAConfig),
            ts=self._load_section("TS", TSConfig),
            id=self._load_section("ID", IDConfig),
            nat_t=self._load_section("NAT_T", NATConfig),
        )
        self.esp = ESPConfig(tests=self._load_section("ESP", ESPTests))
        if cli_args:
            self.apply_cli_overrides(cli_args)

    def _load_section(
        self, section_name: str, config_class: BaseModel
    ) -> BaseModel:
        """Load a configuration section into a config object."""
        if self.config.has_section(section_name):
            section_data = dict(self.config.items(section_name))
            try:
                # Pass raw configuration data to the Pydantic model
                return config_class(**section_data)
            except Exception as e:
                logger.error(f"Error loading section [{section_name}]: {e}")
                raise
        else:
            # If section not in config file, return default instance
            return config_class()

    def _convert_value(self, value: Any, to_type: Any) -> Any:
        """Convert value to the specified type using Pydantic parsing."""
        try:
            # Use Pydantic's parse_obj_as for type conversion
            return parse_obj_as(to_type, value)
        except Exception as e:
            logger.error(f"Error converting value '{value}' to {to_type}: {e}")
            raise

    def apply_cli_overrides(self, cli_args: Dict[str, Any]) -> None:
        """Apply command-line argument overrides to the configurations."""
        for arg_key, arg_value in cli_args.items():
            if "." in arg_key:
                section, key = arg_key.split(".", 1)
                section_upper = section.upper()
                if section_upper == "ARGS":
                    config_object = self.global_config
                elif section_upper == "IF":
                    config_object = self.network
                elif section_upper == "CA":
                    config_object = self.pki
                elif section_upper == "IKE_SA":
                    config_object = self.ipsec.ike_sa
                elif section_upper == "AUTH":
                    config_object = self.ipsec.auth
                elif section_upper == "CHILD_SA":
                    config_object = self.ipsec.child_sa
                elif section_upper == "TS":
                    config_object = self.ipsec.ts
                elif section_upper == "ID":
                    config_object = self.ipsec.id
                elif section_upper == "NAT_T":
                    config_object = self.ipsec.nat_t
                else:
                    continue  # Unknown section

                if hasattr(config_object, key):
                    try:
                        setattr(config_object, key, arg_value)
                    except Exception as e:
                        logger.error(
                            f"Error applying CLI override for {arg_key}: {e}"
                        )
                        raise

    def overlay_config(self, overlay_files: List[str]) -> None:
        """Overlay additional configuration files on top of existing configurations."""
        overlay_config = configparser.ConfigParser()
        overlay_config.read(overlay_files)
        self._apply_overlay(overlay_config)

    def _apply_overlay(
        self, overlay_config: configparser.ConfigParser
    ) -> None:
        """Apply overlay configurations to the current configurations."""
        for section in overlay_config.sections():
            section_upper = section.upper()
            if section_upper == "ARGS":
                self._update_config_section(
                    overlay_config, section, self.global_config
                )
            elif section_upper == "IF":
                self._update_config_section(
                    overlay_config, section, self.network
                )
            elif section_upper == "CA":
                self._update_config_section(overlay_config, section, self.pki)
            elif section_upper == "IKE_SA":
                self._update_config_section(
                    overlay_config, section, self.ipsec.ike_sa
                )
            elif section_upper == "AUTH":
                self._update_config_section(
                    overlay_config, section, self.ipsec.auth
                )
            elif section_upper == "CHILD_SA":
                self._update_config_section(
                    overlay_config, section, self.ipsec.child_sa
                )
            elif section_upper == "TS":
                self._update_config_section(
                    overlay_config, section, self.ipsec.ts
                )
            elif section_upper == "ID":
                self._update_config_section(
                    overlay_config, section, self.ipsec.id
                )
            elif section_upper == "NAT_T":
                self._update_config_section(
                    overlay_config, section, self.ipsec.id
                )

    def _update_config_section(
        self,
        overlay_config: configparser.ConfigParser,
        section: str,
        config_object: BaseModel,
    ) -> None:
        """Update a config object with values from the overlay config."""
        section_data = dict(overlay_config.items(section))
        for key, value in section_data.items():
            if hasattr(config_object, key):
                try:
                    setattr(config_object, key, value)
                except Exception as e:
                    logger.error(
                        f"Error updating config for {section}.{key}: {e}"
                    )
                    raise
