import ipsecdr.core.crypto.ecsdsa as ecsdsa
from ipsecdr.core.IKEv2.constants import (
    DH_TFM_ID,
    INTEG_TFM_ID,
    PRF_TFM_ID,
    CIPHERS_TFM_ID,
    get_cipher_tfm_name,
)
from scapy.layers.ipsec import ESP, SecurityAssociation
from cryptography.hazmat.primitives.ciphers import (
    Cipher as _Cipher,
    algorithms,
    modes,
)
from cryptography.hazmat.primitives.asymmetric.utils import (
    decode_dss_signature,
)
from cryptography.hazmat.primitives.asymmetric import padding, ec
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives import hashes
from cryptography import x509
import cryptography.hazmat.backends.openssl.backend
import binascii
import hashlib
import secrets
from hmac import HMAC
from re import search
from ipsecdr.utils.logger import logger

logger = logger


# Classe pour l'exchange du secret partager
class ECDH:
    backend = cryptography.hazmat.backends.openssl.backend

    def __repr__(self):
        attr_dict = {
            attr: getattr(self, attr)
            for attr in dir(self)
            if not callable(getattr(self, attr)) and not attr.startswith("__")
        }
        return str(attr_dict)

    def __init__(self, algo: int):
        # appel de la fonction pour definir l'algoritme choisit
        self.choix_algo(algo)

        # generation de la cle privee
        if not hasattr(self, "algorithme"):
            logger.error("Continue withouth private key cannot init ECDH")
            return
        self.private_key = ec.generate_private_key(
            self.algorithme, backend=self.backend
        )
        # KAD # Je pense qu'il a mis "+7" à cause de ec.SECP521R1() ?
        # KAD # Et comme on a une division entière, le résultat est tjr correct par chance...
        # KAD # self.key_len = (self._private_key.key_size + 7) // 8
        self.key_len = self.private_key.key_size // 8

        # Le secret partage
        self.shared_secret = b""

        # recuperation d'un nombre public à partir de la cle privee
        public_numbers = self.private_key.public_key().public_numbers()

        # Creation de la cle public avec le nombre public la taille de la cle en "big indien"
        self.public_key = public_numbers.x.to_bytes(
            self.key_len, "big"
        ) + public_numbers.y.to_bytes(self.key_len, "big")

    # Fonction creer le secret partage
    def compute_secret(self, peer_public_key: bytes):
        # récuperation de la cle public de l'autre en big indien
        x = int.from_bytes(peer_public_key[: self.key_len], "big")
        # fmt: off
        y = int.from_bytes(peer_public_key[self.key_len:], "big")
        # fmt: on
        # creation d'un nombre public à partir de la cle de l'autre
        peer_public_numbers = ec.EllipticCurvePublicNumbers(
            x, y, self.algorithme
        )

        # creatio, d'une clé public de l'autre
        ecc_peer_public_key = peer_public_numbers.public_key(self.backend)

        # Création du secret partage
        self.shared_secret = self.private_key.exchange(
            ec.ECDH(), ecc_peer_public_key
        )

    # Fonction mettre en place l'algorithme selectionner
    def choix_algo(self, algo: int):
        if algo == DH_TFM_ID["256randECPgr"]:
            self.algorithme = ec.SECP256R1()

        elif algo == DH_TFM_ID["384randECPgr"]:
            self.algorithme = ec.SECP384R1()

        # KAD # elif algo == DH_TFM_ID['521randECPgr']:
        # KAD #     self.algorithme = ec.SECP521R1()

        elif algo == DH_TFM_ID["brainpoolP256r1"]:
            self.algorithme = ec.BrainpoolP256R1()

        elif algo == DH_TFM_ID["brainpoolP384r1"]:
            self.algorithme = ec.BrainpoolP384R1()

        elif algo == DH_TFM_ID["brainpoolP512r1"]:
            self.algorithme = ec.BrainpoolP512R1()

        else:
            logger.error(
                "ERROR: Wrong DH algorithm, DH accepted: randECPgr and brainpool\
                \nContinue withouth setting class algo"
            )


class Integrity:
    def __repr__(self):
        attr_dict = {
            attr: getattr(self, attr)
            for attr in dir(self)
            if not callable(getattr(self, attr)) and not attr.startswith("__")
        }
        return str(attr_dict)

    def __init__(self, algo: int):
        # appel de la fonction pour definir l'algoritme choisit
        self.choix_algo(algo)

    @property
    def key_size(self) -> int:
        # retourne la taille de la cle
        return self.hasher().digest_size

    @property
    def hash_size(self) -> int:
        #  retourne le taille du hash
        return self.keybits // 8

    def compute(self, key: bytes, data: bytes) -> bytes:
        # Fait l'integrité (le checksum) d'une donnee en fonction d'une clé (SK_ai ou SK_ar)
        m = HMAC(key, data, digestmod=self.hasher)

        # retour du résultat avec la taille precise
        return m.digest()[: self.hash_size]

    # Fonction mettre en place l'algorithme selectionner
    def choix_algo(self, algo: int):
        if algo == INTEG_TFM_ID["AUTH_HMAC_SHA2_256_128"]:
            self.hasher = hashlib.sha256
            self.keybits = 128

        elif algo == INTEG_TFM_ID["AUTH_HMAC_SHA2_384_192"]:
            self.hasher = hashlib.sha384
            self.keybits = 192

        elif algo == INTEG_TFM_ID["AUTH_HMAC_SHA2_512_256"]:
            self.hasher = hashlib.sha512
            self.keybits = 256

        else:
            exit(
                "ERROR: Wrong integrity algorithm, integrity accepted: AUTH_HMAC_SHA2_256_128, AUTH_HMAC_SHA2_384_192 and AUTH_HMAC_SHA2_512_256"
            )


class Prf:
    def __repr__(self):
        attr_dict = {
            attr: getattr(self, attr)
            for attr in dir(self)
            if not callable(getattr(self, attr)) and not attr.startswith("__")
        }
        return str(attr_dict)

    def __init__(self, algo: int):
        # appel de la fonction pour definir l'algoritme choisit
        self.choix_algo(algo)

    @property
    def key_size(self) -> int:
        # retoune la taille de la cle
        return self.hash_size

    @property
    def hash_size(self) -> int:
        # retourne la taille du hash
        return self.hasher().digest_size

    # Fonction pour realiser le pfr
    def prf(self, key: bytes, data: bytes) -> bytes:
        m = HMAC(key, data, digestmod=self.hasher)
        return m.digest()

    # Fonction qui réalise le prf + pour qui derive le Skeyssed en plusieur clé
    def prfplus(self, skeyseed: bytes, nonce_spi: bytes, size: int) -> bytes:
        # donnee en bytes
        result = bytes()
        temp = bytes()

        # compteur
        i = 1

        # Boucle tant que la taille du result n'est pas superieur ou egale a la taille necessaire
        while len(result) < size:
            # calcul du prf
            temp = self.prf(skeyseed, temp + nonce_spi + i.to_bytes(1, "big"))

            # ajout du calcul au resultat final
            result += temp

            # incrementation du compteur
            i += 1

        # retourne le resultat a la taille souhaite
        return result[:size]

    # Fonction mettre en place l'algorithme selectionner
    def choix_algo(self, algo: int):
        if algo == PRF_TFM_ID["PRF_HMAC_SHA2_256"]:
            self.hasher = hashlib.sha256

        elif algo == PRF_TFM_ID["PRF_HMAC_SHA2_384"]:
            self.hasher = hashlib.sha384

        elif algo == PRF_TFM_ID["PRF_HMAC_SHA2_512"]:
            self.hasher = hashlib.sha512

        else:
            exit(
                "ERROR: Wrong prf algorithm, prf accepted: PRF_HMAC_SHA2_256, PRF_HMAC_SHA2_384 or PRF_HMAC_SHA2_512"
            )


class Chiffrement:
    _backend = cryptography.hazmat.backends.openssl.backend

    def __repr__(self):
        attr_dict = {
            attr: getattr(self, attr)
            for attr in dir(self)
            if not callable(getattr(self, attr)) and not attr.startswith("__")
        }
        return str(attr_dict)

    def __init__(self, algo: list):
        # appel de la fonction pour definir l'algoritme choisit
        self.choix_algo(algo)

    @property
    def block_size(self) -> int:
        # reourne la taille du block (pour AES toujours 16 octect)
        return self._algorithm.block_size // 8

    @property
    def key_size(self) -> int:
        # retourne la taille de la cle en fonction de l'algorithme
        if self.AES == "CBC":
            return self.key // 8

        elif self.AES == "CTR":
            # ajout de 4 octect a cause du nonce
            return (self.key // 8) + 4

        elif self.AES == "GCM":
            # Ajout de 4 octect a cause du salt(nonce)
            return (self.key // 8) + 4

        else:
            exit(
                "ERROR: Wrong cipher algorithm, cipher accepted: AES-CBC,AES-CTR and AEC-GCM"
            )

    # Fonction qui chiffre la donnee souhaitee en fonction de l'algorithme
    def encrypt(
        self,
        key: bytes,
        iv: bytes,
        data: bytes,
        payload_for_GCM=b"",
    ) -> bytes:
        if self.AES == "CBC":
            # Verification de la taille de la cle de chiffrement
            if len(key) != self.key_size:
                raise Exception(
                    "Key must be of the indicated size {}".format(
                        self.key_size
                    )
                )

            # Preparation au chiffrement
            _cipher = _Cipher(
                self._algorithm(key), modes.CBC(iv), backend=self._backend
            )
            encryptor = _cipher.encryptor()

        elif self.AES == "CTR":
            # Verification de la taille de la cle de chiffrement

            if len(key) != self.key_size:
                raise Exception(
                    "Key must be of the indicated size {}".format(
                        self.key_size
                    )
                )

            # Recuperation du nonce qui sont les 4 dernier octect de la cle
            # fmt: off
            nonce = key[(len(key) - 4):]
            # fmt: on
            # Retire le nonce de la cle
            key = key[: (len(key) - 4)]
            # Recuperation de l'iv
            iv = iv
            # Ajout d'un compteur pour le CTR qui commence à 1
            compteur = binascii.unhexlify("00000001")

            # Creation du block compter pour le CTR
            block_compter_ctr = nonce + iv + compteur

            # Preparation au chiffrement
            _cipher = _Cipher(
                self._algorithm(key),
                modes.CTR(block_compter_ctr),
                backend=self._backend,
            )
            encryptor = _cipher.encryptor()

        elif self.AES == "GCM":
            # Verification de la taille de la cle de chiffrement
            if len(key) != (self.key_size):
                raise Exception(
                    "Key must be of the indicated size {}".format(
                        self.key_size
                    )
                )

            # Recuperation du salt(nonce) qui sont les 4 dernier octect de la cle
            # fmt: off
            nonce = key[(len(key) - 4):]
            # fmt: on
            # Retire le salt(nonce) de la cle
            key = key[: (len(key) - 4)]
            # Recuperation de l'iv
            iv = iv
            # Creation du secret pour le chiffrement
            secret = nonce + iv

            # Preparation au chiffrement
            _cipher = _Cipher(
                self._algorithm(key),
                modes.GCM(secret, None, self.ICV_size),
                backend=self._backend,
            )
            encryptor = _cipher.encryptor()

        else:
            exit(
                "ERROR: Wrong cipher algorithm, cipher accepted: AES-CBC,AES-CTR and AEC-GCM"
            )

        # Chiffrement et retour de la donnee
        return encryptor.update(data) + encryptor.finalize()

    # Fonction qui retourne L'ICV (checksum) pour AES GCM
    def IntegrityTag(
        self, AAD: bytes, key: bytes, iv: bytes, data: bytes
    ) -> bytes:
        # recuperation du nonce
        # fmt: off
        nonce = key[(len(key) - 4):]
        # fmt: on
        # recuperation de la clee  (en retirant le nonce 4 dernier octect)
        key = key[: (len(key) - 4)]
        # recuperation de l'iv
        iv = iv

        # creation du secret
        secret = nonce + iv

        # Preparation au chiffrement
        _cipher = _Cipher(
            self._algorithm(key),
            modes.GCM(secret, None, self.ICV_size),
            backend=self._backend,
        )
        encryptor = _cipher.encryptor()

        # Ajout de la donne additionnel pour authentification
        encryptor.authenticate_additional_data(AAD)
        encryptor.update(data) + encryptor.finalize()

        # recuperation de l'ICV en fonction de sa taille
        return encryptor.tag[: self.ICV_size]

    # Fonction qui dechiffre la donnee souhaitee
    def decrypt(self, key: bytes, iv: bytes, data: bytes) -> bytes:
        if self.AES == "CBC":
            # Verification de la taille de la cle de dechiffrement
            if len(key) != self.key_size:
                raise Exception(
                    "Key must be of the indicated size {}".format(
                        self.key_size
                    )
                )

            # Preparation au dechiffrement
            _cipher = _Cipher(
                self._algorithm(key), modes.CBC(iv), backend=self._backend
            )
            decryptor = _cipher.decryptor()

        if self.AES == "CTR":
            # Verification de la taille de la cle de dechiffrement
            if len(key) != self.key_size:
                raise Exception(
                    "Key must be of the indicated size {}".format(
                        self.key_size
                    )
                )

            # Recuperation du salt(nonce) qui sont les 4 dernier octect de la cle
            # fmt: off
            nonce = key[(len(key) - 4):]
            # fmt: on
            # Retire le salt(nonce) de la cle
            key = key[: (len(key) - 4)]
            # Recuperation de l'iv
            iv = iv

            # Ajout d'un compteur pour le CTR qui commence à 1
            compteur = binascii.unhexlify("00000001")
            # Creation du block compter pour le CTR
            block_compter_ctr = nonce + iv + compteur

            # Preparation au chiffrement
            _cipher = _Cipher(
                self._algorithm(key),
                modes.CTR(block_compter_ctr),
                backend=self._backend,
            )
            decryptor = _cipher.decryptor()

        # retourne la donne dechiffrer
        return decryptor.update(data) + decryptor.finalize()

    # Dechiffrement de L'AES GCM
    def DecryptAndCheck(
        self,
        AAD: bytes,
        key: bytes,
        iv: bytes,
        data_encrypted: bytes,
        ICV: bytes,
    ) -> bytes:
        # Verification de la taille de la cle de chiffrement
        if len(key) != self.key_size:
            raise Exception(
                f"Key must be of the indicated size {self.key_size}"
            )

        # Recuperation du salt(nonce) qui sont les 4 dernier octect de la cle
        nonce = key[-4:]
        # Retire le salt(nonce) de la cle
        key = key[:-4]
        # Recuperation de l'iv
        iv = iv
        # Creation du secret pour le chiffrement
        secret = nonce + iv

        # Preparation au dechiffrement
        _cipher = _Cipher(
            self._algorithm(key),
            modes.GCM(secret, ICV, self.ICV_size),
            backend=self._backend,
        )
        decryptor = _cipher.decryptor()

        # Ajout de la donne additionnel pour authentification
        decryptor.authenticate_additional_data(AAD)

        # Dechiffrement et retour de la donnee
        return decryptor.update(data_encrypted) + decryptor.finalize()

    def generate_iv(self) -> bytes:
        if self.AES in ["CBC", "CTR", "GCM"]:
            return secrets.token_bytes(self.iv_size)
        else:
            exit(
                "ERROR: Wrong cipher algorithm, cipher accepted: AES-CBC,AES-CTR and AEC-GCM"
            )

    # fonction qui realise le padding d'une donne si necessaire
    def data_padding(self, data: bytes, next_header: bytes = b"") -> bytes:
        # seuelemnt pour les paquet ESP qui doivent contenir un next header
        if len(next_header) > 0:
            padding_data = b""
            data_len = len(data) + 2
            padding_len = (4 - (data_len % 4)) % 4

            for i in range(1, padding_len + 1):
                padding_data += i.to_bytes(1, byteorder="big")

            data = (
                data
                + padding_data
                + padding_len.to_bytes(1, byteorder="big")
                + next_header
            )

        # POur les paquets non ESP
        else:
            if self.AES == "CBC":
                # multiple taille padding
                padding = self.block_size
                # taille actuel de la donne
                length_data = len(data)

                if (
                    length_data % padding
                ) == 0:  # si la donne atteint deja un multiple du padding
                    # la taille du padding correspond au padding  1 (car la taille du padding doit être dans la donnee)
                    lenght_pad = padding - 1

                else:
                    # la taille du padding correspond a la difference entre le padding-1 et les donnees manquantes
                    lenght_pad = (padding - 1) - (length_data % padding)

                # preparation du padding en fonction de la longueur du padding
                pad = lenght_pad * bytes.fromhex("00")

                # ajout du padding et de la taille du padding à la donnee
                data = data + pad + lenght_pad.to_bytes(1, byteorder="big")

                # verification si la taille de la donne est ujn multiple de 4 ou non
                if (len(data) % 4) != 0:
                    exit("ERROR: The padding is not a multiple of 4.")
            else:
                # AES-CTR et AES-GCM n'ont pas besoin de padding donc la taille du padding est de 0
                data = data + binascii.unhexlify("00")

        # retourne la donnee avec padding
        return data

    # Fonction mettre en place l'algorithme selectionner
    def choix_algo(self, algo: list):
        # n'accepte que AES
        self._algorithm = algorithms.AES
        # Cle possible
        key_size = [128, 192, 256]

        if algo[1] in key_size:
            self.key = algo[1]
        else:
            exit("ERROR: size accepted for key_size: 128, 192 and 256.")

        if algo[0] == "AES-CBC" or algo[0] == 12:
            self.iv_size = 16
            self.AES = "CBC"

        elif algo[0] == "AES-CTR" or algo[0] == 13:
            self.iv_size = 8
            self.AES = "CTR"

        elif (
            search("AES-GCM", str(algo[0]))
            or algo[0] == 18
            or algo[0] == 19
            or algo[0] == 20
        ):
            self.iv_size = 8
            self.AES = "GCM"

            if search("8ICV", str(algo[0])) or algo[0] == 18:
                self.ICV_size = 8

            elif search("12ICV", str(algo[0])) or algo[0] == 19:
                self.ICV_size = 12

            elif search("16ICV", str(algo[0])) or algo[0] == 20:
                self.ICV_size = 16

            else:
                exit("ERROR: Wrong ICV size, ICV accepted : 8, 12 and 16")
        else:
            exit(
                "ERROR: Wrong cipher algorithm, cipher accepted : AES-CBC, AES-CTR and AES-GCM"
            )


# Classe qui contient les differents algorithme d'IKEV2 ###
class IKEv2Algorithm:
    def __repr__(self):
        attr_dict = {
            attr: getattr(self, attr)
            for attr in dir(self)
            if not callable(getattr(self, attr)) and not attr.startswith("__")
        }
        return str(attr_dict)

    def __init__(self, choix_dh: int):
        # KAD # # scapy ne connait pas ecpbp alors il faut utiliser leurs ID pour les utiliser
        # KAD # if (choix_dh == 'ecp256bp'):
        # KAD #     self.choix_dh = 28
        # KAD # elif (choix_dh == 'ecp384bp'):
        # KAD #     self.choix_dh = 29
        # KAD # elif (choix_dh == 'ecp512bp'):
        # KAD #     self.choix_dh = 30
        # KAD # else:
        # KAD #     self.choix_dh = choix_dh  # le nom de l'algorithme pour l'echange de cle
        logger.debug("IKEv2Algorithm INITIALISATION")
        self.choix_dh = choix_dh
        self.dh = ECDH(self.choix_dh)  # Creation de l'objet

        self.choix_integrite = 0
        self.integrite = None

        self.choix_prf = 0
        self.prf = None

        self.choix_chiffrement = 0
        self.key_chiffrement = 0
        self.chiffrement = None

    # Fonction qui cree les 3 autres alogrithme pour IKEv2
    def set_other_algo(
        self,
        choix_chiffrement: int,
        key_size: int,
        choix_integrite: int,
        choix_prf: int,
        child: bool = False,
        esn: int = 0,
    ):
        # Creation de l'integrite si necessaire
        self.choix_integrite = (
            choix_integrite  # le nom de l'algorithme pour l'integrite
        )

        cipher_name = get_cipher_tfm_name(choix_chiffrement)
        is_aead = CIPHERS_TFM_ID[cipher_name][1]
        if not is_aead:
            self.integrite = Integrity(choix_integrite)  # Creation de l'objet

        # Pour le child pas de prf mais ESN

        # creation du prf
        self.choix_prf = choix_prf  # le nom de l'algorithme pour le prf
        self.prf = Prf(choix_prf)  # Creation de l'objet

        # Creation du cipher (chiffrement)
        self.choix_chiffrement = (
            choix_chiffrement  # le nom de l'algorithme pour le chiffrement
        )
        self.key_chiffrement = key_size

        chiffrement_cle = [choix_chiffrement, key_size]
        self.chiffrement = Chiffrement(chiffrement_cle)  # Creation de l'objet


# Classe qui contient les cles et certificats d'IKEV2 ###
class IKEv2KeyData:
    def __repr__(self):
        attr_dict = {
            attr: getattr(self, attr)
            for attr in dir(self)
            if not callable(getattr(self, attr)) and not attr.startswith("__")
        }
        return str(attr_dict)

    # Objet creer avec le secret partage
    def __init__(self, secret: bytes):
        self.secret = secret

        self.skeyseed = b""
        self.keymat = b""
        self.sk_ai = b""
        self.sk_ar = b""
        self.sk_ei = b""
        self.sk_er = b""
        self.sk_pi = b""
        self.sk_pr = b""
        self.sk_d = b""

        self.certificate_authority = None
        self.public_certificate = None
        self.public_key = None
        self.certificat_trusted_chain = []

        self.private_key = None
        self.algo_key = ""

        self.Signature_algo = "SHA2-256"

        # Signature avec la cle privee

    # Formerly named SignOctect
    def sign(self, octet: bytes, mode: int = 14) -> bytes:
        if (mode == 9 or mode == 214) and self.algo_key == "EC":
            signature = self.private_key.sign(octet, ec.ECDSA(hashes.SHA256()))
            r, s = decode_dss_signature(signature)
            signature = ((r << 256) | s).to_bytes(64, byteorder="big")
        elif mode == 225 and self.algo_key == "EC":
            # ECSDSA sur secp256r1 avec SHA256
            prv_number = self.private_key.private_numbers()
            r, s = ecsdsa.sign_message(
                ecsdsa.secp256r1, prv_number.private_value, octet
            )
            signature = r + s
        elif mode == 228 and self.algo_key == "EC":
            # ECSDSA sur BrainpoolP256r1 avec SHA256
            prv_number = self.private_key.private_numbers()
            r, s = ecsdsa.sign_message(
                ecsdsa.brainpoolP256r1, prv_number.private_value, octet
            )
            signature = r + s
        elif mode == 14 and (self.algo_key == "EC" or self.algo_key == "RSA"):
            # Utiliser uniquement pour un contre test
            if self.Signature_algo == "SHA2-256":
                if self.algo_key == "EC":
                    signature = self.private_key.sign(
                        octet, ec.ECDSA(hashes.SHA256())
                    )
                elif self.algo_key == "RSA":
                    signature = self.private_key.sign(
                        octet, padding.PKCS1v15(), hashes.SHA256()
                    )
            elif self.Signature_algo == "SHA2-384":
                if self.algo_key == "EC":
                    signature = self.private_key.sign(
                        octet, ec.ECDSA(hashes.SHA384())
                    )
                elif self.algo_key == "RSA":
                    signature = self.private_key.sign(
                        octet, padding.PKCS1v15(), hashes.SHA384()
                    )
            elif self.Signature_algo == "SHA2-512":
                if self.algo_key == "EC":
                    signature = self.private_key.sign(
                        octet, ec.ECDSA(hashes.SHA512())
                    )
                elif self.algo_key == "RSA":
                    signature = self.private_key.sign(
                        octet, padding.PKCS1v15(), hashes.SHA512()
                    )
            else:
                exit(f"ERROR: Bad hash ({self.Signature_algo})")
        else:
            exit(f"ERROR: Bad mod ({mode}) or algo ({self.algo_key})")

        return signature

    #  SETTER #

    # Definition du/des cerificat client (en bytes)
    def setcertificatauthority(self, ca: bytes):
        self.certificate_authority = ca

    # Definition de la cle public avec le certificat
    def SetPublicKeyCertificat(self, path: str):
        # ourverture du certificat
        with open(path, mode="rb") as public_file:
            # recuperation des donnes en lecture
            key_data_public = public_file.read()
            # recuperation du certificat
            cert = x509.load_pem_x509_certificate(
                key_data_public,
                backend=cryptography.hazmat.backends.openssl.backend,
            )
            # recuperation de la cle public
            loaded_public_key = cert.public_key()
        # le certificat
        self.public_certificate = cert
        # la cle public
        self.public_key = loaded_public_key

        # chaine de confiance
        # KAD : On va le rajouter après avec AddTrustedChain #
        # self.certificat_trusted_chain = [(self.public_certificate.public_bytes(serialization.Encoding.DER))]

    # focntion pour ajouter la chaine de confianc es'il y en a
    def AddTrustedChain(self, list_path_cert: list):
        # parcours de la lsite des cerrificat
        for path_cert in list_path_cert:
            # verification des chemin
            try:
                # Lecture et recuepration des donnees
                with open(path_cert, mode="rb") as public_file:
                    key_data_public = public_file.read()
                    cert = x509.load_pem_x509_certificate(
                        key_data_public,
                        backend=cryptography.hazmat.backends.openssl.backend,
                    )
                # ajout du certificat dans la liste
                self.certificat_trusted_chain.append(
                    cert.public_bytes(serialization.Encoding.DER)
                )
            except IOError:
                exit(
                    f'ERROR: Le chemin du certificat "{
                        path_cert}" ne semble pas correct.'
                )

    # Definition de la cle privee avec le certificat
    def SetPrivateKeyCertificat(self, path: str):
        # ourverture du certificat
        with open(path, mode="rb") as private_file:
            key_data = private_file.read()
            # recuperarion de la cle privee
            loaded_private_key = serialization.load_pem_private_key(
                key_data,
                password=None,
                backend=cryptography.hazmat.backends.openssl.backend,
            )
        # ajout comme attribute de class
        self.private_key = loaded_private_key

        # Verification si la clee est RSA ou ECDSA
        if isinstance(
            self.private_key,
            cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey,
        ):
            self.algo_key = "EC"
        elif isinstance(
            self.private_key,
            cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey,
        ):
            self.algo_key = "RSA"
        else:
            exit("ERROR : BAD KEY???")

    # Definition de la skeyseed

    def setkeyseed(self, skeyseed: bytes):
        self.skeyseed = skeyseed

    # definition de l'algorithme de signature pour l'authentification
    def SetSignatureAlgo(self, algo: bytes):
        if algo == b"0002":
            self.Signature_algo = "SHA2-256"
        elif algo == b"0003":
            self.Signature_algo = "SHA2-384"
        elif algo == b"0004":
            self.Signature_algo = "SHA2-512"
        else:
            exit(f"ERROR: Signature_algo unknown: {algo}")

    # Definition des differentes clees
    def setkey(
        self,
        keymat: bytes,
        len_keys_cipher: int,
        len_keys_integrity: int,
        len_keys_prf: int,
    ):
        # mise en place du keymat
        self.keymat = keymat

        # variable temporaire qui contient le keymat
        # le Keymat contient toutes les clees il faut donc couper les donnes pour recuperer les bonnes clees
        # l'ordre des clees dans le keymat : sk_d, sk_ai,sk_ar,sk_ei,sk_er,sk_pi,sk_pr
        key_table = keymat

        # recuperation du sk_d
        self.sk_d = key_table[:len_keys_prf]
        # Retire le sk_d de la variable
        key_table = key_table[len_keys_prf:]

        # si la taille de la clee pour l'integrite n'est pas egale à 0 (pas d'integrite
        if len_keys_integrity != 0:
            # recuperation du sk_ai
            self.sk_ai = key_table[:len_keys_integrity]
            # Retire le sk_ai de la variable
            key_table = key_table[len_keys_integrity:]

            # recuperation du sk_ar
            self.sk_ar = key_table[:len_keys_integrity]
            # Retire le sk_ar de la variable
            key_table = key_table[len_keys_integrity:]

        else:
            # Si pas besoin d'integrite mise a 0 des clees
            self.sk_ai = binascii.unhexlify("00")
            self.sk_ar = binascii.unhexlify("00")

        # recuperation du sk_ei
        self.sk_ei = key_table[:len_keys_cipher]
        # Retire le sk_ei de la variable
        key_table = key_table[len_keys_cipher:]

        # recuperation du sk_er
        self.sk_er = key_table[:len_keys_cipher]
        # Retire le sk_er de la variable
        key_table = key_table[len_keys_cipher:]

        # recuperation du sk_pi
        self.sk_pi = key_table[:len_keys_prf]
        # Retire le sk_pi de la variable
        key_table = key_table[len_keys_prf:]

        # recuperation du sk_pr
        self.sk_pr = key_table[:len_keys_prf]
        # Retire le sk_pr de la variable
        key_table = key_table[len_keys_prf:]

        # si il reste des donnees dans le key_table
        if len(key_table) != 0:
            exit(
                "ERROR: La taille de la clé KEYMAT est plus grand que la sommes des clés."
            )

    # recuperation du DN depuis le certificat
    def GetCertificateDN(self) -> bytes:
        return self.public_certificate.subject.public_bytes(
            backend=cryptography.hazmat.backends.openssl.backend
        )

    # recuperation de l'oid en bytes
    def GetSignatureOID(self) -> bytes:
        if self.Signature_algo == "SHA2-256":
            if self.algo_key == "EC":
                oid = binascii.unhexlify("300a06082a8648ce3d040302")
            elif self.algo_key == "RSA":
                oid = binascii.unhexlify("300d06092a864886f70d01010b0500")
        elif self.Signature_algo == "SHA2-384":
            if self.algo_key == "EC":
                oid = binascii.unhexlify("300a06082a8648ce3d040303")
            elif self.algo_key == "RSA":
                oid = binascii.unhexlify("300d06092a864886f70d01010c0500")
        elif self.Signature_algo == "SHA2-512":
            if self.algo_key == "EC":
                oid = binascii.unhexlify("300a06082a8648ce3d040304")
            elif self.algo_key == "RSA":
                oid = binascii.unhexlify("300d06092a864886f70d01010d0500")

        return oid

    # Methode pour afficher les cles
    def show_key(self):
        logger.info(
            f"[dark_orange]SKEYSEED : {binascii.hexlify(self.skeyseed)}[/dark_orange]"
        )
        logger.info(
            f"[dark_orange]SK_d     : {binascii.hexlify(self.sk_d)}[/dark_orange]"
        )
        logger.info(
            f"[dark_orange]SK_pi    : {binascii.hexlify(self.sk_pi)}[/dark_orange]"
        )
        logger.info(
            f"[dark_orange]SK_pr    : {binascii.hexlify(self.sk_pr)}[/dark_orange]"
        )
        logger.info(
            f"[dark_orange]SK_ei    : {binascii.hexlify(self.sk_ei)}[/dark_orange]"
        )
        logger.info(
            f"[dark_orange]SK_er    : {binascii.hexlify(self.sk_er)}[/dark_orange]"
        )
        logger.info(
            f"[dark_orange]SK_ai    : {binascii.hexlify(self.sk_ai)}[/dark_orange]"
        )
        logger.info(
            f"[dark_orange]SK_ar    : {binascii.hexlify(self.sk_ar)}[/dark_orange]"
        )


# Classe qui contient les differents algorithme d'IKEV2 ###
class ChildAlgorithm:
    def __repr__(self):
        attr_dict = {
            attr: getattr(self, attr)
            for attr in dir(self)
            if not callable(getattr(self, attr)) and not attr.startswith("__")
        }
        return str(attr_dict)

    def __init__(self):
        # valeurs par defaut
        self.choix_dh = 0
        self.dh = None

        self.choix_integrite = 0
        self.integrite = None

        self.esn = 0

        self.choix_chiffrement = 0
        self.key_chiffrement = 0
        self.chiffrement = None

    def Set_dh(self, choix_dh: int):
        self.choix_dh = choix_dh  # l'agorithme pour le groupdesc
        self.dh = ECDH(choix_dh)  # Creation de l'objet

    # Fonction qui cree les 3 autres alogrithme pour IKEv2
    def set_other_algo(
        self,
        choix_chiffrement: int,
        key_size: int,
        choix_integrite: int,
        esn: int = 0,
    ):
        # Creation de l'integrite si necessaire
        self.choix_integrite = choix_integrite  # l'algorithme pour l'integrite

        # Si AES GCM pas d'integrite
        cipher_name = get_cipher_tfm_name(choix_chiffrement)
        is_aead = CIPHERS_TFM_ID[cipher_name][1]
        if not is_aead:
            self.integrite = Integrity(choix_integrite)  # Creation de l'objet

        # Pour le child pas de prf mais ESN
        self.esn = esn

        # Creation du cipher (chiffrement)
        self.choix_chiffrement = (
            choix_chiffrement  # L'algorithme pour le chiffrement
        )
        self.key_chiffrement = key_size

        list_choix_chiffrement = [choix_chiffrement, key_size]
        self.chiffrement = Chiffrement(
            list_choix_chiffrement
        )  # Creation de l'objet


# Classe qui contient les cles et certificats d'IKEV2 ###
class ChildKeyData:
    def __repr__(self):
        attr_dict = {
            attr: getattr(self, attr)
            for attr in dir(self)
            if not callable(getattr(self, attr)) and not attr.startswith("__")
        }
        return str(attr_dict)

    # Objet pour garder les cles du SA Childs en memoires
    def __init__(self):
        self.secret = b""
        self.keymat = b""
        self.skeyseed = b""
        self.sk_ai = b""
        self.sk_ar = b""
        self.sk_ei = b""
        self.sk_er = b""

        # Variable qui vont contenir les SA Childs pour l'envoie et la reception
        self.sa_init = None
        self.sa_resp = None

    # SETTER #
    # Definition de la skeyseed
    def setsharesecret(self, secret: bytes):
        self.secret = secret

    # Definition de la skeyseed
    def setkeyseed(self, skeyseed: bytes):
        self.skeyseed = skeyseed

    # Definition des differentes cles
    def setkey(
        self, keymat: bytes, len_keys_cipher: int, len_keys_integrity: int
    ):
        # mise en place du keymat
        self.keymat = keymat
        # variable temporaire qui contient le keymat
        # le Keymat contient toutes les clees il faut donc couper les donnes pour recuperer les bonnes clees
        # l'ordre des clees dans le keymat :sk_ei,sk_ai,sk_er,sk_ar,
        key_table = keymat
        # recuperation du sk_d

        # recuperation du sk_ei
        self.sk_ei = key_table[:len_keys_cipher]
        # Retire le sk_ei de la variable
        key_table = key_table[len_keys_cipher:]

        if len_keys_integrity != 0:
            # recuperation du sk_ai
            self.sk_ai = key_table[:len_keys_integrity]
            # Retire le sk_ai de la variable
            key_table = key_table[len_keys_integrity:]
        else:
            # Si pas besoin d'integrite mise a 0 des clees
            self.sk_ai = binascii.unhexlify("00")

        # recuperation du sk_er
        self.sk_er = key_table[:len_keys_cipher]
        # Retire le sk_er de la variable
        key_table = key_table[len_keys_cipher:]

        if len_keys_integrity != 0:
            self.sk_ar = key_table[:len_keys_integrity]
            # Retire le sk_ar de la variable
            key_table = key_table[len_keys_integrity:]
        else:
            self.sk_ar = binascii.unhexlify("00")

        # si il reste des donnees dans le key_table
        if len(key_table) != 0:
            exit(
                "ERROR: La taille de la clé KEYMAT est plus grand que la sommes des clés."
            )

    # fonction pour ajouter une SA
    # KAD : Fonction qui ne semble pas être utilisé au sein du code
    def setSA(
        self,
        spi_init: bytes,
        spi_resp: bytes,
        chiffrement: str,
        integrity: str,
        esn: int,
    ):
        # si AES GCM (pas d'integrite)
        if chiffrement == "AES-GCM-16ICV":
            # activation ou non de ESN
            esn_active = False
            if esn == 1:
                esn_active = True
            # SA pour chiffrer les donnees
            self.sa_init = SecurityAssociation(
                ESP,
                spi=int.from_bytes(spi_resp, "big"),
                crypt_algo="AES-GCM",
                crypt_key=self.sk_ei,
                esn_en=esn_active,
            )

            # SA pour dechiffrer les donnees
            self.sa_resp = SecurityAssociation(
                ESP,
                spi=int.from_bytes(spi_init, "big"),
                crypt_algo="AES-GCM",
                crypt_key=self.sk_er,
                esn_en=esn_active,
            )
        else:
            # AES CTR ou AES CCB
            # SA pour chiffrer les donnes
            self.sa_init = SecurityAssociation(
                ESP,
                spi=int.from_bytes(spi_resp, "big"),
                crypt_algo=chiffrement,
                crypt_key=self.sk_ei,
                auth_algo=integrity,
                auth_key=self.sk_ai,
                esn=1,
            )
            # SA pour dechiffrer les donnees
            self.sa_resp = SecurityAssociation(
                ESP,
                spi=int.from_bytes(spi_init, "big"),
                crypt_algo=chiffrement,
                crypt_key=self.sk_er,
                auth_algo=integrity,
                auth_key=self.sk_ar,
                esn=1,
            )

    # Methode pour afficher les cles
    def show_key(self):
        logger.info(
            f"[dark_orange]SK_ei    :{binascii.hexlify(self.sk_ei)}[/dark_orange]"
        )
        logger.info(
            f"[dark_orange]SK_er    :{binascii.hexlify(self.sk_er)}[/dark_orange]"
        )
        logger.info(
            f"[dark_orange]SK_ai    :{binascii.hexlify(self.sk_ai)}[/dark_orange]"
        )
        logger.info(
            f"[dark_orange]SK_ar    :{binascii.hexlify(self.sk_ar)}[/dark_orange]"
        )


class ChildSA:
    def __repr__(self):
        attr_dict = {
            attr: getattr(self, attr)
            for attr in dir(self)
            if not callable(getattr(self, attr)) and not attr.startswith("__")
        }
        return str(attr_dict)

    # Objet pour garder les cles du SA Childs en memoires
    def __init__(
        self,
        ChildAlgorithm: ChildAlgorithm,
        ChildKeyData: ChildKeyData,
        spi_init: bytes,
        spi_resp: bytes,
    ):
        self.algo_child = ChildAlgorithm
        self.key_child = ChildKeyData
        self.spi_init = spi_init
        self.spi_resp = spi_resp

        if self.algo_child.esn == 1:
            self.LSB = binascii.unhexlify("00000000")
            self.MSB = binascii.unhexlify("00000000")
        else:
            self.LSB = binascii.unhexlify("00000000")
            self.MSB = binascii.unhexlify("")

    def encrypt(self, payload_to_encrypt: bytes, next_hearder: bytes) -> ESP:
        if not self.increase_seq_num():
            exit("ERROR: Cannot increase sequence number for ESP packet.")

        iv = self.algo_child.chiffrement.generate_iv()
        data_pad = self.algo_child.chiffrement.data_padding(
            payload_to_encrypt, next_header=next_hearder
        )
        ciphed = self.algo_child.chiffrement.encrypt(
            self.key_child.sk_ei, iv, data_pad
        )

        cipher_name = get_cipher_tfm_name(self.algo_child.choix_chiffrement)
        is_aead = CIPHERS_TFM_ID[cipher_name][1]

        # pas de GCM
        if not is_aead:
            hash_data = self.algo_child.integrite.compute(
                self.key_child.sk_ai,
                self.spi_resp + self.LSB + iv + ciphed + self.MSB,
            )
        else:
            additional_authenticated_data = self.spi_resp + self.MSB + self.LSB
            hash_data = self.algo_child.chiffrement.IntegrityTag(
                additional_authenticated_data,
                self.key_child.sk_ei,
                iv,
                data_pad,
            )

        return ESP(
            spi=int.from_bytes(self.spi_resp, "big"),
            seq=int.from_bytes(self.LSB, "big"),
            data=iv + ciphed + hash_data,
        )

    def decrypt(
        self, payload_to_decrypt: bytes, header: bytes, is_initiator: bool
    ) -> bytes:
        data = None

        if is_initiator:
            sk_e = self.key_child.sk_er
            sk_a = self.key_child.sk_ar
        else:
            sk_e = self.key_child.sk_ei
            sk_a = self.key_child.sk_ai

        cipher_name = get_cipher_tfm_name(self.algo_child.choix_chiffrement)
        is_aead = CIPHERS_TFM_ID[cipher_name][1]

        iv_size = self.algo_child.chiffrement.iv_size

        if is_aead:
            hash_size = self.algo_child.chiffrement.ICV_size
        else:
            hash_size = self.algo_child.integrite.hash_size

        iv = payload_to_decrypt[:iv_size]
        hash_data = payload_to_decrypt[-hash_size:]
        data_encr = payload_to_decrypt[iv_size:-hash_size]

        if not is_aead:
            hash_compute = self.algo_child.integrite.compute(
                sk_a, header + iv + data_encr + self.MSB
            )

            if hash_compute != hash_data:
                logger.info(
                    "Erreur le hash pour l'integrite ESP ne correspond pas !"
                )
                logger.info(f"hash_data:    {binascii.hexlify(hash_data)}")
                logger.info(f"hash_compute: {binascii.hexlify(hash_compute)}")
            else:
                data = self.algo_child.chiffrement.decrypt(sk_e, iv, data_encr)
        else:
            try:
                aad = header[:-4] + self.MSB + header[-4:]
                data = self.algo_child.chiffrement.DecryptAndCheck(
                    aad, sk_e, iv, data_encr, hash_data
                )
            except Exception:
                logger.info(
                    "Erreur de déchiffrement AEAD concernant la donnée ESP"
                )

        return data

    def increase_seq_num(self) -> bool:
        if self.algo_child.esn == 1:
            if self.LSB == binascii.unhexlify("ffffffff"):
                if self.MSB == binascii.unhexlify("ffffffff"):
                    return False
                else:
                    temp_MSB = int.from_bytes(self.MSB, "big") + 1
                    self.MSB = temp_MSB.to_bytes(4, byteorder="big")

            else:
                temp_LSB = int.from_bytes(self.LSB, "big") + 1
                self.LSB = temp_LSB.to_bytes(4, byteorder="big")
        else:
            if self.LSB == binascii.unhexlify("ffffffff"):
                return False

            temp_LSB = int.from_bytes(self.LSB, "big") + 1
            self.LSB = temp_LSB.to_bytes(4, byteorder="big")

        return True
