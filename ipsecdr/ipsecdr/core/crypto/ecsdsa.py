#!/usr/bin/env python3

import binascii
import collections
import hashlib
import secrets

from typing import NewType, Optional

from ipsecdr.utils.logger import logger


Point = NewType("Point", tuple[int, int])
Signature = NewType("Signature", tuple[bytes, bytes])
EllipticCurve = collections.namedtuple("EllipticCurve", "name p a b g n h")


class NotOnCurve(Exception):
    "Raised when point is not on EllipticCurve"
    pass


brainpoolP256r1 = EllipticCurve(
    "brainpoolP256r1",
    # Field characteristic.
    p=0xA9FB57DBA1EEA9BC3E660A909D838D726E3BF623D52620282013481D1F6E5377,
    # Curve coefficients.
    a=0x7D5A0975FC2C3057EEF67530417AFFE7FB8055C126DC5C6CE94A4B44F330B5D9,
    b=0x26DC5C6CE94A4B44F330B5D9BBD77CBF958416295CF7E1CE6BCCDC18FF8C07B6,
    # Base point.
    g=(
        0x8BD2AEB9CB7E57CB2C4B482FFC81B7AFB9DE27E1E3BD23C23A4453BD9ACE3262,
        0x547EF835C3DAC4FD97F8461A14611DC9C27745132DED8E545C1D54C72F046997,
    ),
    # Subgroup order.
    n=0xA9FB57DBA1EEA9BC3E660A909D838D718C397AA3B561A6F7901E0E82974856A7,
    # Subgroup cofactor.
    h=1,
)

secp256r1 = EllipticCurve(
    "secp256k1",
    # Field characteristic.
    p=0xFFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFF,
    # Curve coefficients.
    a=0xFFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFC,
    b=0x5AC635D8AA3A93E7B3EBBD55769886BC651D06B0CC53B0F63BCE3C3E27D2604B,
    # Base point.
    g=(
        0x6B17D1F2E12C4247F8BCE6E563A440F277037D812DEB33A0F4A13945D898C296,
        0x4FE342E2FE1A7F9B8EE7EB4A7C0F9E162BCE33576B315ECECBB6406837BF51F5,
    ),
    # Subgroup order.
    n=0xFFFFFFFF00000000FFFFFFFFFFFFFFFFBCE6FAADA7179E84F3B9CAC2FC632551,
    # Subgroup cofactor.
    h=1,
)


# Modular arithmetic ##########################################################


def inverse_mod(k: int, p: int) -> int:
    """
    Compute the modular inverse of `k` modulo `p`.

    This function finds the integer `x` such that `(x * k) % p == 1`. It uses the
    Extended Euclidean Algorithm to compute the inverse.

    :param k: The integer whose modular inverse is to be computed.
    :type k: int
    :param p: The modulus, which should be a prime number.
    :type p: int
    :return: The modular inverse of `k` modulo `p`.
    :rtype: int
    :raises ZeroDivisionError: If `k` is zero, as zero has no modular inverse.
    :raises AssertionError: If the greatest common divisor of `k` and `p` is not 1, implying that `k` and `p` are not coprime.
    """
    if k == 0:
        raise ZeroDivisionError("division by zero")

    if k < 0:
        # k ** -1 = p - (-k) ** -1  (mod p)
        return p - inverse_mod(-k, p)

    # Extended Euclidean algorithm.
    s, old_s = 0, 1
    t, old_t = 1, 0
    r, old_r = p, k

    while r != 0:
        quotient = old_r // r
        old_r, r = r, old_r - quotient * r
        old_s, s = s, old_s - quotient * s
        old_t, t = t, old_t - quotient * t

    gcd, x, _ = old_r, old_s, old_t  # gcd, x, y  y not used so _

    assert gcd == 1
    assert (k * x) % p == 1

    return x % p


# Functions that work on curve points #########################################


def is_on_curve(curve: EllipticCurve, point: Optional[Point]) -> bool:
    """
    Determine if a point lies on a given elliptic curve.

    This function checks whether the provided point satisfies the elliptic curve equation:

        y^2 mod p == (x^3 + a*x + b) mod p

    For the special case where `point` is `None`, which represents the point at infinity,
    the function returns `True`.

    :param curve: The elliptic curve against which to check the point.
    :type curve: EllipticCurve
    :param point: The point to verify. Can be `None` to represent the point at infinity.
    :type point: Optional[Point]
    :return: `True` if the point lies on the curve, `False` otherwise.
    :rtype: bool
    """
    if point is None:
        # None represents the point at infinity.
        return True

    x, y = point

    return (y * y - x * x * x - curve.a * x - curve.b) % curve.p == 0


def point_neg(curve: EllipticCurve, point: Optional[Point]) -> Optional[Point]:
    """
    Compute the negation of a point on an elliptic curve.

    Given a point `P` on the elliptic curve, this function returns `-P`, which is the
    reflection of `P` over the x-axis. For the point at infinity (`None`), the function
    returns `None`.

    :param curve: The elliptic curve to which the point belongs.
    :type curve: EllipticCurve
    :param point: The point to negate. Can be `None` for the point at infinity.
    :type point: Optional[Point]
    :return: The negated point `-P`.
    :rtype: Optional[Point]
    :raises AssertionError: If the provided point is not on the curve.
    """
    if not is_on_curve(curve, point):
        raise NotOnCurve

    if point is None:
        # -0 = 0
        return None

    x, y = point
    result = Point((x, -y % curve.p))

    if not is_on_curve(curve, result):
        raise NotOnCurve

    return result


def point_add(
    curve: EllipticCurve, point_1: Optional[Point], point_2: Optional[Point]
) -> Optional[Point]:
    """
    Add two points on an elliptic curve according to the group law.

    This function computes the sum of `point_1` and `point_2` on the given elliptic curve.
    It handles all cases, including point doubling and adding inverses.

    :param curve: The elliptic curve on which the points lie.
    :type curve: EllipticCurve
    :param point_1: The first point to add. Can be `None` for the point at infinity.
    :type point_1: Optional[Point]
    :param point_2: The second point to add. Can be `None` for the point at infinity.
    :type point_2: Optional[Point]
    :return: The resulting point after addition.
    :rtype: Optional[Point]
    :raises NotOnCurve: If either point is not on the curve.
    """
    if not is_on_curve(curve, point_1):
        raise NotOnCurve
    if not is_on_curve(curve, point_2):
        raise NotOnCurve

    if point_1 is None:
        # 0 + point_2 = point_2
        return point_2
    if point_2 is None:
        # point_1 + 0 = point_1
        return point_1

    x1, y1 = point_1
    x2, y2 = point_2

    if x1 == x2 and y1 != y2:
        # point_1 + (-point_1) = 0
        return None

    if x1 == x2:
        # This is the case point_1 == point_2.
        m = (3 * x1 * x1 + curve.a) * inverse_mod(2 * y1, curve.p)
    else:
        # This is the case point_1 != point_2.
        m = (y1 - y2) * inverse_mod(x1 - x2, curve.p)

    x3 = m * m - x1 - x2
    y3 = y1 + m * (x3 - x1)
    result = Point((x3 % curve.p, -y3 % curve.p))

    if not is_on_curve(curve, result):
        raise NotOnCurve

    return result


def scalar_mult(
    curve: EllipticCurve, k: int, point: Optional[Point]
) -> Optional[Point]:
    """
    Multiply a point on an elliptic curve by an integer scalar.

    This function computes `k * point` using the double-and-add algorithm. It efficiently
    calculates the scalar multiplication even for large integers.

    :param curve: The elliptic curve on which the point lies.
    :type curve: EllipticCurve
    :param k: The scalar by which to multiply the point.
    :type k: int
    :param point: The point to be multiplied. Can be `None` for the point at infinity.
    :type point: Optional[Point]
    :return: The resulting point after scalar multiplication.
    :rtype: Optional[Point]
    :raises NotOnCurve: If the provided point is not on the curve.
    """
    if not is_on_curve(curve, point):
        raise NotOnCurve

    if k % curve.n == 0 or point is None:
        return None

    if k < 0:
        # k * point = -k * (-point)
        return scalar_mult(curve, -k, point_neg(curve, point))

    result: Optional[Point] = None
    addend: Optional[Point] = point

    while k:
        if k & 1:
            # Add.
            result = point_add(curve, result, addend)

        # Double.
        addend = point_add(curve, addend, addend)

        k >>= 1

    if not is_on_curve(curve, result):
        raise NotOnCurve

    return result


# Keypair generation and ECSDSA ###############################################


def make_keypair(curve: EllipticCurve) -> tuple[int, Point]:
    """
    Generate a random private and public key pair for elliptic curve cryptography.

    The private key is a random integer in the range [1, n-1], where n is the order of
    the curve. The public key is computed by scalar multiplication of the curve's base
    point with the private key.

    :param curve: The elliptic curve to use for key generation.
    :type curve: EllipticCurve
    :return: A tuple containing the private key and the public key point.
    :rtype: tuple[int, Point]
    """

    private_key = 0
    public_key = None

    while public_key is None:
        private_key = secrets.randbelow(curve.n - 1) + 1
        public_key = scalar_mult(curve, private_key, curve.g)

    return private_key, public_key


def sign_message(
    curve: EllipticCurve, private_key: int, message: bytes
) -> Signature:
    """
    Generate a digital signature for a message using ECDSA.

    This function creates a signature over the provided message using the Elliptic Curve
    Digital Signature Algorithm (ECDSA). It uses the private key and a random nonce to
    produce the signature.

    :param curve: The elliptic curve used for signing.
    :type curve: EllipticCurve
    :param private_key: The signer's private key.
    :type private_key: int
    :param message: The message to be signed.
    :type message: bytes
    :return: The signature as a tuple `(r, s)`, where `r` is a hash and `s` is an integer.
    :rtype: Signature
    :raises ValueError: If the ephemeral point `w` is `None`, indicating an error in scalar multiplication.
    """
    r = b""
    e = 0
    s = 0
    if not (1 <= private_key < curve.n):
        raise ValueError(
            "Private key must be an integer in the range [1, n-1]."
        )

    while not e or not s:
        k = secrets.randbelow(curve.n - 1) + 1
        w = scalar_mult(curve, k, curve.g)

        if not type(w) is tuple:
            raise ValueError("w is None")

        w_x, w_y = w

        h = hashlib.sha256()
        h.update(w_x.to_bytes(32, byteorder="big", signed=False))
        h.update(w_y.to_bytes(32, byteorder="big", signed=False))
        h.update(message)

        r = h.digest()
        e = int.from_bytes(r, byteorder="big", signed=False) % curve.n
        s = (k + e * private_key) % curve.n

    return Signature((r, s.to_bytes(32, byteorder="big", signed=False)))


def verify_signature(
    curve: EllipticCurve, public_key: Point, message: bytes, sign: Signature
) -> bool:
    """
    Verify a digital signature using ECDSA.

    This function checks whether a given signature is valid for the provided message and
    public key, according to the Elliptic Curve Digital Signature Algorithm.

    :param curve: The elliptic curve used for verification.
    :type curve: EllipticCurve
    :param public_key: The signer's public key.
    :type public_key: Point
    :param message: The message whose signature is to be verified.
    :type message: bytes
    :param sign: The signature to verify, as a tuple `(r, s)`.
    :type sign: Signature
    :return: `True` if the signature is valid, `False` otherwise.
    :rtype: bool
    :raises ValueError: If the computed point `w` is `None`, indicating an error in scalar multiplication.
    """
    r_bytes, s_bytes = sign
    s = int.from_bytes(s_bytes, byteorder="big", signed=False)

    if (len(r_bytes) != 32) or (not (0 < s < curve.n)):
        return False

    e = int.from_bytes(r_bytes, byteorder="big", signed=False) % curve.n
    if not e:
        return False

    w = point_add(
        curve,
        scalar_mult(curve, s, curve.g),
        point_neg(curve, scalar_mult(curve, e, public_key)),
    )

    if not type(w) is tuple:
        raise ValueError("w is None")

    w_x, w_y = w

    h = hashlib.sha256()
    h.update(w_x.to_bytes(32, byteorder="big", signed=False))
    h.update(w_y.to_bytes(32, byteorder="big", signed=False))
    h.update(message)

    rp = h.digest()

    if r_bytes == rp:
        return True
    else:
        return False


if __name__ == "__main__":

    msg = b"Hello World !"

    ecc = brainpoolP256r1

    logger.info(f"Curve: {ecc.name}")

    private, public = make_keypair(ecc)
    logger.info(f"Private key: {hex(private)}")
    logger.info(f"Public key: ({hex(public[0])}, {hex(public[1])})")

    signature = sign_message(ecc, private, msg)

    logger.info()
    logger.info(f"Message: {msg!r}")
    logger.info(
        f'Signature: (0x{binascii.hexlify(signature[0]).decode("ascii")}, 0x{binascii.hexlify(signature[1]).decode("ascii")})'
    )
    logger.info(
        f"Verification: {verify_signature(ecc, public, msg, signature)}"
    )
    logger.info()

    ecc = secp256r1

    logger.info(f"Curve: {ecc.name}")

    private, public = make_keypair(ecc)
    logger.info(f"Private key: {hex(private)}")
    logger.info(f"Public key: ({hex(public[0])}, {hex(public[1])})")

    signature = sign_message(ecc, private, msg)

    logger.info()
    logger.info(f"Message: {msg!r}")
    logger.info(
        f'Signature: (0x{binascii.hexlify(signature[0]).decode("ascii")}, 0x{binascii.hexlify(signature[1]).decode("ascii")})'
    )
    logger.info(
        f"Verification: {verify_signature(ecc, public, msg, signature)}"
    )
