import binascii
from scapy.compat import bytes_encode, raw
from scapy.contrib.ikev2 import (
    IKEv2NotifyMessageTypes,
    IKEv2,
    IKEv2_Notify,
    IKEv2_Payload,
)
from ipsecdr.core.crypto.ikev2_crypto import (
    IKEv2Algorithm,
    IKEv2KeyData,
    ChildAlgorithm,
    ChildKeyData,
)
from ipsecdr.core.IKEv2.constants import (
    DH_TFM_ID,
    CIPHERS_TFM_ID,
    PRF_TFM_ID,
    INTEG_TFM_ID,
    ESN_TFM_ID,
    AUTHENTIFICATION_METHOD,
    TRANSFORMS_TYPE,
)
from ipsecdr.utils.logger import logger


def get_tfm_name(tfm_type, tfm_id) -> tuple[str, str]:
    match tfm_type:
        case 1:
            for k, v in CIPHERS_TFM_ID.items():
                logger.debug(f"K: {k} V: {v}")
                if tfm_id == v[0]:
                    return "ENCR", k
            raise KeyError(tfm_id)
        case 2:
            for k, v in PRF_TFM_ID.items():
                if tfm_id == v:
                    return "PRF", k
            raise KeyError(tfm_id)
        case 3:
            for k, v in INTEG_TFM_ID.items():
                if tfm_id == v:
                    return "INTEG", k
            raise KeyError(tfm_id)
        case 4:
            for k, v in DH_TFM_ID.items():
                if tfm_id == v:
                    return "DH", k
            raise KeyError(tfm_id)
        case 5:
            for k, v in ESN_TFM_ID.items():
                if tfm_id == v:
                    return "ESN", k
            raise KeyError(tfm_id)
        case _:
            raise KeyError(tfm_type)


def get_notify_key(req: str):
    """
    Return notify key (int) for a given name

    :param req: The name of the wanted notify key.
    :type req: str
    :return: The fetched key if found else None
    :rtype: int
    """
    for key, value in IKEv2NotifyMessageTypes.items():
        if req == value:
            return key
    return None


def get_notify_name(notify_id: int) -> str:
    """
    Return notify name (str) for a given id

    :param notify_id: The id of the wanted notify.
    :type notify_id: int
    :return: The fetched notify name if found
    :rtype: str
    :raises KeyError: If notify id not found in notifys
    """
    for key, value in IKEv2NotifyMessageTypes.items():
        if notify_id == key:
            return value
    raise KeyError(notify_id)


def get_notify_id(notify_type: str) -> int:
    """
    Return notify key (int) for a given name

    :param notify_type: The name of the wanted notify key.
    :type notify_type: str
    :return: The fetched key if found
    :rtype: int
    :raises KeyError: If name not found in notifys
    """
    for key, value in IKEv2NotifyMessageTypes.items():
        if notify_type == value:
            return key
    raise KeyError(notify_type)


def is_notify(pkt_ikev2: IKEv2, notify_type: str) -> bool:
    """
    Return bool if a given notify is found in a packet

    :param pkt_ikev2: The packet to look in.
    :type pkt_ikev: IKEv2
    :param notify_type: The name of the wanted notify key.
    :type notify_type: str
    :return: True if the notify is found else False
    :rtype: bool
    :raises KeyError: If notify_type is not found in notifys
    """
    try:
        notify_id = get_notify_id(notify_type)
    except KeyError:
        return False

    for payload in pkt_ikev2.iterpayloads():
        if isinstance(payload, IKEv2_Notify):
            if payload.type == notify_id:
                return True
    return False


def check_for_any_notify(pkt_ikev2: IKEv2) -> list:
    """
    Return list of notify type in a packet

    :param pkt_ikev2: The packet to look in.
    :type pkt_ikev: IKEv2
    :return: The list of found notify type's
    :rtype: list
    """
    return [
        payload.type
        for payload in pkt_ikev2.iterpayloads()
        if isinstance(payload, IKEv2_Notify)
    ]


def is_cookie(pkt_ikev2: IKEv2) -> bool:
    """
    Return bool if a cookie is in IKEv2 pkt_ikev2

    :param pkt_ikev2: The packet to look in.
    :type pkt_ikev: IKEv2
    :return: True if the notify is found else False
    :rtype: bool
    """
    return is_notify(pkt_ikev2, "COOKIE")


def get_cookie(pkt_ikev2: IKEv2) -> bytes:
    """
    Return bytes of a cookie in IKEv2 pkt_ikev2

    :param pkt_ikev2: The packet to look in.
    :type pkt_ikev: IKEv2
    :return: Cookie bytes if it is found else b""
    :rtype: bytes
    """
    null_cookie = b""
    notify_id = get_notify_id("COOKIE")
    for payload in pkt_ikev2.iterpayloads():
        if isinstance(payload, IKEv2_Notify):
            if payload.type == notify_id:
                return bytes(payload.notify)
    return null_cookie


def get_notify(pkt_ikev2: IKEv2, notify_type: str) -> dict:
    """
    Return NotifyPayload of notify_type if found in IKEv2 pkt_ikev2

    :param pkt_ikev2: The packet to look in.
    :type pkt_ikev: IKEv2
    :param notify_type: The notify type to look for.
    :type notify_type: str
    :return: Cookie bytes if it is found else b""
    :rtype: bytes
    """
    notify_id = get_notify_id(notify_type)
    for payload in pkt_ikev2.iterpayloads():
        if isinstance(payload, IKEv2_Notify):
            if payload.type == notify_id:
                return {"type": notify_type, "notify": bytes(payload.notify)}
    return False


def get_auth_name(auth_type: int) -> str:
    """
    Return auth name (str) for a given id

    :param auth_type: The id of the wanted notify.
    :type auth_type: int
    :return: The fetched auth name if found
    :rtype: str
    :raises KeyError: If auth_type not in AUTHENTIFICATION_METHOD
    """
    for key, value in AUTHENTIFICATION_METHOD.items():
        if auth_type == value:
            return key
    raise KeyError(auth_type)


# Fonction qui retourne une cle en fonction du groupdesc selectionne
def groupdesc_key(groupdesc: int) -> bytes:
    """
    Return a key bytes for a given DH group

    :param groupdesc: The id of the wanted groupdesc key.
    :type groupdesc: int
    :return: The fetched auth name if found None
    :rtype: bytes
    """
    if groupdesc == DH_TFM_ID["768MODPgr"]:
        key = binascii.unhexlify(
            "e715cc54f33cd34cbcde4f2717d5d950f9e45becda2bf38cd460c0328650497baa89e7ce7c25e514c957f242845970504e4e5d4e38e6292c42eaca72810775ab78dab01488ef31d030894ce405470eed7fa34985f094d1d2c9bc19eb4a73ff86"
        )

    elif groupdesc == DH_TFM_ID["1024MODPgr"]:
        key = binascii.unhexlify(
            "5844dedb00d65b3c344a749989692dbbc9f7abda470a495200c52f7c0ffc42f986efaf6c1c23226a8e49e58c5fceb198982f6930ec1e7399fdbe0dd31a9f24fafb46020454ea91ad3794a3ee0377ccb2e3dde751a49e7da2a6b073a789117206486f6a582eedf667a22947cce9a15dfc685fd9bac1afc10b5013f6ee4d97ad03"
        )

    elif groupdesc == DH_TFM_ID["1536MODPgr"]:
        key = binascii.unhexlify(
            "782cc1629a610c2aef0525e82deaf17035bf42fd9af18a2c69d535e8f3d1acbb40bdb32f4db1e6fa1eb7fb167cf62067c34b6084ae593f650b05c305dcfeeb0d67a5ce987153455996ef727653a956ed522e85a3bc53362d517aae53c75a88907ac1037be92af8bae5be39e20e2782b4bae34c8fe83188b1e115959d16f5f185ce47451f4d9ae7ae5129d04308e3c718a65e2d03ab4c9a135cbbfe564fbb020e0c9f8b4574fc3388eb12a70f8cb95233ae122caeb917d2193b116e36161a5023"
        )

    elif groupdesc == DH_TFM_ID["2048MODPgr"]:
        key = binascii.unhexlify(
            "e703a82b81c2e2cfb04a071846dd2a08eb3400a2d3b7574cb2e71a50c961e2328d8f73f880342abce00836d12609d39952ce9418dd01a34eaa0af4f049fedbd60d3e7cdb3647e8152f720d9ea1da6e9e54029309ac93cc931b293bf6a0bdb866ee79b2a89d38e651aaaa29d2bc8a3ceeda2471b929e6d36b46ff45b770ed8f1f5b1d799f98dadc2d6176bc357cd05e07ce9b881584f32945ef4ed7dd2b00e52eb0df4f4d8bfbe2029bff709c9865ed8a80d6c30d1d38b79321d1858cb5f3841a235a848d2976462ffaf4e619c08760e6e119f3e6d7e0d7ad2fb998c5e0a49850261c43589f8e947feb635fe4e790b939510767f7efa356c9c5b3929d0e89051e"
        )

    elif groupdesc == DH_TFM_ID["3072MODPgr"]:
        key = binascii.unhexlify(
            "0ce8bd83752c535a55d206c1a04fc2dbdaaeba5e9be25d7a67d04ec32353df6d7c14adafc73346609e9a9059930321763af40014950ce6fa36b49a354e4e14b5ce9279af1f1c69093ea928fcf703951d16d9cd40681882834345f5e3cf0c1ce911c2520c07435bc252123c1b1d4677d6359af1a10e30cee5a386c3da1c515e56f17fa59257103cdae1a445465670f9a9a9be41ee6b051b9a7a2920b7c2d37c19618d1bb313fc4369d6fbcbc3b47aafb72a38c6a92c76b5d8b6094d263b28202d8a25e5e54d2311389dc9e82b7567de311e01df28a143be704284d6f19216f350c9d4ce24e237bb3328d79352a9eae6d156679201ee287675cd96d05ce5716eaaac98fc58c482ea769ca6a87928236df02080f877f9ac8bda2b977bbd52d45780c62e6783325f7c13a15b16605574faa287aac1f65cf135669742ff06d4c1247602ce5b67bff474cd69f37cdd236eb5c2e61c8bf3d932263735348c85db4e6df933fc81ce60552a65031ecb53fd478aa768f3e85e2499bf4d0e1ad3b2d65cb743"
        )

    elif groupdesc == DH_TFM_ID["4096MODPgr"]:
        key = binascii.unhexlify(
            "707395d9cb185a669510a79fd1db58db583b2877b5e0527ee65ae7dd842ca9f6ced680e870ac6cb590a7e6a42e7a26e32703f35ecc74fe519154f853a57046ed499c217c14f6bc3245bdb295317b8bd13c762eb1d3f900ed6f3428d2ee6f39b2d7f38f6b4ac24e4f2d3e78978fd627fdf46e852657777af0fee98eb4a781e7de44457fc7189f8b43bff021b3ebedda8fca03024de4ec021927c968e68380eee72c653fdd99cb63be3e6762d9bc09cd8ba4634205cd5eba9d5c388092d49e83dcafdf5a496cfce6f70e10838cba33c5ff73d9c31de620252467bf1c20a73d2a52d6abf1e65d12f0597793fba22e3bd58112f8306ccd7bfe869261afd33eddd50ba1b2406fe165ae75d65a2269513849ba9fd702cc108248a30e6592a8ebffee608f84d4afac12a5c64e6e0336fdc8cf18843aaa44eec3c7717f99e5d316bd89997dc735e5b965f21ab57fd6ee5ccef921d5f1f934add0820b4c806a193df43c312d4f66a7df66a70f8d377766d7b65aef6e1badf6c93f5ed1c8186e698e2318034534e2822bcbe5cff39be926bca6cfb3774c679b26607c65ed60d97e2c271e6b5064c6e8c01e63990ee9f0f407e6f4889db9dde03d771d37f0f6c784ae557eaeb16e2bbe4247a711f4bbbd8a069b50cce45007373bc84e98f8796a445d27c5e7d6efdd540f76e4af9d906bde1fb146a32e2f47ddf39714ac372b574808b39ca5"
        )

    elif groupdesc == DH_TFM_ID["6144MODPgr"]:
        key = binascii.unhexlify(
            "e6d4ed5f9bd67a1ac9029c41c447cb84d6cf2417a1589a820eb8ab312b2c6a51fea153504b37f9f65b5564bef84f06ebab9243571414add42101453207e2ed09d881af7799fa381277226016e86531016efbc4a249404fa507544913d828799648064d76db3d5cb63551ffa42b05ab4274dbc702b1efad9365c98db57c576c28f8294456d3444218fa2dd584d8656f1406b0904f99378cabaa2f9d0c860db58cefd4a9047f5516ec1500e18402408ead2213fb07344a93063f488fea5826ab3028daa3c8d896e20cedbcb97e85486757ba1aa8236929d3e577ce1a9138e199b8fdfa844ef2ec0669180d167d3c5e26c187fb12daed22e98deecd3f4622b9ecf6fece674d6f1dc6e1e3c39b9f4e5b06bbaf378d1cc3bbffb9caeb5b918232d3ea69e75f6fbc99dc897d50aabc1ab1904e8340506b5f2b3831d3792d13585b65941b2e081a4ec0a0cbbb029a71831fb342ff801523ac582e14fb5509ff7073d99512da8b3a90579db59344ab605e03ccdd858aef4f91919c694a73fe38e4f50628a98e9b5340f877b1ac58309ec00666b63521372335706c601b5a97bff4c4a94d42e3f1b876a6935e1579e17f58932ad953609c8f47cf8462c4a93f1a915b2a1e99c9670e84ca5046e6ebcb0606759f585c5c5216b30ed24d5236e260e0b3536d84263606d3901b51f50b53713c62408463944f066b2916469448c508610afc1ec3597f4b1fc8c31a299f5d3d5c51dd94e68a5de8ebc734840933922ef7622b1d2403e3fa62b0d5861650c0f01e70478ad701f4fb101da1fe0c6ee80352e410f880a506d709ec33626b5e90a44d6be48b5ebaf862a096f49b5cb685abc3558b02417415ad86c6a115fc518e4b884af46ea715e8479104d2d78c35479dfce03331dd89e4e88c3a2f99ae5daf12ca2315d4c452bda106465946d1643eeecf94d45b0fd689c85e3fbf29e5784e6960f8c26d7b6e927c325091cc20ea49ac1fe04145d3d00bdf43f489bf5dbbcc99d0959741359403db639f6ecd1c35e6b9bcded085f4abcfea1f83337eb07cfc0d132d1cbb5c8eccd7a70809e8cea855b8bf59d625"
        )

    elif groupdesc == DH_TFM_ID["8192MODPgr"]:
        key = binascii.unhexlify(
            "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"
        )

    elif groupdesc == DH_TFM_ID["256randECPgr"]:
        key = binascii.unhexlify(
            "75029b959759c4975820e0893b264b33e2d4c5af5275a36154eb70df5947be86bfad3774d8b78d75aa24a8e924b99f555462f1d9cabff042a88e22622ffc8f56"
        )

    elif groupdesc == DH_TFM_ID["384randECPgr"]:
        key = binascii.unhexlify(
            "099ae56e94f6a5bbc74c5239874cbd289541bc5d7eef6a038c9879287ce8f81cabcf2e6f999ce043b3ba485393ea34f614ee709ddf5b0ae18de027912c732a88d324f573e7d70269296034ea89e12bf0fb7869ae2c72bb65173063c8ff4e8608"
        )

    elif groupdesc == DH_TFM_ID["521randECPgr"]:
        key = binascii.unhexlify(
            "01e75497b2a3103133ed597c0749cb86507eab318569d92d6118df46c05af863169b6e2e84e0550974e8c8d27469d729c4f777e5fca42d7006283fc8020bee652e040101138b91159f41a9816186e9480267e543ac23bf0e4e001e1c0a5b4de80050b7b8bac07bc5f04dcd5b2d9480e33f56b771088c40fd6dc7293a6b558d95b6c5fb04"
        )

    elif groupdesc == DH_TFM_ID["1024MODP160POSgr"]:
        key = binascii.unhexlify(
            "7c7476e11e2e86bc3747de989ea9ad149a7f11342352e006e2cef9707fa0fc60e0b66d8699710eee8db7a78b10acf24001303b3ea9a8fb08e9614ac8172a73c3eb9554bc350ab9f9fbc27035d08929bcb3d758aad2d134c3a0db9aebc3d5148316f164e79f55ae1a6d2a2bc1e8e36792b2515d126a0028ec7c52bd1d83695317"
        )

    elif groupdesc == DH_TFM_ID["2048MODP224POSgr"]:
        key = binascii.unhexlify(
            "288ee76fbd3523a0c2a37230c65da2179f52fc9f04543a956a386abf0d099f78de0900b2a4120ecdacb2c5b29dc122c6382a9385147190ecf4e77d589520ca36f2c5a57e98f9e6a789a77d3d45f1cb866e42d6a9ab990a04d3838409fa292f84dcad5d0c4a21f121329cb0fae3e2f309ff6031e05059ff46c9f5c12fd4e3695598544a90cb96c7eb62c8bfafb30024f36bd8ff5bc53948e51930329a6a7a752840f87a359e845567adacf418e140f823d807c6404e510a1145638d466c5d52918d7f13aaff2d5987bd77dbd7dd12fb99862c9f599cc9ba12beea4a996bccaca6e7e2bfe5d750854b1526f60af0ca40b831e73ce39b01202698ba15b7760f908f"
        )

    elif groupdesc == DH_TFM_ID["2048MODP256POSgr"]:
        key = binascii.unhexlify(
            "2d66619ead0207f1b6fc9e5a4b630b604dea6d0c6eec6f7ac6e22cabd87c88e69d718ff7182705f3e281e402e77468f8feb38940f922194639d1ba4ffb01328da3aebcf4988df29eb4a78cb96d9a0a8e9e8e843405cf5743a78c16a9cba91270d81aa7a9c73c4e90c3578e594257aa46a507a4585223a2fa80670c8b190e565b15a28ceb1f2f952b95a4ec1f5e5d10007046f388ba336953a6bfd1bf4b3da86a231a37ae0288a968dfeb82a9a90dd1dcabd66ac399fcbbfb488f10a0bce28da5c700e299a5ea5c49327919cf017d5a55052ab3a07b27ab23d19fc26e0b9e57ab1dd596bc709374fe0d226b3659da40737b8010a15a7a8933b8e87771c696cbf0"
        )

    elif groupdesc == DH_TFM_ID["192randECPgr"]:
        key = binascii.unhexlify(
            "4e554d4bf4279c7271dfd435cb8873327b6e46e1617793db4e534796990d7f06606536ead108759507a1aaa12e4ab553"
        )

    elif groupdesc == DH_TFM_ID["224randECPgr"]:
        key = binascii.unhexlify(
            "725bbc514baba8fe2b54e5491d5b27c988cf0f72246de949655a25c26190a466451511a4e7bd029e7230022a9af83d790654ca4e4393ced7"
        )

    elif groupdesc == DH_TFM_ID["brainpoolP224r1"]:
        key = binascii.unhexlify(
            "d1caa7d7b064980e10ebc61dc75b91efb09e4a89f1d33aa50145261f093674d67b4f9c6125f5d6f3d2cf2de8b23a1a90dcefd99c16ce686c"
        )

    elif groupdesc == DH_TFM_ID["brainpoolP256r1"]:
        key = binascii.unhexlify(
            "2e0f283e86d52c6d6aec79d0c4a56de254e6d97c41e05e4c239095f0d1c2230d688510233e0fc1003122d53fb1fb62c52c7d9d32c18ae2fb40738058fb0a615b"
        )

    elif groupdesc == DH_TFM_ID["brainpoolP384r1"]:
        key = binascii.unhexlify(
            "7612bc8031276623c81b9d33c29a79bb967dcb72b003206e1c182a81710f09f419c0bc73fb7fb274fbda80f7af2343d2408a38eeea21ade6b7ec0d2e67ccfaa0bf340d007359f69cd154192d23c67fa150a4046de89e65b5dd9abe4b3ea88ed9"
        )

    elif groupdesc == DH_TFM_ID["brainpoolP512r1"]:
        key = binascii.unhexlify(
            "0c456d1b942faee25562c24213c40a11213b234ac50de8e4c432d4bff88b0b293672f7118194cc33d6978763ef6a451b7a9c1b38660a4027fac38978e7a927c500fac9aee5f915455a0ac3bedb16527b5a05227470786a9b8bfa061027e777953477c313810c494fd2da6cd549ceda8abed71bb34cb09998fb9b69294410177b"
        )
    else:
        logger.error(f"ERROR: Groupdesc unknown: {groupdesc}")
        return
    return key


def encrypt_ike_chunk(
    ikev2_crypto: IKEv2Algorithm, encryption_key: bytes, chunk: bytes
) -> bytes:
    """
    Encrypt an IKE chunk of data, given cryptographic setup of the SA, encryption key
    and chunk of data.

    :param ikev2_crypto: The current SA cryptographic environment.
    :type ikev2_crypto: IKEv2Algorithm
    :param encryption_key: The current SA encryption key (sk_ei).
    :type encryption_key: bytes
    :param chunk: The current chunk of data to encrypt.
    :type chunk: bytes
    :return: A tuple of iv, encrypted_chunk, and padded_data
    :rtype: tuple
    """
    padded_chunk = ikev2_crypto.chiffrement.data_padding(raw(chunk))
    iv = ikev2_crypto.chiffrement.generate_iv()
    encrypted_chunk = iv + ikev2_crypto.chiffrement.encrypt(
        encryption_key, iv, padded_chunk
    )
    return (iv, encrypted_chunk, padded_chunk)


def gen_hash_data(
    ikev2_crypto: IKEv2Algorithm,
    auth_key: bytes,
    ikev2_frame: bytes,
    padded_chunk: bytes,
    iv: bytes,
) -> bytes:
    """
    Generate the hash data for encryption.

    :param ikev2_crypto: The current SA cryptographic environment.
    :type ikev2_crypto: IKEv2Algorithm
    :param auth_key: The current SA auth key generally (sk_ai).
    :type auth_key: bytes
    :param ikev2_frame: The frame to generate hash data from.
    :type ikev2_frame: bytes
    :param chunk: The current chunk of data to encrypt.
    :type chunk: bytes
    :param iv: The current IV for encryptions.
    :type iv: bytes
    :return: The generated hash_data
    :rtype: bytes
    """

    if ikev2_crypto.chiffrement.AES == "GCM":
        # recuperation de l'AAD qui correspond pour IKEv2
        #  au header et au header du payload encrypt
        additional_authenticated_data = bytes_encode(ikev2_frame)[
            : (len(IKEv2() / IKEv2_Payload()))
        ]
        # creation du Checksum avec AES GCm
        hash_data = ikev2_crypto.chiffrement.IntegrityTag(
            additional_authenticated_data, auth_key, iv, padded_chunk
        )
    else:
        hash_data = ikev2_crypto.integrite.compute(
            auth_key, bytes_encode(ikev2_frame)
        )
    return hash_data


def rebuild_header(decrypted: bytes, ikev2_pkt_with_hdr: bytes) -> IKEv2:
    """
    Rebuild the IKE header after processing an encryption

    :param decrypted: The current decrypted data.
    :type decrypted: bytes
    :param ikev2_pkt_with_hdr: The wanted IKE header.
    :type ikev2_pkt_with_hdr: bytes
    :param chunk: The current chunk of data to encrypt.
    :type chunk: bytes
    :return: A tuple of iv, encrypted_chunk, and padded_data
    :rtype: tuple
    """
    pld_hdr = ikev2_pkt_with_hdr
    pld = pld_hdr[:16]
    pld_hdr = pld_hdr[17:]
    nx_pld = (pld_hdr[11]).to_bytes(1, byteorder="little")
    pld = pld + nx_pld + pld_hdr[:11]
    pld = pld + decrypted
    return IKEv2(pld)


def uncipher_ike_pkt(
    ikev2_crypto: IKEv2Algorithm, ikev2_keys: IKEv2KeyData, ike_packet: IKEv2
) -> IKEv2:
    """
    Decrypt an IKE chunk of data, given cryptographic setup of the SA, SA keys
    and an IKEv2 packet.

    :param ikev2_crypto: The current SA cryptographic environment.
    :type ikev2_crypto: IKEv2Algorithm
    :param ikev2_keys: The current SA keys.
    :type ikev2_keys: IKEv2KeyData
    :param ike_packet: The current IKEv2 packet to decrypt.
    :type ike_packet: bytes
    :return: A decrypted IKEv2 packet.
    :rtype: IKEv2
    :raises ValueError: If some fields are not to the expected value
    """
    if not (encrypted := ike_packet["IKEv2_Encrypted"]):
        logger.error("No encrypted data found on IKE packet")
        raise ValueError

    if ike_packet["IKEv2"].flags == "Initiator":
        enc_key = ikev2_keys.sk_ei
        auth_key = ikev2_keys.sk_ai
    elif ike_packet["IKEv2"].flags == "Response":
        enc_key = ikev2_keys.sk_er
        auth_key = ikev2_keys.sk_ar
    else:
        logger.warning(
            "Other modes than Initiator or Response are not supported for now"
        )
        raise ValueError

    header_len_IKE = 28
    header_len_pld = 4
    raw_auth = raw(ike_packet["IKEv2"])
    iv = encrypted.load[: ikev2_crypto.chiffrement.iv_size]
    if ikev2_crypto.chiffrement.AES == "GCM":
        chksum_len = ikev2_crypto.chiffrement.ICV_size
        # fmt: off
        encrypted_data = encrypted.load[
            ikev2_crypto.chiffrement.iv_size: -chksum_len
        ]
        # fmt: on
        h_integrity = encrypted.load[-chksum_len:]
        # AAD gathering
        additional_authenticated_data = raw_auth[
            : (header_len_IKE + header_len_pld)
        ]
        # Unciphering data with AES-GCM
        decrypt = ikev2_crypto.chiffrement.DecryptAndCheck(
            additional_authenticated_data,
            enc_key,
            iv,
            encrypted_data,
            h_integrity,
        )
    else:
        chksum_len = ikev2_crypto.integrite.hash_size
        # fmt: off
        encrypted_data = encrypted.load[
            ikev2_crypto.chiffrement.iv_size: -chksum_len
        ]
        # fmt: on
        h_integrity = encrypted.load[-chksum_len:]
        raw_auth_without_chksum = raw_auth[:-chksum_len]
        h_integrity_calculated = ikev2_crypto.integrite.compute(
            auth_key, raw_auth_without_chksum
        )
        # checksum validation
        if h_integrity_calculated != h_integrity:
            exc_msg = "Erreur le hash pour l'integrite ne correspond pas !\n"
            exc_msg += f"hash_integrite_packet:     {
                binascii.hexlify(h_integrity)}\n"
            exc_msg += f"hash_integrite_calculated: {
                binascii.hexlify(h_integrity_calculated)}\n"
            logger.error(exc_msg)
            raise ValueError
        # dechiffrement de la donnee avec les donnees
        decrypt = ikev2_crypto.chiffrement.decrypt(enc_key, iv, encrypted_data)
    return rebuild_header(
        decrypted=decrypt,
        ikev2_pkt_with_hdr=raw_auth,
    )


def set_ikev2_crypto(
    ikev2_pkt: IKEv2,
    ikev2_crypto: IKEv2Algorithm,
) -> IKEv2Algorithm:
    logger.debug("Building IKEv2Algorithm")
    current_encryption = None
    current_aes_keysize = None
    current_prf = None
    current_integrity = None
    logger.debug("Gathering Transforms chosen by server")
    for payload in ikev2_pkt["IKEv2_SA"]["IKEv2_Proposal"][
        "IKEv2_Transform"
    ].iterpayloads():
        transform_type = payload.transform_type
        transform_id = payload.transform_id
        if transform_type == TRANSFORMS_TYPE["Encryption"]:
            current_encryption = transform_id
            current_aes_keysize = payload.key_length
        elif transform_type == TRANSFORMS_TYPE["PRF"]:
            current_prf = transform_id
        elif transform_type == TRANSFORMS_TYPE["Integrity"]:
            current_integrity = transform_id

    # Set a default integrity if none was found
    #  (GCM/CCM implies no separate integrity)
    if current_integrity is None:
        logger.debug("Set a default integrity if none was found")
        current_integrity = 12
        # default integrity value (AUTH_HMAC_SHA2_256_128)
    logger.debug("Set algorithms.")
    ikev2_crypto.set_other_algo(
        choix_chiffrement=current_encryption,
        key_size=current_aes_keysize,
        choix_integrite=current_integrity,
        choix_prf=current_prf,
    )
    return ikev2_crypto


def set_ikev2_keys(
    ke: bytes,
    nonce_i: bytes,
    nonce_r: bytes,
    spi_i: bytes,
    spi_r: bytes,
    ikev2_crypto: IKEv2Algorithm,
    pub_cert: str,
    key_cert: str,
    trust_chain,
) -> IKEv2KeyData:
    logger.debug("Building IKEv2KeyData")
    # Compute shared secret
    ikev2_crypto.dh.compute_secret(ke)

    # Concatenate nonces in the order Ni | Nr
    nonces_sum = nonce_i + nonce_r

    # Create IKEv2KeyData object
    ikev2_keys = IKEv2KeyData(ikev2_crypto.dh.shared_secret)
    # Generate SKEYSEED
    skeyseed = ikev2_crypto.prf.prf(
        nonces_sum,
        ikev2_crypto.dh.shared_secret,
    )
    ikev2_keys.setkeyseed(skeyseed)
    logger.debug(f"Nonce_i: {binascii.hexlify(nonce_i)}")
    logger.debug(f"Nonce_r: {binascii.hexlify(nonce_r)}")
    logger.debug(f"SPI_i: {binascii.hexlify(spi_i)}")
    logger.debug(f"SPI_r: {binascii.hexlify(spi_r)}")
    logger.debug(f"SKEYSEED: {binascii.hexlify(skeyseed)}")

    # Calculate key lengths
    len_keys_cipher = ikev2_crypto.chiffrement.key_size
    len_keys_integrity = (
        0
        if ikev2_crypto.chiffrement.AES == "GCM"
        else ikev2_crypto.integrite.key_size
    )
    len_keys_prf = ikev2_crypto.prf.key_size

    # Total key material size
    total_size_key = (
        len_keys_cipher * 2 + len_keys_integrity * 2 + len_keys_prf * 3
    )

    # Concatenate SPIs in the order SPIi | SPIr
    spis = spi_i + spi_r

    # Generate KEYMAT
    keymat = ikev2_crypto.prf.prfplus(
        skeyseed,
        nonces_sum + spis,
        total_size_key,
    )
    ikev2_keys.setkey(
        keymat, len_keys_cipher, len_keys_integrity, len_keys_prf
    )
    if pub_cert and key_cert:
        try:
            open(pub_cert, "r")
        except IOError:
            logger.error(
                "Error: Public key certificat does not appear to exist."
            )
            return False
        try:
            open(key_cert, "r")
        except IOError:
            logger.error(
                "Error: Private key certificat does not appear to exist."
            )
            return False
        logger.debug("Adding Certificates")
        # Ouverture et stockage des certificats et des clees public et privee
        ikev2_keys.SetPublicKeyCertificat(
            pub_cert
        )  # recuperation de la cle public et du certificat client
        ikev2_keys.SetPrivateKeyCertificat(
            key_cert
        )  # recuperation de la clee privee
        ikev2_keys.AddTrustedChain(trust_chain)
    return ikev2_keys


def set_ikev2_keys_child(
    nonce_child_init: bytes,
    nonce_child_resp: bytes,
    key_sk_d: bytes,
    decrypted_packet_with_SAChild,
    child_algorithm_data: ChildAlgorithm,
    ikev2_algorithm_data: IKEv2Algorithm,
) -> ChildKeyData:

    # Creation d'un objet où sera stocké toutes les informations
    #  des cles du SA Childs
    child_key = ChildKeyData()

    # Creation du secret partage avec la cle public du respondeur
    #  si groupdesc existe
    if child_algorithm_data.dh:
        child_algorithm_data.dh.compute_secret(
            decrypted_packet_with_SAChild["IKEv2_KE"].ke
        )
        child_key.setsharesecret(child_algorithm_data.dh.shared_secret)

    # KAD # print(decrypted_packet_with_SAChild["IKEv2 Key Exchange"].load)
    # Creation de la SKEYSEED avec les differentes donnees
    #  (child_key.secret = "" si pas groupdesc)
    skeyseed = child_key.secret + nonce_child_init + nonce_child_resp

    # Ajout du Skeyseed à l'objet
    child_key.setkeyseed(skeyseed)

    # recuperation de la taille de la cle de chiffrement
    len_keys_cipher = child_algorithm_data.chiffrement.key_size

    # Si GCM (sans integrite)
    if child_algorithm_data.chiffrement.AES == "GCM":
        # Pas d'integrite donc as de cle
        len_keys_integrity = 0

    else:
        # recuperation de la taille de la cle pour l'integrite
        len_keys_integrity = child_algorithm_data.integrite.key_size

    # Calcul de la taille total des cles en fonctions du nombre necessaire
    # chiffrement 2 cles : sk_ei & sk_er
    # integrite 2 cles : sk_ai & sk_ei
    total_size_key = len_keys_cipher * 2 + len_keys_integrity * 2

    # Creation keymat où seront les clé dérivé
    keymat = ikev2_algorithm_data.prf.prfplus(
        key_sk_d, skeyseed, total_size_key
    )

    # Ajout du keymat et des cles derivees dans l'objet
    # qui contient les cles ikev2
    child_key.setkey(keymat, len_keys_cipher, len_keys_integrity)

    # Retourne l'objet child key.
    return child_key


# TODO handle mutiple proposal in sa_payload
def check_sa_transforms_match(sa_payload, config_transforms) -> bool:
    """
    Checks if the transforms in the received Security Association proposals
    match or are a subset of the configured transforms.

    :param sa_payload: SA payload from the received IKE/ESP SA.
    :param config_transforms: Dictionary of configured transforms.
        Should have keys: "encryption", "prf", "integrity", "groupdesc"
    :return: True if configured transforms match or are a subset
    of the proposal transforms, False otherwise.
    """
    config_encryption = config_transforms.get("encryption", [])
    config_prf = config_transforms.get("prf", [])
    config_integrity = config_transforms.get("integrity", [])
    config_groupdesc = config_transforms.get("groupdesc", [])
    config_encryption_is_aead = False
    for enc_id in config_encryption:
        for cipher_name, cipher_info in CIPHERS_TFM_ID.items():
            if cipher_info[0] == enc_id:
                if cipher_info[1]:  # If IS_AEAD is True
                    config_encryption_is_aead = True
    # Iterate over each proposal in the SA payload
    for proposal in sa_payload.prop.iterpayloads():
        # Track matching status for each transform type
        match_encryption = False
        match_prf = False
        match_integrity = False
        match_groupdesc = False

        # Iterate over each transform in the proposal
        for transform in proposal.trans.iterpayloads():
            transform_type = transform.transform_type
            transform_id = transform.transform_id

            # Check encryption
            if transform_type == TRANSFORMS_TYPE["Encryption"]:
                if transform_id in config_encryption:
                    match_encryption = True

            # Check PRF
            elif transform_type == TRANSFORMS_TYPE["PRF"]:
                if transform_id in config_prf:
                    match_prf = True

            # Check Integrity
            elif transform_type == TRANSFORMS_TYPE["Integrity"]:
                if transform_id in config_integrity:
                    match_integrity = True

            # Check GroupDesc (Diffie-Hellman)
            elif transform_type == TRANSFORMS_TYPE["GroupDesc"]:
                if transform_id in config_groupdesc:
                    match_groupdesc = True

        # If all the necessary transform types match, return True
        if (
            (not config_encryption or match_encryption)
            and (not config_prf or match_prf)
            and (
                (not config_integrity or match_integrity)
                or (config_encryption_is_aead and [] in config_integrity)
            )
            and (not config_groupdesc or match_groupdesc)
        ):
            return True

    # If no proposal matches the configured transforms, return False
    return False
