from typing import List, Optional
from scapy.contrib.ikev2 import IKEv2
from ipsecdr.core.IKEv2.payloads import (
    SecurityAssociation,
    KeyExchange,
    Nonce,
    handle_notify_list,
    TS,
    prepare_ikev2_pkt,
)
from ipsecdr.core.crypto.ikev2_crypto import (
    IKEv2Algorithm,
    IKEv2KeyData,
)
from ipsecdr.utils.models import NotifyPayload


# Removed test ah
# Could be a CP payload (configuration) however hhere it is not implemented
def forge_ikev2_create_child_sa(
    mode: str,
    spi_i,
    spi_r,
    mid: int,
    ikev2_crypto: IKEv2Algorithm,
    ikev2_keys: IKEv2KeyData,
    traffic_selector_client: list,
    traffic_selector_server: list,
    ciphers: list,
    key_lengths: list,
    prfs: list,
    integrities: list,
    groupdescs: list,
    nonce: bytes,
    KE: bytes,
    esn: int,
    notify_options: Optional[List[NotifyPayload]],
    notify_extras: Optional[List[NotifyPayload]] = None,
    cspi: bytes = None,
    return_clear: bool = False,
) -> IKEv2:
    """
    Function to abstract creation of CREATE_CHILD_SA exchange packet in IKEv2.

    :param mode: The mode Initiator or Response
    :type mode: str
    :param spi_i: The initiator SPI.
    :type spi_i: bytes
    :param spi_r: The responder SPI.
    :type spi_r: bytes
    :param mid: Message ID identifier.
    :type mid: int
    :param ikev2_crypto: The current SA crypto environment.
    :type ikev2_crypto: IKEv2Algorithm
    :param ikev2_keys: The current SA keys.
    :type ikev2_keys: IKEv2KeyData
    :param traffic_selector_client: The client Traffic Selector to embed.
    :type traffic_selector_client: list
    :param traffic_selector_server: The server Traffic Selector to embed.
    :type traffic_selector_server: list
    :param ciphers: The choosen ciphers.
    :type ciphers: list
    :param key_lengths: The choosen key length for ciphers.
    :type key_lengths: list
    :param prfs: The choosen PRFs.
    :type prfs: list
    :param groupdescs: The choosen groupdescs.
    :type groupdescs: list
    :param integrities: The choosen integrities.
    :type integrities: list
    :param nonce: The nonce data.
    :type nonce: bytes
    :param KE: The KeyExchange data. Optional.
    :type KE: bytes
    :param esn: The choosen esn.
    :type esn: int
    :param notify_options: A list[dict] of optional notify to send in IKE_AUTH exchange before ID payloads. Optional.
    :type notify_options: Optional[List[NotifyPayload]]
    :param notify_extras: A list[dict] of optional notify to send in IKE_AUTH exchange. Optional.
    :type notify_extras: Optional[List[NotifyPayload]]
    :param cspi: The child spi.
    :type cspi: bytes
    :return: Return an IKEv2 CREATE_CHILD_SA packet.
    :rtype: IKEv2
    """
    if notify_options:
        ikev2_to_be_encrypted_frame = handle_notify_list(
            notify_list=notify_options,
            next_payload="SA",
        ) / SecurityAssociation(
            next_payload="Nonce",
            ciphers=ciphers,
            key_ciphers=key_lengths,
            prfs=prfs,
            groupdescs=groupdescs,
            integrities=integrities,
            esn=esn,
        )
    else:
        ikev2_to_be_encrypted_frame = SecurityAssociation(
            next_payload="Nonce",
            ciphers=ciphers,
            key_ciphers=key_lengths,
            prfs=prfs,
            groupdescs=groupdescs,
            integrities=integrities,
            esn=esn,
            cspi=cspi,
        )
    ikev2_to_be_encrypted_frame /= Nonce(
        next_payload="KE" if KE else "TSi", nonce=nonce
    )
    if KE:
        ikev2_to_be_encrypted_frame /= KeyExchange(
            next_payload="TSi", group=groupdescs[0], ke=KE
        )
    ikev2_to_be_encrypted_frame /= TS(
        mode=mode,
        next_payload="TSr",
        number_of_TSs=1,
        traffic_selector=traffic_selector_client,
    )
    ikev2_to_be_encrypted_frame /= TS(
        mode=mode,
        next_payload="Notify" if notify_extras else "None",
        number_of_TSs=1,
        traffic_selector=traffic_selector_server,
    )
    if notify_extras:
        ikev2_to_be_encrypted_frame /= handle_notify_list(
            notify_list=notify_extras,
            next_payload="None",
        )
    nx_pld = "SA" if not notify_options else "Notify"
    if return_clear:
        return (
            prepare_ikev2_pkt(
                spi_i=spi_i,
                spi_r=spi_r,
                mid=mid,
                mode=mode,
                xchg_type="CREATE_CHILD_SA",
                next_payload=nx_pld,
                ikev2_crypto=ikev2_crypto,
                ikev2_keys=ikev2_keys,
                ikev2_frame=ikev2_to_be_encrypted_frame,
            ),
            ikev2_to_be_encrypted_frame,
        )
    return prepare_ikev2_pkt(
        spi_i=spi_i,
        spi_r=spi_r,
        mid=mid,
        mode=mode,
        xchg_type="CREATE_CHILD_SA",
        next_payload=nx_pld,
        ikev2_crypto=ikev2_crypto,
        ikev2_keys=ikev2_keys,
        ikev2_frame=ikev2_to_be_encrypted_frame,
    )


"""
class ChildSA:
    def __init__(
        self,
        ispi=None,
        rspi=None,
        TSi: list = None,
        TSr: list = None,
        child_SA_algo_data: ChildAlgorithm = None,
        child_SA_key: ChildKeyData = None,
        child_SA_cryptoSA: ChildSA = None,
    ) -> None:
        self.ispi = ispi
        self.rspi = rspi
        self.TSi = TSi
        self.TSr = TSr
        self.algo_data = child_SA_algo_data
        self.key = child_SA_key
        self.SA = child_SA_cryptoSA

    def create_class_algo_child(
        self,
        respondeur_packet_create_child_sa: ChildAlgorithm,
    ) -> bool:
        transform = respondeur_packet_create_child_sa["IKEv2 SA"][
            "IKEv2 Proposal"
        ]["IKE Transform"]
        try:
            ite = 0
            while transform[ite]:
                if (
                    transform[ite].transform_type
                    == TRANSFORMS_TYPE["Encryption"]
                ):
                    # chiffrement et taille de la cle
                    choix_encryption = transform[ite].transform_id
                    choix_aes_keysize = transform[ite].key_length
                elif (
                    transform[ite].transform_type
                    == TRANSFORMS_TYPE["Integrity"]
                ):
                    choix_integrity = transform[ite].transform_id
                elif (
                    transform[ite].transform_type
                    == TRANSFORMS_TYPE["Extended_Sequence_Number"]
                ):
                    choix_esn = transform[ite].transform_id
                ite += 1
        except (ValueError, IndexError) as e:
            logger.exception(f"{e}")
            return False
        cipher_name = get_cipher_tfm_name(choix_encryption)
        is_aead = CIPHERS_TFM_ID[cipher_name][1]
        if is_aead:
            # si AEC CCM ou GCM integrite par defaut
            choix_integrity = INTEG_TFM_ID["AUTH_HMAC_SHA2_256_128"]
        # Ajout des l'algorithmes trouves
        self.algo_data.set_other_algo(
            choix_encryption, choix_aes_keysize, choix_integrity, choix_esn
        )
        return True

    def create_ikev2_keys_child(
        self,
        xchg_ccSA: Exchange,
    ) -> bool:
        # Creation d'un objet où sera stocké toutes les informations des cles du SA Childs
        nonces_sum = (
            xchg_ccSA.initiator["IKEv2 Nonce"].nonce
            + xchg_ccSA.decrypted_responder["IKEv2 Nonce"].nonce
        )
        self.key = ChildKeyData()
        # Creation du secret partage avec la cle public du respondeur si groupdesc existe
        if self.algo_data.dh:
            self.algo_data.dh.compute_secret(
                decrypted_packet_with_SAChild["IKEv2 Key Exchange"].ke
            )
            self.key.setsharesecret(self.algo_data.dh.shared_secret)
        skeyseed = self.key.secret + nonces_sum
        self.key.setkeyseed(skeyseed)
        len_keys_cipher = self.algo_data.chiffrement.key_size
        if self.algo_data.chiffrement.AES == "GCM":
            len_keys_integrity = 0
        else:
            len_keys_integrity = self.algo_data.integrite.key_size
        total_size_key = len_keys_cipher * 2 + len_keys_integrity * 2
        keymat = ikev2_algorithm_data.prf.prfplus(
            self.ikev2_key.key_sk_d, skeyseed, total_size_key
        )
        self.key.setkey(keymat, len_keys_cipher, len_keys_integrity)
        return True

    def encrypt(self, payload_to_encrypt: bytes, next_header: bytes) -> ESP:
        return self.SA.encrypt(payload_to_encrypt, next_header)

    def decrypt(
        self, payload_to_decrypt: bytes, header: bytes, is_initiator: bool
    ) -> bytes:
        return self.SA.decrypt(payload_to_decrypt, header, is_initiator)

    def increase_seq_num(
        self,
    ) -> bool:
        return self.SA.increase_seq_num()

    def make_SA(
        self,
    ):
        self.SA = Child_SA(
            self.algo_data,
            self.key,
            self.ispi,
            self.rspi,
        )
"""
