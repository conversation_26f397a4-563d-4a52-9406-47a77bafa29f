import secrets
import hashlib
from typing import List, Optional
from cryptography import x509
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.serialization import Encoding, PublicFormat
from scapy.contrib.ikev2 import IKEv2
from scapy.contrib.ikev2 import (
    IKEv2_SA,
    IKEv2_Transform,
    IKEv2_Proposal,
    IKEv2_KE,
    IKEv2_Nonce,
    IKEv2_Notify,
    IKEv2_IDi,
    IKEv2_IDr,
    IKEv2_CERT,
    IKEv2_AUTH,
    IKEv2_CP,
    IPv4TrafficSelector,
    IKEv2_TSr,
    IKEv2_TSi,
    IKEv2_Encrypted,
    IKEv2_CERTREQ,
    IKEv2CertificateEncodings,
)
from ipsecdr.core.IKEv2.constants import (
    CIPHERS_TFM_ID,
    AUTHENTIFICATION_METHOD,
    TRANSFORMS_TYPE,
    get_cipher_tfm_name,
)
from ipsecdr.core.IKEv2.utils import (
    get_notify_key,
    encrypt_ike_chunk,
    gen_hash_data,
    get_notify_name,
    get_notify_id,
)
from ipsecdr.core.crypto.ikev2_crypto import IKEv2Algorithm, IKEv2KeyData
from ipsecdr.utils.models import NotifyPayload
from ipsecdr.utils.logger import logger


# May be needed to update payload
# in order to load many certificate here using only 1
def CERTREQ(
    cert_path: str,
    cert_type: int = 4,
    next_payload: str = "None",
    htype: int = 2,
) -> IKEv2_CERTREQ:
    """
    Wrapper around IKEv2_CERTREQ.

    :param cert_path: A path to the certificate.
    :type cert_path: str
    :param cert_type: Cert encoding type.
    :type cert_type: int
    :param next_payload: The next_payload field.
    :type next_payload: str
    :param htype: The type of used hash 1 -> SHA1, 2 -> SHA256
    :type htype: int
    :return: Return an IKEv2_CERTREQ payload.
    :rtype: IKEv2_CERTREQ
    """
    logger.debug("Building CERTREQ")
    if cert_type not in IKEv2CertificateEncodings:
        logger.error(
            f"ERROR: Le choix du type de certificat \
                n'est pas correct ({cert_type})"
        )
        return None

    with open(cert_path, "rb") as cert_handle:
        cert_pem = cert_handle.read()
    cert_x509 = x509.load_pem_x509_certificate(cert_pem, default_backend())
    public_key_bytes = cert_x509.public_key().public_bytes(
        encoding=Encoding.DER, format=PublicFormat.SubjectPublicKeyInfo
    )
    if htype != 2:
        sha_ctx = hashlib.sha1()
    else:
        sha_ctx = hashlib.sha256()
    sha_ctx.update(public_key_bytes)
    hashed_data = sha_ctx.digest()
    return IKEv2_CERTREQ(
        next_payload=next_payload,
        cert_encoding=cert_type,
        cert_authority=hashed_data,
    )


def Encrypted(
    next_payload: str, length: int, encrypted_chunk: bytes
) -> IKEv2_Encrypted:
    """
    Wrapper around IKEv2_Encrypted.

    :param next_payload: The next_payload field.
    :type next_payload: str
    :param length: The length of the encrypted_chunk
    :type length: int
    :param encrypted_chunk: Cert encoding type.
    :type encrypted_chunk: bytes
    :return: Return an IKEv2_Encrypted payload.
    :rtype: IKEv2_Encrypted
    """
    return IKEv2_Encrypted(
        next_payload=next_payload,
        length=length,
        load=encrypted_chunk,
    )


def TS(
    mode: str, next_payload: str, number_of_TSs: int, traffic_selector: list
) -> IKEv2_TSr | IKEv2_TSi:
    """
    Wrapper around IKEv2_TS[r|i].

    :param mode: The mode Initiator or Response
    :type mode: str
    :param next_payload: The next_payload field.
    :type next_payload: str
    :param number_of_TSs: The number of Ts to build (only 1 supported).
    :type number_of_TSs: int
    :param traffic_selector: The list of Traffic Selectors to embed.
    :type traffic_selector: list
    :return: Return an IKEv2_TS[i|r] payload.
    :rtype: IKEv2_TSr, IKEv2_TSi
    :raises ValueError: If mode is not Initiator or Response
    """
    if mode == "Initiator":
        return IKEv2_TSi(
            next_payload=next_payload,
            number_of_TSs=number_of_TSs,
            traffic_selector=IPv4TrafficSelector(
                TS_type=7,
                IP_protocol_ID=None,
                starting_address_v4=traffic_selector[0],
                ending_address_v4=traffic_selector[1],
                start_port=traffic_selector[2],
                end_port=traffic_selector[3],
            ),
        )
    elif mode == "Response":
        return IKEv2_TSr(
            next_payload=next_payload,
            number_of_TSs=number_of_TSs,
            traffic_selector=IPv4TrafficSelector(
                TS_type=7,
                IP_protocol_ID=None,
                starting_address_v4=traffic_selector[0],
                ending_address_v4=traffic_selector[1],
                start_port=traffic_selector[2],
                end_port=traffic_selector[3],
            ),
        )
    else:
        logger.warning(
            "Not all mode are currently supported by ipsecdr,\
                 only Initiator and Response for now."
        )
        logger.error(f"MODE {mode} is not supported or invalid")
        raise ValueError


# Implementation not complete
def CP(next_payload: str, cfg_type: int = 1, attributes=None) -> IKEv2_CP:
    """
    Wrapper around IKEv2_CP. Unused not fully working.

    :param next_payload: The next_payload field.
    :type next_payload: str
    :param cfg_type: The configuration type.
    :type cfg_type: int
    :param attributes: The attributes of CP payload.
    :return: Return an IKEv2_CP payload.
    :rtype: IKEv2_CP
    """
    return IKEv2_CP(
        next_payload=next_payload, CFGType=cfg_type, attributes=attributes
    )


def gen_authentication_data(
    mode: str,
    init_i_pkt: bytes,
    init_r_pkt: bytes,
    ikev2_crypto: IKEv2Algorithm,
    ikev2_keys: IKEv2KeyData,
    id_data: bytes,
    auth_method: int,
) -> bytes:
    """
    Generate the authentication data for an IKEv2 packet to be encrypted.


    :param mode: The mode Initiator or Response
    :type mode: str
    :param init_i_pkt: The exchange initiator init packet.
    :type init_i_pkt: bytes
    :param init_r_pkt: The next_payload field.
    :type init_r_pkt: bytes
    :param ikev2_crypto: The current SA crypto environment.
    :type ikev2_crypto: IKEv2Algorithm
    :param ikev2_keys: The current SA keys.
    :type ikev2_keys: IKEv2KeyData
    :param id_data: The ID data.
    :type id_data: bytes
    :param auth_method: The auth_method for the SA.
    :type auth_method: int
    :return: Return an IKEv2 packet.
    :rtype: IKEv2
    :raises ValueError: If mode is not Initiator or Response
    """
    logger.debug(f"Auth method: {auth_method}")
    if mode == "Initiator":
        prf_idi = ikev2_crypto.prf.prf(ikev2_keys.sk_pi, id_data)
        bytes_to_sign = (
            bytes(init_i_pkt["IKEv2"])
            + init_r_pkt["IKEv2_Nonce"].nonce
            + prf_idi
        )
        authenticated_data = ikev2_keys.sign(bytes_to_sign, auth_method)
    elif mode == "Response":
        prf_idr = ikev2_crypto.prf.prf(ikev2_keys.sk_pr, id_data)
        bytes_to_sign = (
            bytes(init_r_pkt["IKEv2"])
            + init_i_pkt["IKEv2_Nonce"].nonce
            + prf_idr
        )
        authenticated_data = ikev2_keys.sign(bytes_to_sign, auth_method)
    else:
        logger.warning(
            "Not all mode are currently supported by ipsecdr,\
                 only Initiator and Response for now."
        )
        logger.error(f"MODE {mode} is not supported or invalid")
        raise ValueError
    if auth_method != AUTHENTIFICATION_METHOD["DIGITAL_SIGNATURE"]:
        logger.debug("Not digital signature auth")
        return authenticated_data
    oid = ikev2_keys.GetSignatureOID()
    return len(oid).to_bytes(1, byteorder="litlle") + oid + authenticated_data


def AUTH(next_payload: str, auth_method: int, auth_data: bytes) -> IKEv2_AUTH:
    """
    Wrapper around IKEv2_AUTH.

    :param next_payload: The next_payload field.
    :type next_payload: str
    :param auth_method: The choosen auth_method.
    :type auth_method: int
    :param auth_data: The auth_data.
    :type auth_data: bytes
    :return: Return an IKEv2_AUTH payload.
    :rtype: IKEv2_AUTH
    """
    return IKEv2_AUTH(
        next_payload=next_payload,
        auth_type=auth_method,
        load=auth_data,
    )


def CERT(ikev2_keys: IKEv2KeyData, next_payload: str = "Notify") -> IKEv2_CERT:
    """
    Wrapper around IKEv2_CERT.

    :param ikev2_keys: The IKEv2 keys (for cert data).
    :type ikev2_keys: IKEv2KeyData
    :param next_payload: The next_payload field.
    :type next_payload: str
    :return: Return an IKEv2_AUTH payload.
    :rtype: IKEv2_CERT
    """
    payloads = None
    for cert_idx, cert in enumerate(ikev2_keys.certificat_trusted_chain):
        logger.debug(f"Using cert data in trust chain: {cert_idx}:{cert}")
        cert_payload = IKEv2_CERT(
            next_payload=(
                "CERT"
                if cert_idx != len(ikev2_keys.certificat_trusted_chain) - 1
                else next_payload
            ),
            cert_encoding=4,  # Type 4 x509 by default
            cert_data=cert,
        )
        if payloads is None:
            payloads = cert_payload
        else:
            payloads /= cert_payload

    return payloads


def ID(
    mode: str,
    next_payload: str,
    id_type: str,
    id_data: str,
) -> IKEv2_IDi | IKEv2_IDr:
    """
    Wrapper around IKEv2_TS[r|i].

    :param mode: The mode Initiator or Response
    :type mode: str
    :param next_payload: The next_payload field.
    :type next_payload: str
    :param id_type: The type of the ID to embed.
    :type id_type: str
    :param id_data: The ID data to embed.
    :type id_data: str
    :return: Return an IKEv2_ID[i|r] payload.
    :rtype: IKEv2_IDr, IKEv2_IDi
    :raises ValueError: If mode is not Initiator or Response
    """
    if mode == "Initiator":
        return IKEv2_IDi(
            next_payload=next_payload,
            IDtype=id_type,
            ID=id_data.encode("ascii"),
        )
    elif mode == "Response":
        return IKEv2_IDr(
            next_payload=next_payload,
            IDtype=id_type,
            ID=id_data.encode("ascii"),
        )
    else:
        logger.warning(
            "Not all mode are currently supported by ipsecdr,\
                 only Initiator and Response for now."
        )
        logger.error(f"MODE {mode} is not supported or invalid")
        raise ValueError


def handle_notify_list(
    notify_list: Optional[List[NotifyPayload]], next_payload: str = "None"
) -> Optional[bytes]:
    """
    Create IKEv2 chained Notify payloads from a list of NotifyPayload objects.

    :param notify_list: The list of NotifyPayload objects to embed.
    :type notify_list: Optional[List[NotifyPayload]]
    :param next_payload: The next_payload field. Defaults to "None".
    :type next_payload: str
    :return: An IKEv2 packet layer with chained Notify payloads or None if no notifies are present.
    :rtype: Optional[IKEv2]
    """
    if not notify_list:
        return None

    logger.debug("Building extra NOTIFY payloads")
    payloads = None
    for idx, notify in enumerate(notify_list):
        # Determine the display name for the notify type
        if isinstance(notify.type, int):
            notify_type_name = get_notify_name(notify.type)
        else:
            notify_type_name = notify.type

        logger.debug(
            f"Constructing NOTIFY payload with type: {notify_type_name}"
        )

        # Determine the next payload
        current_next_payload = (
            next_payload if idx == len(notify_list) - 1 else "Notify"
        )

        # Build the Notify payload
        notify_payload = Notify(
            next_payload=current_next_payload,
            notify_type=(
                notify.type
                if isinstance(notify.type, int)
                else get_notify_id(notify.type)
            ),
            notify=notify.notify if notify.notify is not None else b"",
        )
        logger.debug(f"Notify payload constructed: {notify_payload}")

        # Chain the payloads
        if payloads is None:
            logger.debug("Initializing NOTIFY payload chain")
            payloads = notify_payload
        else:
            logger.debug("Appending NOTIFY payload to the chain")
            payloads /= notify_payload

    logger.debug("All NOTIFY payloads have been constructed and chained")
    return payloads


def Notify(
    next_payload: str,
    notify_type: str | int,
    notify: bytes = b"",
) -> IKEv2_Notify:
    """
    Wrapper around IKEv2_Notify.

    :param next_payload: The next_payload field.
    :type next_payload: str
    :param notify_type: The type of the Notify to embedded.
    :type notify_type: str, int
    :param notify: The Notify data to embed.
    :type notify: bytes
    :return: Return an IKEv2_Notify payload.
    :rtype: IKEv2_Notify
    """
    return IKEv2_Notify(
        next_payload=next_payload,
        proto=0,
        type=(
            get_notify_key(notify_type) if notify_type is str else notify_type
        ),
        notify=notify,
    )


def Nonce(
    next_payload: str,
    nonce: bytes,
) -> IKEv2_Nonce:
    """
    Wrapper around IKEv2_Nonce.

    :param next_payload: The next_payload field.
    :type next_payload: str
    :param nonce: The Nonce data to embed.
    :type nonce: bytes
    :return: Return an IKEv2_Nonce payload.
    :rtype: IKEv2_Nonce
    """
    return IKEv2_Nonce(
        next_payload=next_payload,
        nonce=nonce,
    )


def KeyExchange(
    next_payload: str,
    group: int,
    ke: bytes,
) -> IKEv2_KE:
    """
    Wrapper around IKEv2_KE.

    :param next_payload: The next_payload field.
    :type next_payload: str
    :param group: The KeyExchange groupt.
    :type group: int
    :param ke: The KeyExchange data to embed.
    :type ke: bytes
    :return: Return an IKEv2_KE payload.
    :rtype: IKEv2_KE
    """
    return IKEv2_KE(
        next_payload=next_payload,
        group=group,
        ke=ke,
    )


def SecurityAssociation(
    next_payload: str,
    ciphers: list,
    key_ciphers: list,
    prfs: list,
    groupdescs: list,
    integrities: list = None,  # Only one allowed to be null in case of AEAD
    esn: int = None,
    cspi: bytes = None,
) -> IKEv2_SA:
    """
    Wrapper around IKEv2_SA.

    :param next_payload: The next_payload field.
    :type next_payload: str
    :param ciphers: The choosen ciphers.
    :type ciphers: list
    :param key_ciphers: The choosen key length for ciphers.
    :type key_ciphers: list
    :param prfs: The choosen PRFs.
    :type prfs: list
    :param groupdescs: The choosen groupdescs.
    :type groupdescs: list
    :param integrities: The choosen integrities.
    :type integrities: list
    :param esn: The choosen esn.
    :type esn: int
    :param cspi: The child spi.
    :type cspi: bytes
    :return: Return an IKEv2_SA payload.
    :rtype: IKEv2_SA
    :raises ValueError: If key length is not supported
    """
    trans_nb = 0
    tfm = None
    #    AEAD = False
    for (
        cipher
    ) in ciphers:  # Written like this there is always only one encr algo
        name = get_cipher_tfm_name(cipher)
        key_lengths = CIPHERS_TFM_ID[name][4]
        for key_len in key_ciphers:
            if key_len not in key_lengths:
                logger.exception(f"Key len {key_len} is not supported")
                raise ValueError
            trans_nb += 1
            if tfm is None:
                tfm = IKEv2_Transform(
                    next_payload="Transform",
                    transform_type="Encryption",
                    transform_id=cipher,
                    length=12,
                    key_length=key_len,
                )
                continue
            tfm /= IKEv2_Transform(
                next_payload="Transform",
                transform_type="Encryption",
                transform_id=cipher,
                length=12,
                key_length=key_len,
            )
    if prfs is not None:
        for prf in prfs:
            trans_nb += 1
            tfm /= IKEv2_Transform(
                next_payload="Transform",
                transform_type="PRF",
                transform_id=prf,
            )

    if integrities:
        for integrity in integrities:
            trans_nb += 1
            tfm /= IKEv2_Transform(
                next_payload="Transform",
                transform_type="Integrity",
                transform_id=integrity,
            )

    for grpdesc in groupdescs[:-1]:
        trans_nb += 1
        tfm /= IKEv2_Transform(
            next_payload="Transform",
            transform_type="GroupDesc",
            transform_id=grpdesc,
        )

    if len(groupdescs) >= 1:
        trans_nb += 1
        tfm /= IKEv2_Transform(
            # 0 (last) or 3 (more) (1 octet) - page 79
            # https://www.rfc-editor.org/rfc/pdfrfc/rfc5996.txt.pdf
            next_payload=0 if esn is None else "Transform",
            transform_type="GroupDesc",
            transform_id=groupdescs[-1],
        )
    if esn is not None:
        trans_nb += 1
        tfm /= IKEv2_Transform(
            next_payload=0,
            transform_type=TRANSFORMS_TYPE["Extended_Sequence_Number"],
            transform_id=esn,
        )
        return IKEv2_SA(
            next_payload=next_payload,
            prop=IKEv2_Proposal(
                proposal=1,
                proto=3,  # ESP
                SPIsize=4,
                SPI=secrets.token_bytes(4) if cspi is None else cspi,
                trans_nb=trans_nb,
                trans=tfm,
            ),
        )
    return IKEv2_SA(
        next_payload=next_payload,
        prop=IKEv2_Proposal(proposal=1, trans_nb=trans_nb, trans=tfm),
    )


def prepare_ikev2_pkt(
    spi_i: bytes,
    spi_r: bytes,
    mid: int,
    mode: str,
    xchg_type: str,
    next_payload: str,
    ikev2_crypto: IKEv2Algorithm,
    ikev2_keys: IKEv2KeyData,
    ikev2_frame: bytes,
) -> IKEv2:
    """
    Prepare an IKEv2 packet to be encrypted.

    :param spi_i: The initiator SPI.
    :type spi_i: bytes
    :param spi_r: The responder SPI.
    :type spi_r: bytes
    :param mid: Message ID identifier.
    :type mid: int
    :param mode: The mode Initiator or Response
    :type mode: str
    :param xchg_type: The exchange type init, auth etc.
    :type xchg_type: str
    :param next_payload: The next_payload field.
    :type next_payload: str
    :param ikev2_crypto: The current SA crypto environment.
    :type ikev2_crypto: IKEv2Algorithm
    :param ikev2_keys: The current SA keys.
    :type ikev2_keys: IKEv2KeyData
    :param ikev2_frame: The current frame to be prepared.
    :type ikev2_frame: bytes
    :return: Return an IKEv2 packet.
    :rtype: IKEv2
    :raises ValueError: If mode is not Initiator or Response
    """
    if mode == "Initiator":
        auth_key = (
            ikev2_keys.sk_ei
            if ikev2_crypto.chiffrement.AES == "GCM"
            else ikev2_keys.sk_ai
        )
        enc_key = ikev2_keys.sk_ei
    elif mode == "Response":
        auth_key = (
            ikev2_keys.sk_er
            if ikev2_crypto.chiffrement.AES == "GCM"
            else ikev2_keys.sk_ar
        )
        enc_key = ikev2_keys.sk_er
    else:
        logger.warning(
            "Other modes than Initiator or Response are not supported for now"
        )
        raise ValueError

    iv, encrypted_data_frame, padded_chunk = encrypt_ike_chunk(
        ikev2_crypto=ikev2_crypto,
        encryption_key=enc_key,
        chunk=ikev2_frame,
    )
    h_len = (
        ikev2_crypto.chiffrement.ICV_size
        if ikev2_crypto.chiffrement.AES == "GCM"
        else ikev2_crypto.integrite.hash_size
    )
    len_pld = len(IKEv2_Encrypted()) + len(encrypted_data_frame) + h_len
    len_final = len_pld + len(IKEv2())  # len payload + len header
    ikev2_frame = IKEv2(
        init_SPI=spi_i,
        resp_SPI=spi_r,
        next_payload="Encrypted",
        exch_type=xchg_type,
        id=mid,
        flags=mode,
        length=len_final,
    ) / Encrypted(
        next_payload=next_payload,
        length=len_pld,
        encrypted_chunk=encrypted_data_frame,
    )

    h_data = gen_hash_data(
        ikev2_crypto=ikev2_crypto,
        auth_key=auth_key,
        ikev2_frame=ikev2_frame,
        padded_chunk=padded_chunk,
        iv=iv,
    )
    ikev2_frame = IKEv2(
        init_SPI=spi_i,
        resp_SPI=spi_r,
        next_payload="Encrypted",
        exch_type=xchg_type,
        id=mid,
        flags=mode,
        length=len_final,
    ) / Encrypted(
        next_payload=next_payload,
        length=len_pld,
        encrypted_chunk=encrypted_data_frame + h_data,
    )
    return ikev2_frame
