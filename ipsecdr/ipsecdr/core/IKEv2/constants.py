import binascii
from ipsecdr.utils.logger import logger

AUTHENTIFICATION_METHOD = {
    "PRF_HMAC_SHA2_256": 2,
    "ECDSA_SECP256R1_SHA256": 9,
    "DIGITAL_SIGNATURE": 14,
    "ECDSA_BP256R1_SHA256": 214,
    "ECSDSA_SECP256R1_SHA256": 225,
    "ECSDSA_BP256R1_SHA256": 228,
}

AUTH_METHOD_IPSEC_DR = {
    "ECDSA_SECP256R1_SHA256": 9,
    "ECDSA_BP256R1_SHA256": 214,
    "ECSDSA_SECP256R1_SHA256": 225,
    "ECSDSA_BP256R1_SHA256": 228,
}


ID_TYPES = {
    "ID_IPV4_ADDR": 1,
    "ID_FQDN": 2,
    "ID_RFC822_ADDR": 3,
    "ID_IPV6_ADDR": 5,
    "ID_DER_ASN1_DN": 9,
    "ID_DER_ASN1_GN": 10,
    "ID_KEY_ID": 11,
}


TFM_ID = {
    "ENCR": 1,  # Encryption Algorithm (IKE and ESP)
    "PRF": 2,  # Pseudo-random Function (IKE)
    "INTEG": 3,  # Integrity Algorithm (IKE, AH, optional in ESP)
    "DH": 4,  # Diffie-Hellman Group (IKE, optional in AH & ESP)
    "ESN": 5,  # Extended Sequence Numbers (AH and ESP)
}

TRANSFORMS_TYPE = {
    "Encryption": 1,
    "PRF": 2,
    "Integrity": 3,
    "GroupDesc": 4,
    "Extended_Sequence_Number": 5,
}


# "NAME": (ID, IS_AEAD, IKEv2_SUPPORTED, ESP_SUPPORTED, [KEY_SIZE_SUPPORTED])
CIPHERS_TFM_ID = {
    "ENCR_DES_IV64": (1, False, False, True, [64]),
    "ENCR_DES": (2, False, True, True, [64]),
    "ENCR_3DES": (3, False, True, True, [192]),
    "ENCR_RC5": (4, False, True, True, [128]),
    "ENCR_IDEA": (5, False, True, True, [128]),
    "ENCR_CAST": (6, False, True, True, [128]),
    "ENCR_BLOWFISH": (7, False, True, True, [128]),
    "ENCR_3IDEA": (8, False, True, True, [256]),
    "ENCR_DES_IV32": (9, False, False, True, [64]),
    "ENCR_NULL": (11, False, False, True, [0]),
    "ENCR_AES_CBC": (12, False, True, True, [128, 192, 256]),
    "ENCR_AES_CTR": (13, False, True, True, [128, 192, 256]),
    "ENCR_AES_CCM_8": (14, True, True, True, [128, 192, 256]),
    "ENCR_AES_CCM_12": (15, True, True, True, [128, 192, 256]),
    "ENCR_AES_CCM_16": (16, True, True, True, [128, 192, 256]),
    "ENCR_AES_GCM_8": (18, True, True, True, [128, 192, 256]),
    "ENCR_AES_GCM_12": (19, True, True, True, [128, 192, 256]),
    "ENCR_AES_GCM_16": (20, True, True, True, [128, 192, 256]),
    "ENCR_NULL_AUTH_AES_GMAC": (21, True, False, True, [128, 192, 256]),
    "ENCR_CAMELLIA_CBC": (23, False, True, True, [128, 192, 256]),
    "ENCR_CAMELLIA_CTR": (24, False, False, True, [128, 192, 256]),
    "ENCR_CAMELLIA_CCM_8": (25, True, False, True, [128, 192, 256]),
    "ENCR_CAMELLIA_CCM_12": (26, True, False, True, [128, 192, 256]),
    "ENCR_CAMELLIA_CCM_16": (27, True, False, True, [128, 192, 256]),
    "ENCR_CHACHA20_POLY1305": (28, True, True, True, [256]),
}

CIPHERS_TFM_ID_IPSEC_DR = {"ENCR_AES_CTR": 13, "ENCR_AES_GCM_16": 20}

# "NAME": ID
PRF_TFM_ID = {
    "PRF_HMAC_MD5": 1,
    "PRF_HMAC_SHA1": 2,
    "PRF_HMAC_TIGER": 3,
    "PRF_AES128_XCBC": 4,
    "PRF_HMAC_SHA2_256": 5,
    "PRF_HMAC_SHA2_384": 6,
    "PRF_HMAC_SHA2_512": 7,
    "PRF_AES128_CMAC": 8,
}

PRF_TFM_ID_IPSEC_DR = {"PRF_HMAC_SHA2_256": 5}


# "NAME": ID
INTEG_TFM_ID = {
    "NONE": 0,
    "AUTH_HMAC_MD5_96": 1,
    "AUTH_HMAC_SHA1_96": 2,
    "AUTH_DES_MAC": 3,
    "AUTH_KPDK_MD5": 4,
    "AUTH_AES_XCBC_96": 5,
    "AUTH_HMAC_MD5_128": 6,
    "AUTH_HMAC_SHA1_160": 7,
    "AUTH_AES_CMAC_96": 8,
    "AUTH_AES_128_GMAC": 9,
    "AUTH_AES_192_GMAC": 10,
    "AUTH_AES_256_GMAC": 11,
    "AUTH_HMAC_SHA2_256_128": 12,
    "AUTH_HMAC_SHA2_384_192": 13,
    "AUTH_HMAC_SHA2_512_256": 14,
}

INTEG_TFM_ID_IPSEC_DR = {"AUTH_HMAC_SHA2_256_128": 12}

# "NAME": ID
DH_TFM_ID = {
    # "NONE": 0, # Feature
    "768MODPgr": 1,
    "1024MODPgr": 2,
    "1536MODPgr": 5,
    "2048MODPgr": 14,
    "3072MODPgr": 15,
    "4096MODPgr": 16,
    "6144MODPgr": 17,
    "8192MODPgr": 18,
    "256randECPgr": 19,
    "384randECPgr": 20,
    "521randECPgr": 21,
    "1024MODP160POSgr": 22,
    "2048MODP224POSgr": 23,
    "2048MODP256POSgr": 24,
    "192randECPgr": 25,
    "224randECPgr": 26,
    "brainpoolP224r1": 27,
    "brainpoolP256r1": 28,
    "brainpoolP384r1": 29,
    "brainpoolP512r1": 30,
    # "Curve25519": 31, # Feature
    # "Curve448": 32 # Feature
}

DH_TFM_ID_IPSEC_DR = {
    "256randECPgr": 19,
    "brainpoolP256r1": 28,
}


# "NAME": ID
ESN_TFM_ID = {"NO_ESN": 0, "ESN": 1}

ESN_TFM_ID_IPSEC_DR = {"ESN": 1}


SIGNATURE_METHOD = {
    "SHA2-256": binascii.unhexlify("0002"),
    "SHA2-384": binascii.unhexlify("0003"),
    "SHA2-512": binascii.unhexlify("0004"),
    "Identity": binascii.unhexlify("0005"),
}

NON_ESP_MARKER = binascii.unhexlify("00000000")


IKEv2_EXCHANGE_TYPES = [
    "INIT",
    "AUTH",
    "CREATE_CHILD_SA",
    "INFORMATIONAL",
    # Unimplemented:
    "IKE_SESSION_RESUME",
    "GSA_AUTH",
    "GSA_REGISTRATION",
    "GSA_REKEY",
    "IKE_INTERMEDIATE",
    "IKE_FOLLOWUP_KE",
]


# DR ALLOWED GROUPDESC

CIPHERS_DR = [13, 20]
INTEGRITY_DR = [12]
PRF_DR = [5]
GROUPDESC_DR = [19, 28]
KEY_DR = [256]
ESN_DR = [1]
AUTH_METHOD_DR = [9, 214, 225, 228]


KEY_SIZE_BITS = [128, 192, 256]

PORT_NAT_T_SRC = 4500
PORT_NAT_T_DST = 4500


def change_nat_t(src_port, dst_port):
    """
    Legacy function that update nat_t ports

    :param src_port: NAT_T source port.
    :type src_port: int
    :param dst_port: NAT_T source port.
    :type dst_port: int
    :return: Nonce
    :rtype: None
    """
    global PORT_NAT_T_SRC, PORT_NAT_T_DST
    PORT_NAT_T_SRC = src_port
    PORT_NAT_T_DST = dst_port


def get_integ_tfm_name(integ_id: int) -> str:
    """
    Return integrity transform name (str) for a given id

    :param integ_id: The id of the wanted integrity.
    :type integ_id: int
    :return: The fetched integrity name if found else raises KeyError
    :rtype: str
    """
    for key, value in INTEG_TFM_ID.items():
        if integ_id == value:
            return key
    raise KeyError(integ_id)


def get_dh_tfm_name(dh_id: int) -> str:
    """
    Return Diffie-Helman transform name (str) for a given id

    :param dh_id: The id of the wanted DH.
    :type dh_id: int
    :return: The fetched DH name if found else raises KeyError
    :rtype: str
    """
    for key, value in DH_TFM_ID.items():
        if dh_id == value:
            return key
    raise KeyError(dh_id)


def get_esn_tfm_name(esn_id: int) -> str:
    """
    Return ESN transform name (str) for a given id

    :param esn_id: The id of the wanted ESN.
    :type esn_id: int
    :return: The fetched DH name if found else raises KeyError
    :rtype: str
    """
    for key, value in ESN_TFM_ID.items():
        if esn_id == value:
            return key
    raise KeyError(esn_id)


def get_tfm_name(tfm_id: int) -> str:
    """
    Return transform name (str) for a given id

    :param tfm_id: The id of the wanted Trnasform.
    :type tfm_id: int
    :return: The fetched Transform name if found else raises KeyError
    :rtype: str
    """
    for key, value in TFM_ID.items():
        if tfm_id == value:
            return key
    raise KeyError(tfm_id)


def get_cipher_tfm_name(cipher_id: int) -> str:
    """
    Return Cipher transform name (str) for a given id

    :param cipher_id: The id of the wanted cipher.
    :type cipher_id: int
    :return: The fetched cipher name if found else raises KeyError
    :rtype: str
    """
    for key, value in CIPHERS_TFM_ID.items():
        if cipher_id == value[0]:
            return key
    raise KeyError(cipher_id)


def get_prf_tfm_name(prf_id: int) -> str:
    """
    Return PRF transform name (str) for a given id

    :param prf_id: The id of the wanted prf.
    :type prf_id: int
    :return: The fetched prf name if found else raises KeyError
    :rtype: str
    """
    for key, value in PRF_TFM_ID.items():
        if prf_id == value:
            return key
    raise KeyError(prf_id)


def get_transform_id(category: str, value):
    """
    Converts a transform name or ID to its integer ID.

    :param category: The category of the transform ('encryption', 'prf', 'integrity', 'dh_group', 'auth_method', 'esn')
    :type category: str
    :param value: The transform name (str) or ID (int)
    :type value: Union[str, int]
    :return: The transform ID as an integer
    :rtype: int
    :raises ValueError:
    """
    if isinstance(value, int):
        # Assume it's already an ID
        return value
    elif isinstance(value, str):
        value_upper = value.upper()
        if category == "encr":
            mapping = {k.upper(): v[0] for k, v in CIPHERS_TFM_ID.items()}
        elif category == "prf":
            mapping = {k.upper(): v for k, v in PRF_TFM_ID.items()}
        elif category == "integ":
            mapping = {k.upper(): v for k, v in INTEG_TFM_ID.items()}
        elif category == "groupdesc":
            mapping = {k.upper(): v for k, v in DH_TFM_ID.items()}
        elif category == "auth_method":
            mapping = {k.upper(): v for k, v in AUTH_METHOD_IPSEC_DR.items()}
        elif category == "esn":
            mapping = {k.upper(): v for k, v in ESN_TFM_ID.items()}
        else:
            raise ValueError(f"Unknown category '{category}'")
        if value_upper in mapping:
            return mapping[value_upper]
        else:
            raise ValueError(f"Unknown {category} name '{value}'")
    else:
        raise ValueError(f"Invalid value for {category}: {value}")


def get_scapy_encr_tfm_name(name: str) -> str:
    """
    Function to convert our naming convention of encryption Transforms, into the
    scaoy way in order to use SecurityAssociation class.

    :param name: The name of our chosen encryption.
    :type name: str
    :return: The fetched scapy encryption name if found else raises KeyError
    :rtype: str
    """
    supported_sa_encr = [
        "NULL",
        "AES-CBC",
        "AES-CTR",
        "AES-GCM",
        "AES-NULL-GMAC",
        "AES-CCM",
        "CHACHA20-POLY1305",
        "DES",
        "3DES",
        "CAST",
        "Blowfish",
    ]
    for encr in supported_sa_encr:
        if encr.replace("-", "_") in name:
            logger.warning(encr)
            return encr
    raise KeyError(name)


def get_scapy_integ_tfm_name(name: str):
    """
    Function to convert our naming convention of integrity Transforms, into the
    scaoy way in order to use SecurityAssociation class.

    :param name: The name of our chosen integrity.
    :type name: str
    :return: The fetched scapy integrity name if found else raises KeyError
    :rtype: str
    """
    supported_sa_integ = [
        "NULL",
        "HMAC-SHA1-96",
        "SHA2-256-128",
        "SHA2-384-192",
        "SHA2-512-256",
        "HMAC-MD5-96",
        "AES-CMAC-96",
    ]
    for integ in supported_sa_integ:
        if integ.replace("-", "_") in name:
            logger.warning(integ)
            return integ
    raise KeyError(name)


def get_id_type_id(id_type: str) -> int:
    """
    Return id_type id (int) for a given id_type name aka 'ID_FQDN'

    :param id_type: The id of the wanted integrity.
    :type id_type: str
    :return: The fetched id_type id if found
    :rtype: int
    :raises KeyError:
    """
    for key, value in ID_TYPES.items():
        if id_type == key:
            return value
    raise KeyError
