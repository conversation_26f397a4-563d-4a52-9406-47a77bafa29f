from typing import List, Optional
from scapy.contrib.ikev2 import IKEv2
from ipsecdr.core.IKEv2.payloads import (
    SecurityAssociation,
    KeyExchange,
    Nonce,
    Notify,
    CERTREQ,
    handle_notify_list,
    NotifyPayload,
)
from ipsecdr.utils.logger import logger

# from ipsecdr.core.IKEv2.utils import handle_notify_list


def forge_ikev2_init(
    mode: str,
    spi_i: bytes,
    spi_r: bytes,
    ciphers: list,
    key_lengths: list,
    prfs: list,
    integrities: list,
    groupdescs: list,
    nonce: bytes,
    cookie: bytes = None,
    KE: bytes = None,
    certreq: str = None,  # Path to CA
    notify_extras: Optional[List[NotifyPayload]] = None,
    mid: int = None,
    childless: bool = False,
) -> IKEv2:
    """
    Function to abstract creation of INIT exchange packet in IKEv2.

    :param mode: The mode Initiator or Response
    :type mode: str
    :param spi_i: The initiator SPI.
    :type spi_i: bytes
    :param spi_r: The responder SPI.
    :type spi_r: bytes
    :param ciphers: The choosen ciphers.
    :type ciphers: list
    :param key_lengths: The choosen key length for ciphers.
    :type key_lengths: list
    :param prfs: The choosen PRFs.
    :type prfs: list
    :param groupdescs: The choosen groupdescs.
    :type groupdescs: list
    :param integrities: The choosen integrities.
    :type integrities: list
    :param mid: Message ID identifier.
    :type mid: int
    :param nonce: The nonce data.
    :type nonce: bytes
    :param cookie: The cookie data. Optional.
    :type cookie: bytes
    :param KE: The KeyExchange data. Optional.
    :type KE: bytes
    :param certreq: The CA path for certreq payload. Optional.
    :type certreq: str
    :param notify_extras: A list[dict] of optional notify to send in IKE_INIT exchange. Optional.
    :type notify_extras: list[dict]
    :param mid: Message ID identifier.
    :type mid: int
    :return: Return an IKEv2 IKE_INIT packet.
    :rtype: IKEv2
    """
    logger.debug("Forging a new IKE_INIT packet")
    if mode != "Initiator":
        mode = "Response"
    if not mid:
        mid = 0
    logger.debug("IKEv2 HDR")
    ikev2_frame = IKEv2(
        init_SPI=spi_i,
        resp_SPI=spi_r,
        next_payload="Notify" if cookie else "SA",
        exch_type="IKE_SA_INIT",
        id=mid,
        flags=mode,
    )
    if cookie:
        logger.debug("IKEv2 N[COOKIE]")
        ikev2_frame /= Notify(
            next_payload="SA",
            notify_type="COOKIE",
            notify=cookie,
        )
    # Build SA
    logger.debug("IKEv2 SA")
    ikev2_frame /= SecurityAssociation(
        next_payload="KE" if KE else "Nonce",
        ciphers=ciphers,
        key_ciphers=key_lengths,
        prfs=prfs,
        groupdescs=groupdescs,
        integrities=integrities,
    )
    # Build KE
    if KE:
        logger.debug("IKEv2 KE")
        ikev2_frame /= KeyExchange(
            next_payload="Nonce", group=groupdescs[0], ke=KE
        )
    # Build Nonce
    if childless:
        if notify_extras is None:
            notify_extras = []
        notify_extras.append(
            NotifyPayload(type="CHILDLESS_IKEV2_SUPPORTED", notify=b"")
        )
    nx_pld = "Notify" if notify_extras is [] else "None"
    nx_pld = "CERTREQ" if certreq else nx_pld
    logger.debug("IKEv2 NONCE")
    ikev2_frame /= Nonce(next_payload=nx_pld, nonce=nonce)
    if certreq:
        logger.debug(f"IKEv2 CERTREQ CA:{certreq}")
        ikev2_frame /= CERTREQ(
            cert_path=certreq,
            next_payload="Notify" if notify_extras else "None",
        )

    # Optional notify list
    # Here CHILDLESS notify for server example
    if notify_extras:
        logger.debug("IKEv2 N[+]")
        ikev2_frame /= handle_notify_list(notify_extras)
    return ikev2_frame
