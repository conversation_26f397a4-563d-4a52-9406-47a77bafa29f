from scapy.contrib.ikev2 import IKEv2
from ipsecdr.core.IKEv2.payloads import (
    prepare_ikev2_pkt,
)
from ipsecdr.core.crypto.ikev2_crypto import IKEv2Algorithm, IKEv2KeyData


# TODO add support for notify list delete and configure pld
def forge_ikev2_informational(
    mode: str,
    spi_i: bytes,
    spi_r: bytes,
    mid: int,
    ikev2_crypto: IKEv2Algorithm,
    ikev2_keys: IKEv2KeyData,
    data: bytes = b"",
    next_payload: str = "None",
) -> IKEv2:
    """
    Function to abstract creation of INFORMATIONAL exchange packet in IKEv2.

    :param mode: The mode Initiator or Response
    :type mode: str
    :param spi_i: The initiator SPI.
    :type spi_i: bytes
    :param spi_r: The responder SPI.
    :type spi_r: bytes
    :param mid: Message ID identifier.
    :type mid: int
    :param ikev2_crypto: The current SA crypto environment.
    :type ikev2_crypto: IKEv2Algorithm
    :param ikev2_keys: The current SA keys.
    :type ikev2_keys: IKEv2KeyData
    :param ikev2_frame: The current frame to be prepared.
    :param data: The data to be embedded in INFORMATIONAL.
    :type data: bytes
    :param next_payload: The next_payload field.
    :type next_payload: str
    :return: Return an IKEv2 IKE_INFORMATIONAL packet.
    :rtype: IKEv2
    """
    return prepare_ikev2_pkt(
        spi_i=spi_i,
        spi_r=spi_r,
        mid=mid,
        mode=mode,
        xchg_type="INFORMATIONAL",
        next_payload=next_payload,
        ikev2_crypto=ikev2_crypto,
        ikev2_keys=ikev2_keys,
        ikev2_frame=data,
    )
