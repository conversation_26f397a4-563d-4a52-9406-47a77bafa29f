from typing import List, Optional
from scapy.contrib.ikev2 import IKEv2
from scapy.layers.inet import IP as LayerIP
from ipsecdr.core.IKEv2.payloads import (
    SecurityAssociation,
    Notify,
    handle_notify_list,
    ID,
    CERT,
    gen_authentication_data,
    AUTH,
    TS,
)
from ipsecdr.core.IKEv2.payloads import (
    prepare_ikev2_pkt,
)
from ipsecdr.core.crypto.ikev2_crypto import IKEv2Algorithm, IKEv2KeyData
from ipsecdr.utils.models import NotifyPayload
from ipsecdr.utils.logger import logger


def forge_ikev2_auth(
    mode: str,
    spi_i,
    spi_r,
    mid: int,
    ikev2_crypto: IKEv2Algorithm,
    ikev2_keys: IKEv2KeyData,
    esn: int,
    notify_options: Optional[List[NotifyPayload]],
    idi_type: int,
    idi_data: str,
    idr_type: int,
    idr_data: str,
    traffic_selector_client: list,
    traffic_selector_server: list,
    init_i_pkt: LayerIP,
    init_r_pkt: LayerIP,
    auth_method: int,
    CP_pld: dict = None,  # Not implemented CP payload, for further development
    nat_t: bool = False,
    childless: bool = False,
    notify_extras: Optional[List[NotifyPayload]] = None,
    include_idr: bool = False,
    include_initial_contact: bool = False,
    return_clear: bool = False,
) -> IKEv2:
    """
    Function to abstract creation of IKE_AUTH exchange packet in IKEv2.

    :param mode: The mode Initiator or Response
    :type mode: str
    :param spi_i: The initiator SPI.
    :type spi_i: bytes
    :param spi_r: The responder SPI.
    :type spi_r: bytes
    :param mid: Message ID identifier.
    :type mid: int
    :param ikev2_crypto: The current SA crypto environment.
    :type ikev2_crypto: IKEv2Algorithm
    :param ikev2_keys: The current SA keys.
    :type ikev2_keys: IKEv2KeyData
    :param esn: The choosen esn.
    :type esn: int
    :param notify_options: A list[dict] of optional notify to send in IKE_AUTH exchange before ID payloads. Optional.
    :type notify_options: Optional[List[NotifyPayload]]
    :param idi_type: The type of the initiator ID to embed.
    :type idi_type: int
    :param idi_data: The initiator ID data to embed.
    :type idi_data: str
    :param idr_type: The type of the responder ID to embed.
    :type idr_type: int
    :param idr_data: The responder ID data to embed.
    :type idr_data: str
    :param traffic_selector_client: The client Traffic Selector to embed.
    :type traffic_selector_client: list
    :param traffic_selector_server: The server Traffic Selector to embed.
    :type traffic_selector_server: list
    :param init_i_pkt: The exchange initiator init packet.
    :type init_i_pkt: bytes
    :param init_r_pkt: The next_payload field.
    :type init_r_pkt: bytes
    :param auth_method: The auth_method for the SA.
    :type auth_method: int
    :param CP_pld: A dict to make a CP payload, not implemented correctly.
    :type CP_pld: dict
    :param nat_t: The nat_t value True or False.
    :type  nat_t: bool
    :param childless: The childless auth choice True or False.
    :type childless: bool
    :param notify_extras: A list[dict] of optional notify to send in IKE_AUTH exchange. Optional.
    :type notify_extras: Optional[List[NotifyPayload]]
    :param include_idr: Include IDr payload in initiator's message (True or False)
    :param include_initial_contact: Include N(INITIAL_CONTACT) Notify payload (True or False)
    :param return_clear: Return the clear (unencrypted) IKEv2 payload along with the packet
    :return: An IKEv2 IKE_AUTH packet
    """
    logger.debug("Forging a new IKE_AUTH packet")

    if mode == "Initiator":
        id_type = idi_type
        id_data = idi_data
    else:
        mode = "Response"
        id_type = idr_type
        id_data = idr_data

    # Determine next payload after ID
    next_payload = "CERT" if ikev2_keys.certificat_trusted_chain else "AUTH"

    # Build the ID payload
    ikev2_to_be_encrypted_frame = ID(
        mode=mode, next_payload=next_payload, id_type=id_type, id_data=id_data
    )
    logger.debug(f"Added ID payload: {ikev2_to_be_encrypted_frame}")
    IDdata = bytes(ikev2_to_be_encrypted_frame)[4:]
    if ikev2_keys.certificat_trusted_chain:
        # Determine next payload after CERT
        if mode == "Initiator":
            if include_initial_contact or notify_options:
                next_payload = "Notify"
            elif include_idr:
                next_payload = "IDr"
            else:
                next_payload = "AUTH"
        else:
            next_payload = "AUTH"

    logger.debug("IKEv2 PRE-ENCR CERT")
    ikev2_to_be_encrypted_frame /= CERT(
        ikev2_keys=ikev2_keys,
        next_payload=next_payload,
    )
    logger.debug(f"Added CERT payload: {ikev2_to_be_encrypted_frame}")

    if mode == "Initiator" and include_initial_contact:
        if notify_options:
            next_payload = "Notify"
        elif include_idr:
            next_payload = "IDr"
        else:
            next_payload = "AUTH"

        ikev2_to_be_encrypted_frame /= Notify(
            next_payload=next_payload,
            notify_type="INITIAL_CONTACT",
        )
        logger.debug(
            f"Added N(INITIAL_CONTACT) payload: {ikev2_to_be_encrypted_frame}"
        )

    if notify_options:
        if include_idr:
            next_payload = "IDr"
        else:
            next_payload = "AUTH"

        ikev2_to_be_encrypted_frame /= handle_notify_list(
            notify_options, next_payload=next_payload
        )
        logger.debug(
            f"Added additional Notify payloads: {ikev2_to_be_encrypted_frame}"
        )

    if mode == "Initiator" and include_idr:
        ikev2_to_be_encrypted_frame /= ID(
            mode="Response",
            next_payload="AUTH",
            id_type=idr_type,
            id_data=idr_data,
        )
        logger.debug(f"Added IDr payload: {ikev2_to_be_encrypted_frame}")

    logger.debug("Generating AUTH data")
    auth_data = gen_authentication_data(
        mode,
        init_i_pkt=init_i_pkt,
        init_r_pkt=init_r_pkt,
        ikev2_crypto=ikev2_crypto,
        ikev2_keys=ikev2_keys,
        id_data=IDdata,
        auth_method=auth_method,
    )

    # Determine next payload after AUTH
    if CP_pld:
        next_payload = "CP"
    elif notify_extras:
        next_payload = "Notify"
    elif not childless:
        next_payload = "SA"
    else:
        next_payload = "None"

    ikev2_to_be_encrypted_frame /= AUTH(
        next_payload=next_payload, auth_data=auth_data, auth_method=auth_method
    )
    logger.debug(f"Added AUTH payload: {ikev2_to_be_encrypted_frame}")

    if not childless:
        logger.debug("IKEv2 not childless building SA etc")
        ikev2_to_be_encrypted_frame /= SecurityAssociation(
            next_payload="TSi",
            ciphers=[ikev2_crypto.choix_chiffrement],
            key_ciphers=[ikev2_crypto.key_chiffrement],
            prfs=[ikev2_crypto.choix_prf],
            groupdescs=[ikev2_crypto.choix_dh],
            integrities=[ikev2_crypto.choix_integrite],
            esn=esn,
        )
        ikev2_to_be_encrypted_frame /= TS(
            mode=mode,
            next_payload="TSr",
            number_of_TSs=1,
            traffic_selector=traffic_selector_client,
        )
        ikev2_to_be_encrypted_frame /= TS(
            mode=mode,
            next_payload="None",
            number_of_TSs=1,
            traffic_selector=traffic_selector_server,
        )
    if notify_extras:
        ikev2_to_be_encrypted_frame /= handle_notify_list(
            notify_extras,
            next_payload="None",
        )
    logger.debug(f"Payload to be encrypted: {ikev2_to_be_encrypted_frame}")
    ike_header_next_payload = "IDi" if mode == "Initiator" else "IDr"
    if return_clear:
        ike_packet = prepare_ikev2_pkt(
            spi_i=spi_i,
            spi_r=spi_r,
            mid=mid,
            mode=mode,
            xchg_type="IKE_AUTH",
            next_payload=ike_header_next_payload,
            ikev2_crypto=ikev2_crypto,
            ikev2_keys=ikev2_keys,
            ikev2_frame=ikev2_to_be_encrypted_frame,
        )
        logger.debug(f"Prepared IKEv2 packet with clear payload: {ike_packet}")
        return ike_packet, ikev2_to_be_encrypted_frame
    else:
        ike_packet = prepare_ikev2_pkt(
            spi_i=spi_i,
            spi_r=spi_r,
            mid=mid,
            mode=mode,
            xchg_type="IKE_AUTH",
            next_payload=ike_header_next_payload,
            ikev2_crypto=ikev2_crypto,
            ikev2_keys=ikev2_keys,
            ikev2_frame=ikev2_to_be_encrypted_frame,
        )
        logger.debug(f"Prepared IKEv2 packet: {ike_packet}")
        return ike_packet


# Responder mode OPTIONAL CONFIG
# OPTIONAL NOTIFY LIST - HTTP_SUPPORT CERTREQ etc
# OPTIONAL IDr in initiator mode
# Initiator AUTH
# Initiator mode OPTIONAL CONFIG
# Initiator Notify list
# SA + TSri
# OPTIONAL VENDOR  // NOTIFY

# IF BAD AUTH ofr server:
# OPTIONAL IDr, CERT, AUTH
# NOTIFY ERROR
# OPTIONAL VENDOR  // NOTIFY
