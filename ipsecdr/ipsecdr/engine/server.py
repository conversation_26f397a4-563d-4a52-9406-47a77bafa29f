import asyncio
import secrets
from functools import wraps
from scapy.compat import raw
from scapy.contrib.ikev2 import IKEv2, IKEv2_Delete, IKEv2_Notify, IKEv2_CP
from scapy.layers.inet import IP, UDP, ICMP
from scapy.layers.ipsec import ESP, SecurityAssociation
from ipsecdr.core.IKEv2.init import forge_ikev2_init
from ipsecdr.core.IKEv2.auth import forge_ikev2_auth
from ipsecdr.core.IKEv2.create_child import forge_ikev2_create_child_sa
from ipsecdr.core.IKEv2.informational import forge_ikev2_informational
from ipsecdr.core.crypto.ikev2_crypto import (
    IKEv2Algorithm,
    ChildAlgorithm,
)
from ipsecdr.core.IKEv2.utils import (
    set_ikev2_crypto,
    set_ikev2_keys,
    uncipher_ike_pkt,
    set_ikev2_keys_child,
)
from ipsecdr.core.IKEv2.constants import (
    get_cipher_tfm_name,
    get_integ_tfm_name,
    get_scapy_encr_tfm_name,
    get_scapy_integ_tfm_name,
)
from ipsecdr.utils.logger import logger

IPsecServerStates = {
    0: "IKE_INIT",
    1: "IKE_AUTH",
    2: "CREATE_CHILD_SA",
    3: "INFORMATIONAL",
    4: "ESP",
    5: "CLOSE_SA",
}

NO_ESP_MARKER = b"\x00\x00\x00\x00"


def add_no_esp_marker_if_nat_t_enabled(func):
    @wraps(func)
    async def wrapper(self, *args, **kwargs):
        response_packet = await func(self, *args, **kwargs)
        if not response_packet:
            return None
        if not self.config.ipsec.nat_t:
            return bytes(response_packet)

        return NO_ESP_MARKER + bytes(response_packet)

    return wrapper


class IPsecServerProtocol(asyncio.DatagramProtocol):
    """
    Asynchronous IPsec server protocol using asyncio.DatagramProtocol.

    This class handles incoming UDP packets asynchronously and processes
    IKEv2 and ESP packets accordingly. It maintains per-client discussions
    and manages the state of IPsec exchanges.

    Attributes:
        config (object): Configuration object for the server.
        state (str): Current state of the server.
        exchange_complete (asyncio.Event): Event to signal the completion of an exchange.
        discussions (dict): Stores discussion context per client.
        loop (asyncio.AbstractEventLoop): The event loop in which the protocol is running.
        paused (bool): Flag indicating whether the protocol is paused.
    """

    def __init__(self, config):
        """
        Initialize the IPsecServerProtocol.

        :param config: Configuration object containing server settings.
        :type config: object
        """
        self.config = config
        self.state = IPsecServerStates[0]
        self.exchange_complete = asyncio.Event()
        self.discussions = {}  # Store discussions per client with context info
        self.loop = asyncio.get_running_loop()
        self.paused = False  # Add paused attribute
        conditional_flags = [
            "init_cookie",
            "init_ke",
            "init_notify_extras",
            "auth_notify_extras",
            "cc_notify_extras",
            "auth_notify_options",
            "cc_notify_options",
            "cc_ke",
        ]
        for flag in conditional_flags:
            if not hasattr(self.config, flag):
                setattr(self.config, flag, None)

    def connection_made(self, transport):
        """
        Called when the server's connection is made.

        :param transport: The transport representing the connection.
        :type transport: asyncio.Transport
        """
        self.transport = transport
        logger.debug("Server connection made")

    def datagram_received(self, data, addr):
        """
        Called when a datagram is received.

        :param data: The data received.
        :type data: bytes
        :param addr: The address of the sender.
        :type addr: tuple
        """
        if self.paused:
            logger.debug("Server is paused; ignoring incoming packet")
            return

        logger.debug(f"Server received {len(data)} bytes from {addr}")
        asyncio.create_task(self.process_packet(data, addr))

    async def process_packet(self, data, addr):
        """
        Process an incoming packet asynchronously.

        Determines whether the packet is an IKEv2 or ESP packet, and handles it accordingly.

        :param data: The data received.
        :type data: bytes
        :param addr: The address of the sender.
        :type addr: tuple
        """
        if data[0:4] != NO_ESP_MARKER:  # No NO_ESP_MARKER
            logger.debug(
                "Received packet seems not to be an IKEv2 packet, no non-ESP marker"
            )
            try:
                if (
                    isinstance(IKEv2(data), IKEv2)
                    and IKEv2(data)["IKEv2"].flags == 0x8
                ):
                    logger.info("Not an IKEv2 packet dropping")
                    if IKEv2(data)["IKEv2"].version == 0x20:
                        logger.info(
                            f"Not an IKEv2 packet dropping because of version\
                                number: {IKEv2(data)["IKEv2"].version}"
                        )
                        response_packet = await self.handle_ikev2_packet(
                            IKEv2(data), addr
                        )
                        if response_packet:
                            raw_response = bytes(response_packet)
                            self.transport.sendto(raw_response, addr)
                            logger.debug(
                                f"Server sent {len(raw_response)} bytes to {addr}"
                            )
                elif isinstance(ESP(data), ESP):
                    try:
                        esp_pkt = ESP(data)
                        response_packet = await self.handle_esp_packet(
                            esp_pkt, addr
                        )
                        if response_packet:
                            raw_response = bytes(response_packet)
                            self.transport.sendto(raw_response, addr)
                            logger.debug(
                                f"Server sent {len(raw_response)} bytes to {addr}"
                            )
                        return
                    except Exception as e:
                        logger.debug(f"Error parsing ESP packet: {e}")
                        logger.debug("Parasite packet dropping it...")
                        return
                else:
                    return
            except Exception as e:
                logger.debug(f"Error parsing IKEv2 packet: {e}")
        else:
            # Remove NO_ESP_MARKER
            data = data[4:]
            try:
                ikev2_pkt = IKEv2(data)
                if ikev2_pkt.flags != 0x8:
                    logger.info(
                        "Received packet with unexpected IKEv2 flags, dropping"
                    )
                    return
                if ikev2_pkt.version != 0x20:
                    logger.info(
                        f"Not an IKEv2 packet dropping because of version number: {ikev2_pkt.version}"
                    )
                    return
                response_packet = await self.handle_ikev2_packet(
                    ikev2_pkt, addr
                )
                if response_packet:
                    raw_response = bytes(response_packet)
                    self.transport.sendto(raw_response, addr)
                    logger.debug(
                        f"Server sent {len(raw_response)} bytes to {addr}"
                    )
            except Exception as e:
                logger.debug(f"Error parsing IKEv2 packet: {e}")
                logger.debug("Parasite packet dropping it...")

    async def handle_esp_packet(self, esp_packet, addr):
        """
        Handle an incoming ESP packet.

        :param esp_packet: The ESP packet received from the client.
        :param addr: Tuple containing the IP address and port of the client that sent the packet.
        :returns: Response packet to be sent back to the client, if applicable.

        """
        self.state = IPsecServerStates[4]
        logger.debug(f"Server handling ESP packet from {addr}")
        spi = esp_packet.spi

        logger.debug(f"Lookup the SA for this SPI: {spi}")
        discussions = self.discussions.get(addr)
        if not discussions:
            logger.error(f"No discussion found for {addr}")
            return None
        logger.debug(f"Found a relevant discussion from {addr}")
        security_associations = discussions.get("security_associations")
        if not security_associations:
            logger.error(f"No security associations found for {addr}")
            return None

        logger.debug(f"Find the SA matching the SPI {spi}")
        sa_entry = None
        for sa_info in security_associations.values():
            sa_spi = sa_info["spi"]
            if sa_spi == spi:
                sa_entry = sa_info
                break

        if not sa_entry:
            logger.error(
                f"No Security Association found for SPI {spi} from {addr}"
            )
            return None

        sa = sa_entry.get("sa")
        if not sa:
            logger.error(
                f"No Security Association object found for SPI {spi} from {addr}"
            )
            return None

        logger.debug("Create response data")
        data_esp = (
            IP(src=self.config.network.ip_src, dst=self.config.network.ip_dst)
            / ICMP()
        )
        logger.debug(
            f"Decrypt the ESP packet if {self.config.esp.when} = {esp_packet.seq}"
        )
        if self.config.esp.when == esp_packet.seq:
            decrypted_data = sa.decrypt(esp_packet)
            logger.debug(
                f"Server decrypted ESP data: {decrypted_data.summary()}"
            )
            # Implement tests

        esp_response = sa.encrypt(data_esp)

        logger.debug("Response encrypted ready to be sent")
        return esp_response[ESP]

    async def handle_ikev2_packet(self, ike_packet, addr):
        """
        Handle an incoming IKEv2 packet.

        Determines the exchange type and delegates to the appropriate handler.

        Args:
            ike_packet (IKEv2): The IKEv2 packet received.
            addr (tuple): The address of the sender.
        """
        logger.debug("Server handling packet")
        exchange_type = ike_packet.exch_type
        self.exchange_complete.clear()
        if exchange_type == 34:  # IKE_SA_INIT
            self.state = IPsecServerStates[0]
            return await self.handle_ike_sa_init(ike_packet, addr)
        elif exchange_type == 35:  # IKE_AUTH
            self.state = IPsecServerStates[1]
            return await self.handle_ike_auth(ike_packet, addr)
        elif exchange_type == 36:  # CREATE_CHILD_SA
            self.state = IPsecServerStates[2]
            return await self.handle_create_child_sa(ike_packet, addr)
        elif exchange_type == 37:  # INFORMATIONAL
            self.state = IPsecServerStates[3]
            return await self.handle_informational(ike_packet, addr)
        else:
            logger.warning(f"Unhandled exchange type: {exchange_type}")
            return None

    @add_no_esp_marker_if_nat_t_enabled
    async def handle_ike_sa_init(self, ike_packet, addr):
        """
        Handle an IKE_SA_INIT exchange.

        Processes the IKE_SA_INIT packet received from the client, generates the response,
        and sets up the initial security association.

        Args:
            ike_packet (IKEv2): The IKEv2 packet received.
            addr (tuple): The address of the sender.

        Returns:
            bytes: The response packet to send back to the client.
        """
        logger.debug("SERVER-INIT: IKEv2 INIT packet received")
        spi_i = ike_packet.init_SPI
        spi_r = secrets.token_bytes(8)
        mid = 0
        ciphers = self.config.ipsec.ike_sa.encr
        key_lengths = self.config.ipsec.ike_sa.encr_size
        prfs = self.config.ipsec.ike_sa.prf
        integrities = self.config.ipsec.ike_sa.integ
        groupdescs = self.config.ipsec.ike_sa.groupdesc
        nonce_r = secrets.token_bytes(32)

        ikev2_crypto = IKEv2Algorithm(
            choix_dh=groupdescs[0],
        )
        KE_r = (
            ikev2_crypto.dh.public_key
            if self.config.init_ke is None
            else self.config.init_ke
        )
        cookie = (
            secrets.token_bytes(64) if hasattr(self.config, "cookie") else None
        )
        config_notify_extras = (
            self.config.ipsec.notify_extras_init
            if hasattr(self.config.ipsec, "notify_extras_init")
            else None
        )
        notify_extras = []
        if config_notify_extras:
            notify_extras.extend(config_notify_extras)

        init_r_pkt = forge_ikev2_init(
            mode="Response",
            spi_i=spi_i,
            spi_r=spi_r,
            ciphers=ciphers,
            key_lengths=key_lengths,
            prfs=prfs,
            integrities=integrities,
            groupdescs=groupdescs,
            nonce=nonce_r,
            cookie=cookie,
            KE=KE_r,
            certreq=self.config.pki.ca_root,
            notify_extras=notify_extras,
            mid=mid,
            childless=self.config.ipsec.childless,
        )
        logger.debug("IKE INIT PACKET BUILT")
        self.discussions[addr] = {
            "spi_i": spi_i,
            "spi_r": spi_r,
            "mid": mid,
            "ikev2_crypto": ikev2_crypto,
            "state": "IKE_INIT",
            "discussion": [
                {
                    "initiator": ike_packet,
                    "responder": init_r_pkt,
                }
            ],
            "security_associations": {},
        }
        self.discussions[addr]["ikev2_crypto"] = set_ikev2_crypto(
            ikev2_pkt=self.discussions[addr]["discussion"][0]["responder"],
            ikev2_crypto=self.discussions[addr]["ikev2_crypto"],
        )
        self.discussions[addr]["ikev2_keys"] = set_ikev2_keys(
            ke=self.discussions[addr]["discussion"][0]["initiator"]["IKEv2"][
                "IKEv2_KE"
            ].ke,
            nonce_i=self.discussions[addr]["discussion"][0]["initiator"][
                "IKEv2"
            ]["IKEv2_Nonce"].nonce,
            nonce_r=nonce_r,
            spi_i=spi_i,
            spi_r=spi_r,
            ikev2_crypto=self.discussions[addr]["ikev2_crypto"],
            pub_cert=self.config.pki.pub_key,
            key_cert=self.config.pki.prv_key,
            trust_chain=self.config.pki.trust_chain,
        )
        logger.debug(f"SERVER-INIT: Response made: {init_r_pkt}")
        self.discussions[addr]["mid"] += 1
        self.exchange_complete.set()
        return init_r_pkt

    @add_no_esp_marker_if_nat_t_enabled
    async def handle_ike_auth(self, ike_packet, addr):
        """
        Handle an IKE_AUTH exchange.

        Processes the IKE_AUTH packet received from the client, verifies identity,
        and completes the IKE negotiation.

        Args:
            ike_packet (IKEv2): The IKEv2 packet received.
            addr (tuple): The address of the sender.

        Returns:
            bytes: The response packet to send back to the client.
        """
        logger.debug("IKEv2 AUTH packet received")
        if addr not in self.discussions:
            logger.error(
                f"No discussion found for {addr} trying AUTH before INIT?"
            )
            return None

        traffic_selector_client = [
            self.config.ipsec.ts.tsi_ip_range[0],
            self.config.ipsec.ts.tsi_ip_range[1],
            self.config.ipsec.ts.tsi_port_range[0],
            self.config.ipsec.ts.tsi_port_range[1],
        ]
        traffic_selector_server = [
            self.config.ipsec.ts.tsr_ip_range[0],
            self.config.ipsec.ts.tsr_ip_range[1],
            self.config.ipsec.ts.tsr_port_range[0],
            self.config.ipsec.ts.tsr_port_range[1],
        ]
        config_notify_extras = (
            self.config.ipsec.notify_extras_auth
            if hasattr(self.config.ipsec, "notify_extras_auth")
            else None
        )
        notify_options = (
            self.config.ipsec.notify_options_auth
            if hasattr(self.config.ipsec, "notify_options_auth")
            else None
        )
        logger.debug("Call forge auth")
        auth_r_pkt, auth_r_clear = forge_ikev2_auth(
            mode="Response",
            spi_i=self.discussions[addr]["spi_i"],
            spi_r=self.discussions[addr]["spi_r"],
            mid=self.discussions[addr]["mid"],
            ikev2_crypto=self.discussions[addr]["ikev2_crypto"],
            ikev2_keys=self.discussions[addr]["ikev2_keys"],
            esn=self.config.ipsec.child_sa.esn,
            notify_options=notify_options,
            idi_type=self.config.ipsec.id.idi_type,
            idi_data=self.config.ipsec.id.idi_data,
            idr_type=self.config.ipsec.id.idr_type,
            idr_data=self.config.ipsec.id.idr_data,
            traffic_selector_client=traffic_selector_client,
            traffic_selector_server=traffic_selector_server,
            init_i_pkt=self.discussions[addr]["discussion"][-1]["initiator"],
            init_r_pkt=self.discussions[addr]["discussion"][-1]["responder"],
            auth_method=self.config.ipsec.auth.auth_method,
            nat_t=False,
            notify_extras=config_notify_extras,
            childless=self.config.ipsec.childless,
            include_idr=self.config.ipsec.include_idr,
            include_initial_contact=self.config.ipsec.include_init_contact,
            return_clear=True,
        )
        logger.debug(
            f"KEYS:\n{self.discussions[addr]['ikev2_keys'].show_key()}"
        )
        logger.debug("IKE AUTH PACKET BUILT")
        logger.debug(f"Response made: {auth_r_pkt}")
        self.discussions[addr]["mid"] += 1
        self.discussions[addr]["discussion"].append(
            {
                "initiator": ike_packet,
                "decrypted_initiator": uncipher_ike_pkt(
                    self.discussions[addr]["ikev2_crypto"],
                    self.discussions[addr]["ikev2_keys"],
                    ike_packet,
                ),
                "responder": auth_r_pkt,
                "decrypted_responder": auth_r_clear,
            }
        )
        self.exchange_complete.set()
        return auth_r_pkt

    @add_no_esp_marker_if_nat_t_enabled
    async def handle_create_child_sa(self, ike_packet, addr):
        """
        Handle a CREATE_CHILD_SA exchange.

        Processes the CREATE_CHILD_SA packet received from a client, negotiates a new Child SA,
        and prepares it for further IPsec communication.

        Args:
            ike_packet (IKEv2): The CREATE_CHILD_SA packet received from the client.
            addr (tuple): The address of the sender.

        Returns:
            bytes: The response packet for the CREATE_CHILD_SA exchange.
        """
        if addr not in self.discussions:
            logger.error(
                f"No discussion found for {addr} trying CREATE_CHILD_SA before INIT?"
            )
            return None
        traffic_selector_client = [
            self.config.ipsec.ts.tsi_ip_range[0],
            self.config.ipsec.ts.tsi_ip_range[1],
            self.config.ipsec.ts.tsi_port_range[0],
            self.config.ipsec.ts.tsi_port_range[1],
        ]
        traffic_selector_server = [
            self.config.ipsec.ts.tsr_ip_range[0],
            self.config.ipsec.ts.tsr_ip_range[1],
            self.config.ipsec.ts.tsr_port_range[0],
            self.config.ipsec.ts.tsr_port_range[1],
        ]

        notify_extras = (
            self.config.ipsec.notify_extras_child
            if hasattr(self.config.ipsec, "notify_extras_child")
            else None
        )
        notify_options = (
            self.config.ipsec.notify_options_child
            if hasattr(self.config.ipsec, "notify_options_child")
            else None
        )

        clear_cchild_sa_pkt_i = uncipher_ike_pkt(
            ikev2_crypto=self.discussions[addr]["ikev2_crypto"],
            ikev2_keys=self.discussions[addr]["ikev2_keys"],
            ike_packet=ike_packet,
        )

        child_spi_i = clear_cchild_sa_pkt_i["IKEv2_SA"]["IKEv2_Proposal"].SPI
        child_spi_i = int.from_bytes(child_spi_i, "big")
        child_nonce_i = clear_cchild_sa_pkt_i["IKEv2_Nonce"].nonce
        child_nonce_r = secrets.token_bytes(16)

        child_crypto = ChildAlgorithm()
        child_crypto.Set_dh(self.config.ipsec.child_sa.groupdesc[0])
        child_crypto.set_other_algo(
            self.config.ipsec.child_sa.encr[0],
            self.config.ipsec.child_sa.encr_size[0],
            self.config.ipsec.child_sa.integ[0],
            self.config.ipsec.child_sa.esn,
        )

        child_keys = set_ikev2_keys_child(
            nonce_child_init=child_nonce_i,
            nonce_child_resp=child_nonce_r,
            key_sk_d=self.discussions[addr]["ikev2_keys"].sk_d,
            decrypted_packet_with_SAChild=clear_cchild_sa_pkt_i,
            child_algorithm_data=child_crypto,
            ikev2_algorithm_data=self.discussions[addr]["ikev2_crypto"],
        )
        crypt_algo_name = get_scapy_encr_tfm_name(
            get_cipher_tfm_name(self.config.ipsec.child_sa.encr[0])
        )
        auth_algo_name = get_scapy_integ_tfm_name(
            get_integ_tfm_name(self.config.ipsec.child_sa.integ[0])
            if self.config.ipsec.child_sa.integ[0] != 20
            else "NULL"
        )
        self.discussions[addr]["security_associations"][child_spi_i] = {
            "spi": child_spi_i,
            "nonce_i": child_nonce_i,
            "nonce_r": child_nonce_r,
            "ikev2_crypto": child_crypto,
            "ikev2_keys": child_keys,
            "sa": SecurityAssociation(
                proto=ESP,
                spi=child_spi_i,
                crypt_algo=crypt_algo_name,
                crypt_key=child_keys.sk_er,
                auth_algo=auth_algo_name,
                auth_key=child_keys.sk_ar,
                nat_t_header=(
                    UDP(sport=4500, dport=addr[1])
                    if self.config.ipsec.nat_t.nat_t
                    else None
                ),
                esn_en=bool(self.config.ipsec.child_sa.esn),
            ),
        }
        KE_r = self.discussions[addr]["security_associations"][child_spi_i][
            "ikev2_crypto"
        ].dh.public_key
        cchild_sa_pkt_r, cchild_sa_pkt_r_clear = forge_ikev2_create_child_sa(
            mode="Response",
            spi_i=self.discussions[addr]["spi_i"],
            spi_r=self.discussions[addr]["spi_r"],
            mid=self.discussions[addr]["mid"],
            ikev2_crypto=self.discussions[addr]["ikev2_crypto"],
            ikev2_keys=self.discussions[addr]["ikev2_keys"],
            traffic_selector_client=traffic_selector_client,
            traffic_selector_server=traffic_selector_server,
            ciphers=self.config.ipsec.child_sa.encr,
            key_lengths=self.config.ipsec.child_sa.encr_size,
            prfs=None,
            integrities=self.config.ipsec.child_sa.integ,
            groupdescs=self.config.ipsec.child_sa.groupdesc,
            nonce=secrets.token_bytes(32),
            KE=KE_r,
            esn=self.config.ipsec.child_sa.esn,
            notify_options=notify_options,
            notify_extras=notify_extras,
            cspi=child_spi_i.to_bytes(4, "big"),
            return_clear=True,
        )
        self.discussions[addr]["mid"] += 1
        self.discussions[addr]["discussion"].append(
            {
                "initiator": ike_packet,
                "decrypted_initiator": clear_cchild_sa_pkt_i,
                "responder": cchild_sa_pkt_r,
                "decrypted_responder": cchild_sa_pkt_r_clear,
            }
        )
        self.exchange_complete.set()
        return cchild_sa_pkt_r

    @add_no_esp_marker_if_nat_t_enabled
    async def handle_informational(self, ike_packet, addr):
        """
        Handle an INFORMATIONAL exchange.

        Processes the INFORMATIONAL packet received from a client. Handles delete requests
        for existing SAs or other informational messages.

        Args:
            ike_packet (IKEv2): The INFORMATIONAL packet received from the client.
            addr (tuple): The address of the sender.

        Returns:
            bytes: The response packet for the INFORMATIONAL exchange, if applicable.
        """
        try:
            if addr not in self.discussions:
                logger.error(
                    f"No discussion found for {addr} trying INFORMATIONAL before INIT?"
                )
                return None

            clear_ike_packet = uncipher_ike_pkt(
                ikev2_crypto=self.discussions[addr]["ikev2_crypto"],
                ikev2_keys=self.discussions[addr]["ikev2_keys"],
                ike_packet=ike_packet,
            )

            if clear_ike_packet.next_payload == 42:  # DELETE payload
                protocol = clear_ike_packet["IKEv2_Delete"].proto
                if protocol == 1:  # IKE
                    logger.debug(
                        f"Received DELETE request for IKE SA from {addr}"
                    )
                    pkt_tosend = forge_ikev2_informational(
                        mode="Response",
                        spi_i=self.discussions[addr]["spi_i"],
                        spi_r=self.discussions[addr]["spi_r"],
                        mid=clear_ike_packet.id,  # Use the same message ID
                        ikev2_crypto=self.discussions[addr]["ikev2_crypto"],
                        ikev2_keys=self.discussions[addr]["ikev2_keys"],
                        data=b"",  # No additional data needed for DELETE
                        next_payload="None",
                    )
                elif protocol == 2:  # AH
                    logger.debug(
                        f"Received DELETE request for AH SA from {addr}"
                    )
                    pkt_tosend = forge_ikev2_informational(
                        mode="Response",
                        spi_i=self.discussions[addr]["spi_i"],
                        spi_r=self.discussions[addr]["spi_r"],
                        mid=clear_ike_packet.id,
                        ikev2_crypto=self.discussions[addr]["ikev2_crypto"],
                        ikev2_keys=self.discussions[addr]["ikev2_keys"],
                        data=b"",
                        next_payload="None",
                    )
                elif protocol == 3:  # ESP
                    logger.debug(
                        f"Received DELETE request for ESP SA from {addr}"
                    )
                    pkt_tosend = forge_ikev2_informational(
                        mode="Response",
                        spi_i=self.discussions[addr]["spi_i"],
                        spi_r=self.discussions[addr]["spi_r"],
                        mid=clear_ike_packet.id,
                        ikev2_crypto=self.discussions[addr]["ikev2_crypto"],
                        ikev2_keys=self.discussions[addr]["ikev2_keys"],
                        data=b"",
                        next_payload="None",
                    )
                else:
                    logger.error(
                        f"Unknown protocol in DELETE payload from {addr}"
                    )
                    self.exchange_complete.set()
                    return None
                logger.debug(f"Response to DELETE prepared: {pkt_tosend}")
                self.discussions[addr]["discussion"].append(
                    {
                        "initiator": ike_packet,
                        "decrypted_initiator": clear_ike_packet,
                        "responder": pkt_tosend,
                        "decrypted_responder": b"",  # No payload handled for now
                    }
                )
                self.exchange_complete.set()
                if protocol == 1:  # IKE
                    del self.discussions[addr]
                    logger.debug(
                        "In real implementations we should delete the discussion we keep it for"
                    )
                return pkt_tosend
            else:
                logger.debug(
                    f"Received unsupported payload type {clear_ike_packet.next_payload} in INFORMATIONAL"
                )
                return None
        except Exception as e:
            logger.error(f"Exception in handle_informational: {e}")
            self.exchange_complete.set()
            return None

    async def close_all_SA(self):
        """
        Close all existing Security Associations (SAs).

        Iterates through all active discussions and sends an IKEv2 DELETE message to each client,
        then clears the discussions.
        """
        logger.debug("Starting close_all_SA()")
        self.state = IPsecServerStates[5]
        for addr, sa_info in self.discussions.items():
            try:
                close_sa_pkt = forge_ikev2_informational(
                    mode="Response",
                    spi_i=sa_info["spi_i"],
                    spi_r=sa_info["spi_r"],
                    mid=sa_info["mid"],
                    ikev2_crypto=sa_info["ikev2_crypto"],
                    ikev2_keys=sa_info["ikev2_keys"],
                    data=IKEv2_Delete(
                        proto=1,  # IKE
                        SPIsize=0,
                    ),
                    next_payload="None",
                )

                if self.config.ipsec.nat_t.nat_t:
                    close_sa_pkt = NO_ESP_MARKER + bytes(close_sa_pkt)
                raw_packet = raw(close_sa_pkt)
                self.transport.sendto(raw_packet, addr)
                logger.debug(f"Sent IKE DELETE packet to {addr}")
            except Exception as e:
                logger.error(f"Error sending IKE DELETE packet to {addr}: {e}")
        logger.info("All SAs closed.")

    def connection_lost(self, exc):
        """
        Called when the connection is lost.

        Args:
            exc (Exception): The exception that caused the connection to be lost, if any.
        """
        logger.info("Server connection closed")

    @add_no_esp_marker_if_nat_t_enabled
    async def trigger_exchange(self, exchange: str, addr, **kwargs):
        """
        Trigger an exchange by sending a packet of the specified type to the client.

        :param exchange: The type of exchange to perform (e.g., "Init", "Auth", "Create_Child_SA", "Informational", "ESP").
        :type exchange: str
        :param addr: The address of the client to send the packet to.
        :type addr: tuple
        :param kwargs: Additional parameters needed for the exchange.
        """
        if addr not in self.discussions:
            # For 'Init' exchange, we may not have an existing session
            if exchange == "INIT":
                # Start a new session
                session = {}
                session["spi_i"] = secrets.token_bytes(8)
                session["spi_r"] = secrets.token_bytes(
                    8
                )  # May not be used yet
                session["mid"] = 0
                session["ikev2_crypto"] = IKEv2Algorithm(
                    choix_dh=self.config.ipsec.ike_sa.groupdesc[0],
                )
                session["discussion"] = []
                session["security_associations"] = {}
                self.discussions[addr] = session
                session["state"] = "IKE_INIT"
            else:
                logger.error(
                    f"No discussion found for {addr}, cannot initiate exchange."
                )
                return None
        else:
            session = self.discussions[addr]

        # Clear the exchange_complete event before starting the exchange
        self.exchange_complete.clear()

        if exchange == "Init":
            # Prepare IKE_SA_INIT as initiator
            nonce_i = secrets.token_bytes(32)
            key_exchange = session["ikev2_crypto"].dh.public_key

            init_i_pkt = forge_ikev2_init(
                mode="Initiator",
                spi_i=session["spi_i"],
                spi_r=0,
                ciphers=self.config.ipsec.ike_sa.encr,
                key_lengths=self.config.ipsec.ike_sa.encr_size,
                prfs=self.config.ipsec.ike_sa.prf,
                integrities=self.config.ipsec.ike_sa.integ,
                groupdescs=self.config.ipsec.ike_sa.groupdesc,
                nonce=nonce_i,
                cookie=None,
                KE=key_exchange,
                notify_extras=None,
                mid=session["mid"],
            )
            raw_packet = bytes(init_i_pkt)
            self.transport.sendto(raw_packet, addr)
            logger.debug(f"Server sent IKE_SA_INIT to {addr}")

            # Update session
            session["discussion"].append(
                {"initiator": init_i_pkt, "responder": None}
            )
            session["mid"] += 1

            # Responses will be handled by handle_ikev2_packet
            logger.info("Server initiated IKE_SA_INIT exchange sent")
            # Note: Do not wait for response here; responses are handled by handle_* methods

        elif exchange == "AUTH":
            # Prepare IKE_AUTH as initiator
            if "ikev2_keys" not in session or "ikev2_crypto" not in session:
                logger.error("IKEv2 Crypto or Keys not initialized")
                return False

            # Prepare parameters
            notify_options = kwargs.get("notify_options", None)
            notify_extras = kwargs.get("notify_extras", None)
            traffic_selector_client = [
                self.config.ipsec.ts.tsi_ip_range[0],
                self.config.ipsec.ts.tsi_ip_range[1],
                self.config.ipsec.ts.tsi_port_range[0],
                self.config.ipsec.ts.tsi_port_range[1],
            ]
            traffic_selector_server = [
                self.config.ipsec.ts.tsr_ip_range[0],
                self.config.ipsec.ts.tsr_ip_range[1],
                self.config.ipsec.ts.tsr_port_range[0],
                self.config.ipsec.ts.tsr_port_range[1],
            ]

            auth_i_pkt, auth_i_pkt_clear = forge_ikev2_auth(
                mode="Initiator",
                spi_i=session["spi_i"],
                spi_r=session["spi_r"],
                mid=session["mid"],
                ikev2_crypto=session["ikev2_crypto"],
                ikev2_keys=session["ikev2_keys"],
                esn=self.config.ipsec.child_sa.esn,
                notify_options=notify_options,
                idi_type=self.config.ipsec.id.idi_type,
                idi_data=self.config.ipsec.id.idi_data,
                idr_type=self.config.ipsec.id.idr_type,
                idr_data=self.config.ipsec.id.idr_data,
                traffic_selector_client=traffic_selector_client,
                traffic_selector_server=traffic_selector_server,
                init_i_pkt=session["discussion"][-1]["initiator"],
                init_r_pkt=session["discussion"][-1]["responder"],
                auth_method=self.config.ipsec.auth.auth_method,
                nat_t=False,
                notify_extras=notify_extras,
                return_clear=True,
            )
            raw_packet = bytes(auth_i_pkt)
            self.transport.sendto(raw_packet, addr)
            logger.debug(f"Server sent IKE_AUTH to {addr}")

            # Update session
            session["discussion"].append(
                {
                    "initiator": auth_i_pkt,
                    "decrypted_initiator": auth_i_pkt_clear,
                    "responder": None,
                }
            )
            session["mid"] += 1

            # Responses will be handled by handle_ikev2_packet
            logger.info("Server initiated IKE_AUTH exchange sent")

        elif exchange == "CREATE_CHILD_SA":
            # Prepare a CREATE_CHILD_SA message as Initiator
            if "ikev2_keys" not in session or "ikev2_crypto" not in session:
                logger.error("IKEv2 Crypto or Keys not initialized")
                return False

            notify_options = kwargs.get("notify_options", None)
            notify_extras = kwargs.get("notify_extras", None)
            traffic_selector_client = [
                self.config.ipsec.ts.tsi_ip_range[0],
                self.config.ipsec.ts.tsi_ip_range[1],
                self.config.ipsec.ts.tsi_port_range[0],
                self.config.ipsec.ts.tsi_port_range[1],
            ]
            traffic_selector_server = [
                self.config.ipsec.ts.tsr_ip_range[0],
                self.config.ipsec.ts.tsr_ip_range[1],
                self.config.ipsec.ts.tsr_port_range[0],
                self.config.ipsec.ts.tsr_port_range[1],
            ]
            child_spi_i = secrets.token_bytes(4)
            nonce_i = secrets.token_bytes(32)

            cchild_sa_pkt_i, cchild_sa_pkt_i_clear = (
                forge_ikev2_create_child_sa(
                    mode="Initiator",
                    spi_i=session["spi_i"],
                    spi_r=session["spi_r"],
                    mid=session["mid"],
                    ikev2_crypto=session["ikev2_crypto"],
                    ikev2_keys=session["ikev2_keys"],
                    traffic_selector_client=traffic_selector_client,
                    traffic_selector_server=traffic_selector_server,
                    ciphers=self.config.ipsec.child_sa.encr,
                    key_lengths=self.config.ipsec.child_sa.encr_size,
                    prfs=None,
                    integrities=self.config.ipsec.child_sa.integ,
                    groupdescs=self.config.ipsec.child_sa.groupdesc,
                    nonce=nonce_i,
                    KE=session["ikev2_crypto"].dh.public_key,
                    esn=self.config.ipsec.child_sa.esn,
                    notify_options=notify_options,
                    notify_extras=notify_extras,
                    cspi=child_spi_i,
                    return_clear=True,
                )
            )
            raw_packet = bytes(cchild_sa_pkt_i)
            self.transport.sendto(raw_packet, addr)
            logger.debug(f"Server sent CREATE_CHILD_SA to {addr}")

            # Update session
            session["mid"] += 1
            session["discussion"].append(
                {
                    "initiator": cchild_sa_pkt_i,
                    "decrypted_initiator": cchild_sa_pkt_i_clear,
                    "responder": None,
                }
            )

            # Responses will be handled by handle_ikev2_packet
            logger.info("Server initiated CREATE_CHILD_SA exchange sent")

        elif exchange == "Informational":
            # Prepare and send an INFORMATIONAL exchange
            info_type = kwargs.get("info_type", "Empty")
            SPIs = kwargs.get("SPIs", None)
            proto = kwargs.get("proto", 3)  # Default to ESP
            configuration_data = kwargs.get("configuration_data", None)
            notify = kwargs.get("notify", None)
            next_payload = kwargs.get("next_payload", 0)

            # Prepare the payload based on info_type
            payload = None
            if info_type == "Delete":
                if not SPIs:
                    logger.error("SPIs are required for Delete type.")
                    return None
                spi = SPIs[0]
                payload = IKEv2_Delete(
                    proto=proto, SPIsize=len(spi), SPInum=1, SPI=[spi]
                )
                next_payload = "Delete"
                logger.debug(
                    "Prepared Delete payload for INFORMATIONAL exchange."
                )

            elif info_type == "Configuration":
                if configuration_data is None:
                    logger.error(
                        "configuration_data is required for Configuration type."
                    )
                    return None
                payload = IKEv2_CP(
                    CFGType=2,  # Assuming CFG_REPLY
                    attributes=[configuration_data],
                )
                next_payload = "CP"
                logger.debug(
                    "Prepared Configuration payload for INFORMATIONAL exchange."
                )

            elif info_type == "Notify":
                if notify is None:
                    logger.error("notify data is required for Notify type.")
                    return None
                payload = IKEv2_Notify(
                    proto=1,  # IKE protocol
                    SPIsize=0,
                    type=notify["type"],
                    notify=notify["notify"],
                )
                next_payload = "Notify"
                logger.debug(
                    "Prepared Notify payload for INFORMATIONAL exchange."
                )

            elif info_type == "Empty":
                payload = b""
                logger.debug(
                    "Prepared Empty payload for INFORMATIONAL exchange."
                )
            else:
                logger.error(f"Unsupported info_type: {info_type}")
                return None

            # Forge the IKEv2 INFORMATIONAL packet
            info_pkt = forge_ikev2_informational(
                mode="Initiator",
                spi_i=session["spi_i"],
                spi_r=session["spi_r"],
                mid=session["mid"],
                ikev2_crypto=session["ikev2_crypto"],
                ikev2_keys=session["ikev2_keys"],
                data=bytes(payload),
                next_payload=next_payload,
            )
            raw_packet = bytes(info_pkt)
            self.transport.sendto(raw_packet, addr)
            logger.debug(
                f"Server sent INFORMATIONAL of type {info_type} to {addr}"
            )

            # Update message ID and discussions
            session["mid"] += 1
            session["discussion"].append(
                {
                    "initiator": info_pkt,
                    "decrypted_initiator": payload,
                    "responder": None,
                }
            )

            # Responses will be handled by handle_ikev2_packet
            logger.info(
                f"Server initiated INFORMATIONAL exchange of type {info_type} sent"
            )

        elif exchange == "ESP":
            # Send an ESP packet to the client
            sa_dict = session.get("security_associations")
            if not sa_dict:
                logger.error(f"No security associations found for {addr}")
                return None

            # Get the SA to use
            sa_entry = next(iter(sa_dict.values()))
            sa = sa_entry["sa"]

            # Prepare data to send
            data = kwargs.get("data", None)
            if data is None:
                data = (
                    IP(
                        src=self.config.network.ip_src,
                        dst=self.config.network.ip_dst,
                    )
                    / ICMP()
                )

            esp_packet = sa.encrypt(data)
            raw_packet = bytes(esp_packet[ESP])  # Get the ESP layer
            self.transport.sendto(raw_packet, addr)
            logger.debug(f"Server sent ESP packet to {addr}")

            logger.info("Server sent ESP packet")

        else:
            logger.error(f"Unsupported exchange type: {exchange}")
            return None

        # Clear exchange_complete event after sending the packet
        self.exchange_complete.clear()


class IPsecServerManager:
    """
    Manages the lifecycle of an IPsec server.
    """

    def __init__(self, config, overlay_config=None):
        """
        Initialize the IPsecServerManager.

        :param config: The primary configuration object for the IPsec server.
        :type config: object
        :param overlay_config: An optional configuration overlay that overrides
                               specific settings in the base configuration.
        :type overlay_config: dict, optional
        """
        self.config = config
        if overlay_config:
            self.config.update_from_dict(overlay_config)
        self.server_address = (
            str(self.config.network.ipsec_src),
            self.config.network.port_src,
        )
        self.transport = None
        self.protocol = None
        self.server_port = self.config.network.port_src  # Use specified port

    async def start(self):
        """
        Start the IPsec server.

        Initializes and starts the server in the current event loop.
        """
        try:
            loop = asyncio.get_running_loop()
            self.transport, self.protocol = (
                await loop.create_datagram_endpoint(
                    lambda: IPsecServerProtocol(self.config),
                    local_addr=self.server_address,
                    reuse_port=True,
                )
            )
            logger.info(f"IPsecServer started at {self.server_address}")
        except Exception as e:
            logger.error(f"Failed to start IPsec server: {e}")
            raise RuntimeError(f"Could not start server: {e}")

    async def stop(self):
        """
        Stop the IPsec server.

        Safely shuts down the server, closes all SAs, and closes the transport.
        """
        if self.transport:
            try:
                logger.info("Stopping IPsec server...")
                await self.protocol.close_all_SA()
                self.transport.close()
                logger.info("IPsec server stopped successfully.")
            except Exception as e:
                logger.error(f"Failed to stop IPsec server: {e}")
                raise RuntimeError(f"Could not stop server: {e}")
        else:
            logger.warning("No server instance to stop.")

    async def trigger_exchange(self, exchange: str, addr, **kwargs):
        """
        Trigger an exchange via the server protocol.

        :param exchange: The type of exchange to perform.
        :param addr: The address of the client.
        :param kwargs: Additional parameters for the exchange.
        """
        if self.protocol:
            result = await self.protocol.trigger_exchange(
                exchange, addr, **kwargs
            )
            return result
        else:
            logger.error("Server protocol not initialized.")
            return None

    async def update_config(self, overlay_config):
        """
        Update the server's configuration dynamically.

        :param overlay_config: The configuration overlay to apply.
        :type overlay_config: dict
        """
        await self._pause_protocol()
        self.protocol.config.update_from_dict(overlay_config)
        logger.debug("Server configuration updated")
        await self._resume_protocol()

    async def _pause_protocol(self):
        if self.protocol:
            self.protocol.paused = True
            logger.debug("Server protocol paused")

    async def _resume_protocol(self):
        if self.protocol:
            self.protocol.paused = False
            logger.debug("Server protocol resumed")
