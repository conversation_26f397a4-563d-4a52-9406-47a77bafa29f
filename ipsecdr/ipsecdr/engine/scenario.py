import yaml
import copy
from typing import Dict, Any, List
from itertools import product
from ipsecdr.utils.models import (
    GlobalConfig,
    NetworkConfig,
    PKIConfig,
    IPsecConfig,
    ESPConfig,
    IntAdapter,
    BaseModelAssign,
    IKESAConfig,
    Scenario,
    Test,
)
from ipsecdr.core.IKEv2.constants import (
    CIPHERS_TFM_ID,
    PRF_TFM_ID,
    INTEG_TFM_ID,
    DH_TFM_ID,
)

from ipsecdr.utils.logger import logger


class TestConfiguration:
    """
    A class to manage the configuration settings for an IPsecDR test.
    """

    def __init__(
        self,
        global_config: GlobalConfig,
        network_config: NetworkConfig,
        pki_config: PKIConfig,
        ipsec_config: IPsecConfig,
        esp_config: ESPConfig = None,
    ):
        self.global_config = global_config
        self.network = network_config
        self.pki = pki_config
        self.ipsec = ipsec_config
        self.esp = esp_config

    def __repr__(self):
        return (
            f"TestConfiguration(global={self.global_config}, "
            f"network={self.network}, "
            f"pki={self.pki}, "
            f"ipsec={self.ipsec})"
        )

    def update_from_dict(self, update_dict: dict):
        """
        Recursively updates the configuration attributes from a dictionary.
        Only updates existing attributes; ignores invalid keys.
        """
        for key, value in update_dict.items():
            if hasattr(self, key):
                config_section = getattr(self, key)
                if isinstance(config_section, BaseModelAssign) and isinstance(
                    value, dict
                ):
                    # Recursively update attributes within Pydantic models
                    self._update_model(config_section, value)
                else:
                    # Directly set value if it's not nested
                    setattr(self, key, value)
            else:
                logger.warning(f"Invalid configuration key: '{key}'")

    def _update_model(
        self, model_instance: BaseModelAssign, update_dict: dict
    ):
        """
        Recursively update fields of a Pydantic model instance.
        """
        for key, value in update_dict.items():
            if hasattr(model_instance, key):
                field = getattr(model_instance, key)
                if isinstance(field, BaseModelAssign) and isinstance(
                    value, dict
                ):
                    # Recursively update nested Pydantic models
                    self._update_model(field, value)
                else:
                    # Set field directly
                    setattr(model_instance, key, value)
            else:
                logger.warning(
                    f"Invalid field '{key}' in {model_instance.__class__.__name__}"
                )


class TestScenarioParser:
    def __init__(self, input_source: Any):
        """
        Initialize the TestScenarioParser with either a filepath or a configuration dictionary.

        :param input_source: Path to the test YAML file or a configuration dictionary.
        :type input_source: str or dict
        """
        logger.debug(
            f"Initializing TestScenarioParser with input_source: {input_source}"
        )
        self.default_sequence = [
            "INIT",
            "AUTH",
            "CREATE_CHILD_SA",
            "INFORMATIONAL",
        ]
        self.root_models = {
            "ipsec": IPsecConfig,
            "global_config": GlobalConfig,
            "network": NetworkConfig,
            "pki": PKIConfig,
        }

        if isinstance(input_source, str):
            # Assume it's a file path
            logger.debug(
                "Input source is a string. Attempting to parse as a file path."
            )
            self.config = self._parse_file(input_source)
        elif isinstance(input_source, dict):
            # Assume it's a configuration dictionary
            logger.debug(
                "Input source is a dictionary. Using it as configuration."
            )
            self.config = input_source
        else:
            logger.error(
                "Invalid input type for TestScenarioParser. Expected str or dict."
            )
            raise TypeError(
                "TestScenarioParser expects a file path string or a configuration dictionary."
            )

    def _parse_file(self, filepath: str) -> Dict[str, Any]:
        """
        Parse the YAML test file.

        :param filepath: Path to the test YAML file.
        :type filepath: str
        :return: The parsed YAML file as a dictionary.
        :rtype: Dict[str, Any]
        :raises FileNotFoundError: If the YAML file does not exist.
        :raises yaml.YAMLError: If there's an error in parsing the YAML file.
        """
        logger.debug(f"Attempting to parse file: {filepath}")
        try:
            with open(filepath, "r") as file:
                data = yaml.safe_load(file)
                if not data:
                    logger.error(f"No data found in the test file: {filepath}")
                    raise ValueError(
                        f"Test file '{filepath}' is empty or invalid."
                    )
                logger.debug(f"File parsed successfully: {filepath}")
                return data
        except FileNotFoundError as e:
            logger.error(f"The test file was not found: {filepath}")
            raise FileNotFoundError(
                f"Test file '{filepath}' not found."
            ) from e
        except yaml.YAMLError as e:
            logger.error(f"Error parsing YAML file: {filepath}, Error: {e}")
            raise yaml.YAMLError(
                f"Error parsing YAML file '{filepath}': {e}"
            ) from e
        except Exception as e:
            logger.error(
                f"Unexpected error while parsing file '{filepath}': {e}"
            )
            raise e

    def get_mode(self) -> str:
        return self.config.get("mode", "strict")

    def get_scenario(self) -> List[Dict[str, Any]]:
        """
        Generate scenario data based on the mode.

        :return: A list of scenario dictionaries.
        :rtype: List[Dict[str, Any]]
        """
        mode = self.get_mode()
        if mode == "strict":
            return self._parse_strict_mode()
        elif mode == "target":
            return self._parse_target_mode()
        else:
            raise ValueError(f"Unknown mode '{mode}' in test configuration.")

    def _parse_strict_mode(self) -> List[Dict[str, Any]]:
        packets = self.config.get("packets", [])
        exchanges, overlay_configs, check_functions = [], [], []
        inconsistent_sequence = False
        previous_number = 0

        for pkt in packets:
            number = IntAdapter.validate_python(pkt.get("number"))
            exchange = pkt.get("exchange")  # Can be None
            overlay_config = pkt.get("overlay_config")
            check_function = pkt.get("check_function")

            # Identify and mark inconsistent sequence if exchange number mismatches expected sequence
            if number - 1 != previous_number:
                exchange = f"{number}-{exchange}"
            if exchange and not self._is_numbered_exchange(exchange):
                if self._exchange_to_number(exchange) != number:
                    inconsistent_sequence = True
                    logger.debug("Inconsistent sequence detected")

            # Determine default exchange if not provided
            if exchange is None:
                exchange = self._default_exchange_for_step(number)
                # Handle special cases based on last exchange in list
                if exchanges:
                    last_exchange = exchanges[-1]
                    if last_exchange == "CREATE_CHILD_SA":
                        exchange = "ESP"
                    elif inconsistent_sequence:
                        previous_exch_idx = self._exchange_to_number(
                            last_exchange
                        )
                        exchange = self._default_exchange_for_step(
                            previous_exch_idx + 1
                        )
                        logger.debug(
                            f"Corrected exchange to {exchange} for inconsistency"
                        )

            # Normalize overlay_config
            if overlay_config is not None:
                overlay_config = self._normalize_overlay_config(overlay_config)

            exchanges.append(exchange)
            overlay_configs.append(overlay_config)  # Can be None
            check_functions.append(check_function)  # Can be None
            previous_number = number

        scenario_dict = {
            "exchanges": exchanges,
            "overlay_configs": overlay_configs,
            "check_functions": check_functions,
        }
        return [scenario_dict]

    def _parse_target_mode(self) -> List[Dict[str, Any]]:
        targets = self.config.get("targets", [])
        exchanges = []
        per_exchange_overlay_configs = []
        per_exchange_check_functions = []

        for target in targets:
            logger.debug(f"Target: {target}")
            exchange = target.get("exchange")
            action = target.get("action")
            check_function = target.get("check_function")
            fields = target.get("fields", [])

            exchanges.append(exchange)

            # Process overlay configs
            if action == "enum":
                field_order = []
                field_values = {}
                dependencies = {}
                for field_dict in fields:
                    for key, values in field_dict.items():
                        if not self._is_valid_field(key):
                            raise ValueError(
                                f"Invalid field '{key}' in config fields"
                            )
                        field_order.append(key)
                        if values == "any":
                            field_values[key] = "any"
                        else:
                            if not isinstance(values, list):
                                values = [values]
                            field_values[key] = values

                        # Identify dependencies
                        if key == "ipsec.ike_sa.encr_size":
                            dependencies[key] = "ipsec.ike_sa.encr"

                # Separate independent and dependent fields
                independent_fields = [
                    k for k in field_order if k not in dependencies
                ]
                dependent_fields = [
                    k for k in field_order if k in dependencies
                ]

                # Build possible values for independent fields
                independent_field_values = {}
                for key in independent_fields:
                    if field_values[key] == "any":
                        possible_values = self._get_possible_values_for_field(
                            key
                        )
                    else:
                        possible_values = field_values[key]
                    independent_field_values[key] = possible_values

                # Generate combinations of independent fields
                independent_combinations = list(
                    product(
                        *[
                            independent_field_values[key]
                            for key in independent_fields
                        ]
                    )
                )

                # Now, for each independent combination, process dependent fields
                overlay_configs = []
                for ind_combo in independent_combinations:
                    context = {}
                    resolved_config = {}
                    for idx, key in enumerate(independent_fields):
                        value = ind_combo[idx]
                        context[key] = [value]  # context expects lists
                        nested_dict = (
                            self._create_nested_dict_from_dot_notation(
                                key, value
                            )
                        )
                        resolved_config = self._merge_nested_dict(
                            resolved_config, nested_dict
                        )
                    logger.debug(
                        f"Independent combination: {ind_combo}, context: {context}"
                    )

                    # Process dependent fields with the current context
                    dependent_field_values = {}
                    for dep_field in dependent_fields:
                        _ = dependencies[dep_field]
                        dep_context = context.copy()
                        if field_values[dep_field] == "any":
                            possible_values = (
                                self._get_possible_values_for_field(
                                    dep_field, dep_context
                                )
                            )
                            logger.debug(
                                f"Possible values for '{dep_field}' given context {dep_context}: {possible_values}"
                            )
                        else:
                            possible_values = field_values[dep_field]
                        dependent_field_values[dep_field] = possible_values

                    # Generate combinations of dependent fields for this independent combination
                    dep_field_names = list(dependent_field_values.keys())
                    dep_field_values_list = list(
                        dependent_field_values.values()
                    )
                    dep_combinations = [
                        dict(zip(dep_field_names, values))
                        for values in product(*dep_field_values_list)
                    ]
                    logger.debug(
                        f"Dependent combinations for {ind_combo}: {dep_combinations}"
                    )

                    for dep_combo in dep_combinations:
                        combo_config = copy.deepcopy(
                            resolved_config
                        )  # .copy()
                        for dep_field, dep_value in dep_combo.items():
                            nested_dict = (
                                self._create_nested_dict_from_dot_notation(
                                    dep_field, dep_value
                                )
                            )
                            combo_config = self._merge_nested_dict(
                                combo_config, nested_dict
                            )
                        overlay_configs.append(combo_config)
                per_exchange_overlay_configs.append(overlay_configs)
            else:
                overlay_config = target.get("overlay_config")
                if overlay_config is not None:
                    overlay_config = self._normalize_overlay_config(
                        overlay_config
                    )
                per_exchange_overlay_configs.append([overlay_config])

            per_exchange_check_functions.append(check_function)

        # Now, per_exchange_overlay_configs is a list of lists
        # For each exchange, we have a list of overlay_configs

        # Generate all combinations across exchanges
        all_overlay_config_combinations = list(
            product(*per_exchange_overlay_configs)
        )

        # For each combination, build a scenario_dict
        scenario_dicts = []
        for overlay_combo in all_overlay_config_combinations:
            # overlay_combo is a tuple of overlay_configs, one per exchange
            overlay_configs = list(overlay_combo)
            scenario_dict = {
                "exchanges": exchanges,
                "overlay_configs": overlay_configs,
                "check_functions": per_exchange_check_functions,
            }
            scenario_dicts.append(scenario_dict)

        return scenario_dicts

    def _resolve_primitives(
        self, primitives_config: Dict[str, Any]
    ) -> Dict[str, List[str]]:
        resolved = {}

        for key, value in primitives_config.items():
            if value == "any":
                # Retrieve all available options from constants
                if key == "ENCR":
                    resolved[key] = list(CIPHERS_TFM_ID.keys())
                elif key == "PRF":
                    resolved[key] = list(PRF_TFM_ID.keys())
                elif key == "INTEG":
                    resolved[key] = list(INTEG_TFM_ID.keys())
                elif key == "DH":
                    resolved[key] = list(DH_TFM_ID.keys())
                else:
                    raise ValueError(f"Unknown primitive key '{key}'")
            elif isinstance(value, list):
                resolved[key] = value
            else:
                raise ValueError(f"Invalid value for {key}: {value}")
        return resolved

    def _default_exchange_for_step(self, number: int) -> str:
        index = number - 1
        if index < len(self.default_sequence):
            return self.default_sequence[index]
        else:
            # Return the last exchange if number exceeds the default sequence
            logger.warning(
                f"Setting exchange to {self.default_sequence[-1]} if you wanted something else than INFORMATIONAL, you must set it in scenario file using exchange: <type>"
            )
            return self.default_sequence[-1]

    def _exchange_to_number(self, exchange: str) -> int:
        for idx, e in enumerate(self.default_sequence):
            if exchange == e:
                return idx + 1
        raise ValueError(f"Exchange {exchange} not in {self.default_sequence}")

    def _is_numbered_exchange(self, exchange: str) -> bool:
        if "-" in exchange:
            num, val = exchange.split("-")
            logger.debug(f"{exchange}  -  {num}  - {val}")
            num = IntAdapter.validate_python(num)
            if type(val) is str:
                logger.debug(f"Seems to be a numbered exchange: {exchange}")
                return True
        elif exchange in self.default_sequence:
            logger.debug("This is a default exchange not numbered")
            return False
        raise ValueError(
            f"Unkown exchange, neither default or numbered {exchange}"
        )

    def _is_valid_field(self, field_path: str) -> bool:
        attrs = field_path.split(".")
        root_field = attrs[0]
        if root_field in self.root_models:
            model_class = self.root_models[root_field]
            return self._traverse_model_fields(model_class, attrs[1:])
        else:
            return False

    def _traverse_model_fields(self, model_class, attrs):
        if not attrs:
            return True
        attr = attrs[0]
        if attr in model_class.model_fields:
            field_info = model_class.model_fields[attr]
            if len(attrs) == 1:
                return True
            else:
                field_type = field_info.annotation
                if hasattr(field_type, "model_fields"):
                    return self._traverse_model_fields(field_type, attrs[1:])
                else:
                    # No further nesting possible
                    return False
        else:
            return False

    def _create_nested_dict_from_dot_notation(
        self, key: str, value: Any
    ) -> Dict[str, Any]:
        keys = key.split(".")
        nested_dict = current = {}
        for k in keys[:-1]:
            current[k] = {}
            current = current[k]
        current[keys[-1]] = value
        return nested_dict

    def _merge_nested_dict(
        self, d1: Dict[str, Any], d2: Dict[str, Any]
    ) -> Dict[str, Any]:
        for k, v in d2.items():
            if k in d1 and isinstance(d1[k], dict) and isinstance(v, dict):
                d1[k] = self._merge_nested_dict(d1[k], v)
            else:
                d1[k] = v
        return d1

    def _normalize_overlay_config(self, config):
        if isinstance(config, dict):
            new_config = {}
            for k, v in config.items():
                new_config[k] = self._normalize_overlay_config(v)
            return new_config
        elif isinstance(config, list):
            # Check if all items are dictionaries
            if all(isinstance(item, dict) for item in config):
                merged_dict = {}
                for item in config:
                    # Recursively normalize each item before merging
                    normalized_item = self._normalize_overlay_config(item)
                    merged_dict.update(normalized_item)
                return merged_dict
            else:
                # Recursively normalize each item in the list
                return [
                    self._normalize_overlay_config(item) for item in config
                ]
        else:
            return config

    def _get_possible_values_for_field(
        self, field_path: str, context: Dict[str, Any] = None
    ) -> List[Any]:
        field_parts = field_path.split(".")
        if field_parts[0] == "ipsec" and field_parts[1] == "ike_sa":
            config_class = IKESAConfig
            field_name = field_parts[2]
            possible_values_method = f"possible_{field_name}"
            if hasattr(config_class, possible_values_method):
                if field_name == "encr_size":
                    # Handle dependency on 'encr'
                    if context and "ipsec.ike_sa.encr" in context:
                        possible_encr_values = context["ipsec.ike_sa.encr"]
                        # Convert IDs to names if necessary
                        possible_encr_values = [
                            self._get_encr_name(e)
                            for e in possible_encr_values
                        ]
                        logger.debug(
                            f"Getting possible encr_size for encr values: {possible_encr_values}"
                        )
                        return getattr(config_class, possible_values_method)(
                            possible_encr_values
                        )
                    else:
                        logger.warning(
                            "Context missing 'ipsec.ike_sa.encr' for 'encr_size'"
                        )
                        return getattr(config_class, possible_values_method)()
                else:
                    return getattr(config_class, possible_values_method)()
        else:
            raise ValueError(
                f"Cannot get possible values for field '{field_path}'"
            )

    def _get_encr_name(self, encr):
        if isinstance(encr, int):
            for name, value in CIPHERS_TFM_ID.items():
                if value[0] == encr:
                    return name
            raise ValueError(f"Unknown encryption ID: {encr}")
        return encr


class TestParser:
    """
    A class to parse test files that contain multiple scenarios
    for both initiator and responder modes.
    """

    def __init__(self, filepath: str):
        """
        Initialize the TestParser with a given filepath.

        :param filepath: Path to the test YAML file containing test scenarios.
        :type filepath: str
        """
        logger.debug(f"Initializing TestParser for file: {filepath}")
        self.filepath = filepath
        self.config = self._parse_file()
        logger.debug(f"CONF: {self.config}")
        self.name = self.config.get("name", "Unnamed Test")
        self.test_scenarios = self.config.get("test_scenarios", {})
        self.initiator_scenarios: Dict[str, List[Scenario]] = {}
        self.responder_scenarios: Dict[str, List[Scenario]] = {}
        self._parse_scenarios()

    def _parse_file(self) -> Dict[str, Any]:
        """
        Parse the YAML test file.

        :return: The parsed YAML file as a dictionary.
        :rtype: Dict[str, Any]
        :raises FileNotFoundError: If the YAML file does not exist.
        :raises yaml.YAMLError: If there's an error in parsing the YAML file.
        """
        logger.debug(f"Attempting to parse file: {self.filepath}")
        try:
            with open(self.filepath, "r") as file:
                data = yaml.safe_load(file)
                if not data:
                    logger.error(
                        f"No data found in the test file: {self.filepath}"
                    )
                    raise ValueError(
                        f"Test file '{self.filepath}' is empty or invalid."
                    )
                logger.debug(f"File parsed successfully: {self.filepath}")
                return data
        except FileNotFoundError as e:
            logger.error(f"The test file was not found: {self.filepath}")
            raise FileNotFoundError(
                f"Test file '{self.filepath}' not found."
            ) from e
        except yaml.YAMLError as e:
            logger.error(
                f"Error parsing YAML file: {self.filepath}, Error: {e}"
            )
            raise yaml.YAMLError(
                f"Error parsing YAML file '{self.filepath}': {e}"
            ) from e
        except Exception as e:
            logger.error(
                f"Unexpected error while parsing file '{self.filepath}': {e}"
            )
            raise e

    def _parse_scenarios(self):
        """
        Parse the test scenarios for both initiator and responder modes.
        """
        logger.debug("Parsing test_scenarios from configuration.")
        if not isinstance(self.test_scenarios, dict):
            logger.error("Expected 'test_scenarios' to be a dictionary.")
            raise TypeError(
                "The 'test_scenarios' field must be a dictionary in the test file."
            )

        # Validate and parse initiator scenarios
        initiator_scenarios = self.test_scenarios.get("initiator", [])
        if not isinstance(initiator_scenarios, list):
            logger.error("Expected 'initiator' scenarios to be a list.")
            raise TypeError(
                "The 'initiator' scenarios must be defined as a list."
            )
        self._parse_role_scenarios(initiator_scenarios, role="initiator")

        # Validate and parse responder scenarios
        responder_scenarios = self.test_scenarios.get("responder", [])
        if not isinstance(responder_scenarios, list):
            logger.error("Expected 'responder' scenarios to be a list.")
            raise TypeError(
                "The 'responder' scenarios must be defined as a list."
            )
        self._parse_role_scenarios(responder_scenarios, role="responder")

    def _parse_role_scenarios(self, scenarios: list, role: str):
        """
        Parse scenarios for a given role (initiator or responder).

        :param scenarios: A list of scenarios to parse.
        :type scenarios: list
        :param role: The role for which the scenarios are defined ('initiator' or 'responder').
        :type role: str
        """
        logger.debug(f"Parsing {role} DEBUG {scenarios}.")
        for scenario in scenarios:
            if not isinstance(scenario, dict):
                logger.error(
                    f"A scenario definition in {role} scenarios is not a dictionary: {scenario}"
                )
                raise ValueError(
                    f"Each scenario definition in {role} scenarios must be a dictionary."
                )

            for scenario_name, scenario_def in scenario.items():
                logger.debug(
                    f"Processing scenario '{scenario_name}' for role '{role}'."
                )
                if not scenario_name:
                    logger.error("A scenario is missing a scenario name.")
                    raise ValueError(
                        f"A scenario in {role} scenarios is missing a name."
                    )

                if not scenario_def:
                    logger.error(
                        f"No scenario definition provided for '{scenario_name}'."
                    )
                    raise ValueError(
                        f"Scenario '{scenario_name}' in {role} scenarios is empty or invalid."
                    )

                try:
                    # Parse the scenario using TestScenarioParser
                    logger.debug(
                        f"Initiating parsing of scenario '{scenario_name}' with scenario definition: {scenario_def}"
                    )
                    # Pass the configuration dictionary directly to TestScenarioParser
                    parser = TestScenarioParser(scenario_def)
                    scenario_data_list = parser.get_scenario()

                    # Convert each scenario dictionary into a Scenario model instance
                    scenario_objects = []
                    for data in scenario_data_list:
                        logger.debug(
                            f"Validating scenario data for '{scenario_name}': {data}"
                        )
                        scenario_obj = Scenario(**data)
                        scenario_objects.append(scenario_obj)
                    if role == "initiator":
                        self.initiator_scenarios[scenario_name] = (
                            scenario_objects
                        )
                    else:
                        self.responder_scenarios[scenario_name] = (
                            scenario_objects
                        )
                    logger.info(
                        f"Loaded {len(scenario_objects)} scenario(s) for '{scenario_name}' under role '{role}'."
                    )
                except Exception as e:
                    logger.error(
                        f"Error parsing scenario '{scenario_name}' in {role} scenarios: {e}"
                    )
                    raise ValueError(
                        f"Error parsing scenario '{scenario_name}' in {role} scenarios: {e}"
                    )

    def get_initiator_scenarios(self) -> Dict[str, List[Scenario]]:
        """
        Get the parsed initiator scenarios.

        :return: Dictionary of scenario names to a list of Scenario objects for the initiator.
        :rtype: Dict[str, List[Scenario]]
        """
        logger.debug("Retrieving initiator scenarios.")
        return self.initiator_scenarios

    def get_responder_scenarios(self) -> Dict[str, List[Scenario]]:
        """
        Get the parsed responder scenarios.

        :return: Dictionary of scenario names to a list of Scenario objects for the responder.
        :rtype: Dict[str, List[Scenario]]
        """
        logger.debug("Retrieving responder scenarios.")
        return self.responder_scenarios

    def get_test(self) -> Test:
        """
        Construct and return a Test model instance representing the entire test configuration.

        :return: A Test model instance.
        :rtype: Test
        """
        if not self.initiator_scenarios and not self.responder_scenarios:
            logger.error(
                "No scenarios found for either initiator or responder."
            )
            raise ValueError(
                "No scenarios found for either initiator or responder modes."
            )
        logger.debug(f"Constructing Test model for '{self.name}'.")
        return Test(
            name=self.name,
            initiator_scenarios=self.initiator_scenarios,
            responder_scenarios=self.responder_scenarios,
        )
