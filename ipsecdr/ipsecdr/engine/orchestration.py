import asyncio
import os
from typing import Dict, List
from pathlib import Path

from ipsecdr.engine.tester import Tester
from ipsecdr.engine.checker import Checker
from ipsecdr.engine.scenario import TestConfiguration, TestParser
from ipsecdr.utils.models import Summary, Outcome, Test, TestPoolEntry
from ipsecdr.utils.logger import logger

logger = logger


class Orchestrator:
    """
    Orchestrates the execution of tests by managing Testers and Checkers.

    The Orchestrator is responsible for:
    - Maintaining a table of test structures.
    - Assigning Testers and Checkers to tests.
    - Building configurations and scenarios from a test list and a generic configuration.
    - Displaying and saving results.
    - Dumping test data, including packets and keys, for further analysis.

    Attributes:
        config (TestConfiguration): The global configuration parsed from configuration files and CLI arguments.
        test_pool (Dict[int, Dict[str, Any]]): A dictionary maintaining the state and data of each test.
        max_testers (int): The maximum number of concurrent testers.
        max_checkers (int): The maximum number of concurrent checkers.
        mode (str): The mode in which to run the orchestrator ('client' or 'server').
    """

    def __init__(
        self,
        config: TestConfiguration,
        test_list: List[str],
        max_testers: int = 1,
        max_checkers: int = 1,
        mode: str = None,
    ):
        """
        Initialize the Orchestrator with global configuration and test list.

        :param config: The global configuration object.
        :type config: TestConfiguration
        :param test_list: A list of test names to be executed.
        :type test_list: List[str]
        :param max_testers: Maximum number of concurrent testers, defaults to 1.
        :type max_testers: int, optional
        :param max_checkers: Maximum number of concurrent checkers, defaults to 1.
        :type max_checkers: int, optional
        :param mode: The mode in which to run the orchestrator ('client' or 'server').
        :type mode: str
        """
        self.config = config
        self.test_pool: Dict[str, Dict[int, TestPoolEntry]] = {}
        self.summary = Summary()
        self.max_testers = max_testers
        self.max_checkers = max_checkers
        self.mode = mode
        self.REPOSITORY_ROOT = Path(__file__).resolve().parents[2]

        self._build_test_pool(test_list)

    def _get_next_test_id(self, test_name: str) -> int:
        """
        Generate a new test_id for a given test name by counting existing entries.
        """
        if test_name not in self.test_pool:
            return 1
        return max(self.test_pool[test_name].keys()) + 1

    def _build_test_pool(self, test_list: List[str]) -> None:
        """
        Build the test pool from the test list and configurations.
        """
        logger.info("Building test pool...")
        test_id = 0

        for test_name in test_list:
            # Load scenario and overlay configuration for each test
            test_obj = self._load_test_configuration(test_name)
            # Initialize test structure
            if self.mode is not None:
                for (
                    scenario_name,
                    scenarios,
                ) in getattr(test_obj, f"{self.mode}_scenarios").items():
                    for idx, scenario in enumerate(scenarios, start=1):
                        test_id = self._get_next_test_id(test_obj.name)
                        test_entry = TestPoolEntry(
                            test_id=test_id,
                            test_name=test_obj.name,
                            scenario_name=scenario_name,
                            scenario=scenario,
                            scenario_count=len(scenarios),
                            mode=(
                                "initiator" if self.mode is None else self.mode
                            ),
                        )
                        if test_obj.name not in self.test_pool:
                            self.test_pool[test_obj.name] = {}
                        self.test_pool[test_obj.name][test_id] = test_entry
                        logger.debug(
                            f"Added initiator scenario test {test_id}: {test_obj.name}"
                        )
                continue

            for (
                scenario_name,
                scenarios,
            ) in test_obj.initiator_scenarios.items():
                for idx, scenario in enumerate(scenarios, start=1):
                    test_id = self._get_next_test_id(test_obj.name)
                    test_entry = TestPoolEntry(
                        test_id=test_id,
                        test_name=test_obj.name,
                        scenario_name=scenario_name,
                        scenario=scenario,
                        scenario_count=len(scenarios),
                        mode="initiator" if self.mode is None else self.mode,
                    )
                    if test_obj.name not in self.test_pool:
                        self.test_pool[test_obj.name] = {}
                    self.test_pool[test_obj.name][test_id] = test_entry
                    logger.debug(
                        f"Added initiator scenario test {test_id}: {test_obj.name}"
                    )

            for (
                scenario_name,
                scenarios,
            ) in test_obj.responder_scenarios.items():
                for idx, scenario in enumerate(scenarios, start=1):
                    test_id = self._get_next_test_id(test_obj.name)
                    test_entry = TestPoolEntry(
                        test_id=test_id,
                        test_name=test_obj.name,
                        scenario_name=scenario_name,
                        scenario=scenario,
                        scenario_count=len(scenarios),
                        mode="responder",
                    )
                    if test_obj.name not in self.test_pool:
                        self.test_pool[test_obj.name] = {}
                    self.test_pool[test_obj.name][test_id] = test_entry
                    logger.debug(
                        f"Added responder scenario test {test_id}: {test_obj.name}"
                    )

    def _load_test_configuration(self, test_file_name: str) -> Test:
        """
        Load the scenario and overlay configuration for a given test using the new TestParser.
        """
        # Load scenario using the updated TestParser
        scenario_file = (
            self.REPOSITORY_ROOT
            / "ipsecdr"
            / "engine"
            / "tests"
            / f"{test_file_name}.yml"
        )
        if not os.path.exists(scenario_file):
            logger.error(
                f"Test file {test_file_name} not found at {scenario_file}"
            )
            raise FileNotFoundError(f"Test file {test_file_name} not found.")

        test_parser = TestParser(str(scenario_file))
        test_obj = test_parser.get_test()
        return test_obj

    async def _assign_testers_and_checkers(self):
        """
        Assign testers and checkers to tests in the test pool based on availability.
        """
        tester_semaphore = asyncio.Semaphore(self.max_testers)
        checker_semaphore = asyncio.Semaphore(self.max_checkers)

        tasks = []

        for test_name, test_entries in self.test_pool.items():
            for test_id, test_entry in test_entries.items():
                if test_entry.lock_status == 0:
                    test_entry.lock_status = 1  # In progress
                    scenario = test_entry.scenario
                    if scenario is None:
                        logger.error(
                            f"No scenario found for test {test_entry.test_id} - {test_entry.test_name}"
                        )
                        continue
                    packet_pipe = asyncio.Queue()

                    tester = Tester(
                        test_id=test_entry.test_id,
                        config=self.config,
                        scenario=scenario,
                        packet_pipe=packet_pipe,
                        mode=test_entry.mode,
                    )

                    test_entry.tester = tester
                    test_entry.packet_pipe = packet_pipe
                    tasks.append(
                        self._run_tester_and_checker(
                            tester,
                            tester_semaphore,
                            checker_semaphore,
                            test_entry,
                        )
                    )
                else:
                    continue  # Test already started or completed

        await asyncio.gather(*tasks)

    async def _run_tester_and_checker(
        self,
        tester: Tester,
        tester_semaphore: asyncio.Semaphore,
        checker_semaphore: asyncio.Semaphore,
        test_entry: TestPoolEntry,
    ):
        """
        Run a tester and checker for a test within the context of semaphores to limit concurrency.
        """
        test_id = tester.test_id
        async with tester_semaphore:
            logger.info(f"Starting tester for test {test_id}")
            try:
                await tester.run()
                test_entry.lock_status = 3  # Execution ended
                logger.info(f"Tester for test {test_id} completed")
            except Exception as e:
                logger.error(f"Tester for test {test_id} failed: {e}")
                test_entry.lock_status = 5  # Failed
                test_entry.outcome = Outcome(
                    test_id=test_id,
                    test_name=test_entry.test_name,
                    result="Failed",
                    reason=str(e),
                )
                return

        # After tester completes, assign checker
        checker = Checker(
            test_id=test_id,
            config=self.config,
            packet_pipe=test_entry.packet_pipe,
            check_functions=tester.check_functions,  # Pass check functions from scenario
            mode=test_entry.mode,
            test_name=test_entry.test_name,
        )
        test_entry.checker = checker
        test_entry.lock_status = 4  # In checking

        async with checker_semaphore:
            logger.info(f"Starting checker for test {test_id}")
            try:
                await checker.run()
                test_entry.lock_status = 5  # Done
                test_entry.outcome = checker.outcome
                self.summary.add_outcome(checker.outcome)
                logger.info(f"Checker for test {test_id} completed")
            except Exception as e:
                logger.error(f"Checker for test {test_id} failed: {e}")
                test_entry.lock_status = 5  # Failed
                error_outcome = Outcome(
                    test_id=test_id,
                    test_name=test_entry.test_name,
                    result="Failed",
                    reason=str(e),
                )
                test_entry.outcome = error_outcome
                self.summary.add_outcome(error_outcome)

    def display_results(self, dump_results: bool = True):
        """
        Display the results of all tests.
        """
        logger.info("\nTest Results:")
        logger.info(self.summary.pretty_print())
        if not dump_results:
            return
        with open("tests-summary.json", "w") as f:
            f.write(self.summary.model_dump_json())

    def save_results(self, filepath: str):
        """
        Save the test results to a file.
        """
        logger.info(f"Saving results to {filepath}")
        try:
            import json

            serializable_test_pool = {}
            for test_name, test_entries in self.test_pool.items():
                serializable_test_pool[test_name] = {}
                for test_id, test_entry in test_entries.items():
                    serializable_test_pool[test_name][test_id] = (
                        test_entry.dict(
                            exclude={"packet_pipe", "tester", "checker"}
                        )
                    )
            with open(filepath, "w") as f:
                json.dump(serializable_test_pool, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save results: {e}")

    def dump_test_data(self, output_dir: str):
        """
        Dump the test data, including packets and keys, to the specified directory.
        """
        logger.info(f"Dumping test data to {output_dir}")
        for test_name, test_entries in self.test_pool.items():
            for test_id, test_entry in test_entries.items():
                tester = test_entry.tester
                if tester:
                    tester.dump_packets(output_dir)
                    tester.dump_keys(output_dir)
                else:
                    logger.warning(
                        f"No tester data available for test {test_id}"
                    )

    async def run(self):
        """
        Run the orchestrator to execute tests, assign testers and checkers, and collect results.
        """
        await self._assign_testers_and_checkers()
        self.display_results()
        logger.debug(
            f"Orchestrator {self.mode} mode run() method has completed."
        )
