import asyncio
import secrets
import binascii
from typing import List, Optional
from scapy.compat import raw
from scapy.packet import Packet
from scapy.contrib.ikev2 import IKEv2, IKEv2_Delete, IKEv2_Notify, IKEv2_CP
from scapy.layers.inet import IP, UDP, ICMP
from scapy.layers.ipsec import ESP, SecurityAssociation
from ipsecdr.core.crypto.ikev2_crypto import IKEv2Algorithm
from ipsecdr.core.IKEv2.constants import TRANSFORMS_TYPE
from ipsecdr.core.IKEv2.init import forge_ikev2_init
from ipsecdr.core.IKEv2.auth import forge_ikev2_auth
from ipsecdr.core.IKEv2.create_child import (
    forge_ikev2_create_child_sa,
)
from ipsecdr.core.IKEv2.informational import forge_ikev2_informational
from ipsecdr.core.IKEv2.utils import (
    get_cookie,
    check_for_any_notify,
    get_notify_name,
    set_ikev2_crypto,
    set_ikev2_keys,
    uncipher_ike_pkt,
    set_ikev2_keys_child,
)
from ipsecdr.core.IKEv2.constants import (
    get_cipher_tfm_name,
    get_integ_tfm_name,
    get_scapy_encr_tfm_name,
    get_scapy_integ_tfm_name,
)
from ipsecdr.core.crypto.ikev2_crypto import ChildAlgorithm
from ipsecdr.utils.models import NotifyPayload
from ipsecdr.utils.logger import logger


class IPsecClientProtocol(asyncio.DatagramProtocol):
    """
    A class to handle the asynchronous UDP client communication for IPsec.

    This class manages sending and receiving datagrams for an IPsec client using
    asyncio's DatagramProtocol.

    Attributes:
        on_con_lost (asyncio.Future): A future used to detect when the connection is lost.
        received_data_queue (asyncio.Queue): A queue for received data.
    """

    def __init__(self, on_con_lost, received_data_queue):
        """
        Initialize the IPsecClientProtocol.

        :param on_con_lost: A future used to detect when the connection is lost.
        :type on_con_lost: asyncio.Future
        :param received_data_queue: A queue to store received data packets.
        :type received_data_queue: asyncio.Queue
        """
        self.on_con_lost = on_con_lost
        self.received_data_queue = received_data_queue

    def connection_made(self, transport):
        """
        Called when the connection is established.

        :param transport: The transport for sending and receiving packets.
        :type transport: asyncio.DatagramTransport
        """
        self.transport = transport
        logger.debug("Client connection made")

    def datagram_received(self, data, addr):
        """
        Called when a datagram is received.

        :param data: The data received in the datagram.
        :type data: bytes
        :param addr: The address from which the datagram was received.
        :type addr: tuple
        """
        logger.debug(f"Client received {len(data)} bytes from {addr}")
        self.received_data_queue.put_nowait((data, addr))

    def error_received(self, exc):
        """
        Called when an error occurs while receiving a datagram.

        :param exc: The exception received during the error.
        :type exc: Exception
        """
        logger.error(f"Client error received: {exc}")

    def connection_lost(self, exc):
        """
        Called when the connection is lost or closed.

        :param exc: The exception that caused the connection to be lost, if any.
        :type exc: Exception or None
        """
        logger.info("Client connection closed")
        self.on_con_lost.set_result(True)


class IPsecClient:
    """
    A class to represent the IPsec client for managing the communication and
    key exchange over an IKEv2 protocol.

    This class handles the initialization, authentication, child SA creation,
    and informational exchanges with an IPsec server.

    Attributes:
        config (TestConfiguration): The main configuration for the client.
        overlay_config (dict, optional): Additional configuration to overlay on top of the base config.
        loop (asyncio.AbstractEventLoop): The event loop used for asynchronous tasks.
        transport (asyncio.DatagramTransport): The transport for UDP communication.
        protocol (IPsecClientProtocol): The protocol for handling datagram communication.
        discussion (list): A list to store exchanged messages.
        ikev2_crypto (IKEv2Algorithm): The cryptographic algorithms for the IKEv2 exchange.
        ikev2_keys (IKEv2KeyData): The derived key data for the IKEv2 exchange.
        received_data_queue (asyncio.Queue): A queue to store incoming data.
        remote_addr (tuple): The IP address and port of the remote server.
        local_addr (tuple): The IP address and port of the client.
        spi_i (bytes): The initiator SPI value.
        spi_r (bytes): The responder SPI value.
        MID (int): The message ID counter.
        childs (list): A list of child SAs.
    """

    def __init__(self, config, overlay_config=None):
        """
        Initialize the IPsecClient with a configuration and optional overlay config.

        :param config: The base configuration for the client.
        :type config: TestConfiguration
        :param overlay_config: An optional dictionary to overlay additional configuration, defaults to None.
        :type overlay_config: dict, optional
        """
        self.config = config
        if overlay_config:
            self.config.update_from_dict(overlay_config)
        self.transport = None
        self.protocol = None
        self.discussion = []  # Store messages exchanged
        self.ikev2_crypto = None  # IKEv2Algorithm runtime object
        self.ikev2_keys = None  # IKEv2KeyData runtime object
        self.received_data_queue = asyncio.Queue()
        self.remote_addr = (
            str(self.config.network.ipsec_dst),
            self.config.network.port_dst,
        )
        self.local_addr = (
            str(self.config.network.ipsec_src),
            self.config.network.port_src,
        )
        self.spi_i = None  # Initiator SPI
        self.spi_r = None  # Responder SPI
        self.MID = 0  # Message counter
        self.childs = {}  # Child SA list
        self.exchange_complete = asyncio.Event()
        self.init_retry = 0

    async def start(self):
        """
        Start the IPsec client by establishing a UDP connection.

        This method sets up the UDP transport and protocol for sending and receiving
        datagrams over the network. It checks whether nat_t config is set or not, if it is
        it hook stransport.sendto to send a NON-ESP-MARKER.
        """
        logger.debug(
            f"Setting datagram endpoint with:\nremote_addr={self.remote_addr},local_addr={self.local_addr},"
        )
        loop = asyncio.get_running_loop()
        on_con_lost = loop.create_future()
        self.transport, self.protocol = await loop.create_datagram_endpoint(
            lambda: IPsecClientProtocol(on_con_lost, self.received_data_queue),
            remote_addr=self.remote_addr,
            local_addr=None,  # self.local_addr,
            #            reuse_port=True,
        )
        if self.config.ipsec.nat_t.nat_t:
            original_sendto = self.transport.sendto

            def sendto_with_marker(data):
                # Add non-ESP marker before the IKEv2 packet
                if isinstance(data, bytes) and len(data) >= 4:
                    logger.debug("Adding Non-ESP Marker before IKEv2 packet.")
                    data = b"\x00" * 4 + data  # Prepend the non-ESP marker
                original_sendto(data)  # Call the original sendto

            # Replace the transport's sendto with our modified version
            self.transport.sendto = sendto_with_marker
            self.sendto_raw = original_sendto

    async def stop(self):
        """
        Stop the IPsec client by closing the transport.
        """
        self.transport.close()

    async def ikev2_init(
        self,
        cookie: bytes = None,
        notify_extras: Optional[List[NotifyPayload]] = None,
        max_retry: int = 3,
    ) -> bool:
        """
        Perform the IKE_SA_INIT exchange to establish an IKE Security Association.

        :param cookie: A cookie value to include in the exchange, defaults to None.
        :type cookie: bytes, optional
        :param notify_extras: A list of notifications to send with the IKE_SA_INIT, defaults to None.
        :type notify_extras: Optional[List[NotifyPayload]]
        :return: True if the exchange is successful, False otherwise.
        :rtype: bool
        """
        self.spi_i = secrets.token_bytes(8)
        self.spi_r = 0
        self.ikev2_crypto = IKEv2Algorithm(
            choix_dh=self.config.ipsec.ike_sa.groupdesc[0],
        )
        if not hasattr(self.ikev2_crypto.dh, "public_key"):
            logger.warning(
                "CLIENT-INIT: Diffie Helman choice\
                     does not have a public key implemented, \
                    behaviour might be errored."
            )
            key_exchange = None
        else:
            key_exchange = self.ikev2_crypto.dh.public_key
        nonce_i = secrets.token_bytes(32)

        init_i_pkt = forge_ikev2_init(
            mode="Initiator",
            spi_i=self.spi_i,
            spi_r=0,
            ciphers=self.config.ipsec.ike_sa.encr,
            key_lengths=self.config.ipsec.ike_sa.encr_size,
            prfs=self.config.ipsec.ike_sa.prf,
            integrities=self.config.ipsec.ike_sa.integ,
            groupdescs=self.config.ipsec.ike_sa.groupdesc,
            nonce=nonce_i,
            cookie=None,
            KE=key_exchange,
            notify_extras=notify_extras,
            mid=self.MID,
            childless=self.config.ipsec.childless,
        )

        # Send packet
        raw_packet = bytes(init_i_pkt)
        logger.warning(f"CLIENT-INIT:  {IKEv2(raw_packet)}")
        self.transport.sendto(raw_packet)
        logger.debug(
            f"CLIENT-INIT: sent IKE_SA_INIT {self.MID} to {self.remote_addr}"
        )
        try:
            response_data, addr = await asyncio.wait_for(
                self.received_data_queue.get(), timeout=5
            )
            if not isinstance(IKEv2(response_data), IKEv2):
                logger.info(
                    "CLIENT-INIT: WARNING: Invalid Network Packet Received"
                )
                logger.info("Continue ...")
            logger.debug(
                f"CLIENT-INIT: received IKE_SA_INIT response from {addr}"
            )
            if response_data[:4] == b"\x00" * 4:
                response_data = response_data[4:]
            self.discussion.append(
                {"initiator": init_i_pkt, "responder": IKEv2(response_data)}
            )
            self.exchange_complete.set()
            notify_found = check_for_any_notify(
                self.discussion[self.MID]["responder"]
            )
            for id in notify_found:
                logger.info(
                    f"CLIENT-INIT: A notify {get_notify_name(id)}\
                         was encountered."
                )
                if id == 16390:  # COOKIE

                    return self.ikev2_init(
                        cookie=get_cookie(
                            self.discussion[self.MID]["initiator"]
                        ),
                        notify_extras=notify_extras,
                    )
            # Extract nonce_r and KE_r from the response
            try:
                self.discussion[self.MID]["responder"]["IKEv2"]["IKEv2 SA"]
            except (ValueError, IndexError):
                logger.warning(
                    "CLIENT-INIT: ERROR: No IKE_SA found in packet."
                )
                logger.info("IKE_INIT Failed")
                return True
            self.spi_r = self.discussion[self.MID]["responder"].resp_SPI

            # Derive shared secret
            # Derive keys
            # Set keys
            self.ikev2_crypto = set_ikev2_crypto(
                ikev2_pkt=self.discussion[-1]["responder"],
                ikev2_crypto=self.ikev2_crypto,
            )
            self.ikev2_keys = set_ikev2_keys(
                ke=self.discussion[-1]["responder"]["IKEv2"]["IKEv2_KE"].ke,
                nonce_i=nonce_i,
                nonce_r=self.discussion[-1]["responder"]["IKEv2"][
                    "IKEv2_Nonce"
                ].nonce,
                spi_i=self.spi_i,
                spi_r=self.spi_r,
                ikev2_crypto=self.ikev2_crypto,
                pub_cert=self.config.pki.pub_key,
                key_cert=self.config.pki.prv_key,
                trust_chain=self.config.pki.trust_chain,
            )
            self.ikev2_keys.show_key()
            self._increase_MID()
            return True
        except asyncio.TimeoutError:
            logger.warning(
                "CLIENT-INIT: timeout waiting for IKE_SA_INIT response client return False"
            )
            if self.init_retry == max_retry:
                return False
            self.init_retry += 1
            logger.warning(f"Retry {self.init_retry} on {max_retry}")
            await self.ikev2_init(
                cookie=cookie, notify_extras=notify_extras, max_retry=max_retry
            )

    async def ikev2_auth(
        self,
        notify_options: Optional[List[NotifyPayload]] = None,
        notify_extras: Optional[List[NotifyPayload]] = None,
    ) -> bool:
        """
        Perform the IKE_AUTH exchange to authenticate and establish the IKE SA.

        :param notify_options: A list of optional notifications to send with the IKE_AUTH, defaults to None.
        :type notify_options: Optional[List[NotifyPayload]], optional
        :param notify_extras: A list of notifications to send with the IKE_AUTH, defaults to None.
        :type notify_extras: Optional[List[NotifyPayload]], optional
        :return: True if the exchange is successful, False otherwise.
        :rtype: bool
        """
        if not self.ikev2_crypto or not self.ikev2_keys:
            logger.error("IKEv2 Crypto or Keys not initialized")
            return False
        self.exchange_complete.clear()
        traffic_selector_client = [
            self.config.ipsec.ts.tsi_ip_range[0],
            self.config.ipsec.ts.tsi_ip_range[1],
            self.config.ipsec.ts.tsi_port_range[0],
            self.config.ipsec.ts.tsi_port_range[1],
        ]
        traffic_selector_server = [
            self.config.ipsec.ts.tsr_ip_range[0],
            self.config.ipsec.ts.tsr_ip_range[1],
            self.config.ipsec.ts.tsr_port_range[0],
            self.config.ipsec.ts.tsr_port_range[1],
        ]

        auth_i_pkt, auth_i_pkt_clear = forge_ikev2_auth(
            mode="Initiator",
            spi_i=self.spi_i,
            spi_r=self.spi_r,
            mid=self.MID,
            ikev2_crypto=self.ikev2_crypto,
            ikev2_keys=self.ikev2_keys,
            esn=self.config.ipsec.child_sa.esn,
            notify_options=notify_options,
            idi_type=self.config.ipsec.id.idi_type,
            idi_data=self.config.ipsec.id.idi_data,
            idr_type=self.config.ipsec.id.idr_type,
            idr_data=self.config.ipsec.id.idr_data,
            traffic_selector_client=traffic_selector_client,
            traffic_selector_server=traffic_selector_server,
            init_i_pkt=self.discussion[-1]["initiator"],
            init_r_pkt=self.discussion[-1]["responder"],
            auth_method=self.config.ipsec.auth.auth_method,
            nat_t=False,
            notify_extras=notify_extras,
            childless=self.config.ipsec.childless,
            include_idr=self.config.ipsec.include_idr,
            include_initial_contact=self.config.ipsec.include_init_contact,
            return_clear=True,
        )

        # Send the packet
        raw_packet = bytes(auth_i_pkt)
        self.transport.sendto(raw_packet)
        logger.debug(f"Client sent IKE_AUTH to {self.remote_addr}")

        # Wait for response
        try:
            response_data, addr = await asyncio.wait_for(
                self.received_data_queue.get(), timeout=10
            )
            # Parse the response
            if response_data[:4] == b"\x00" * 4:
                response_data = response_data[4:]
            auth_r_pkt = IKEv2(response_data)
            logger.debug(f"Client received IKE_AUTH response from {addr}")

            self._increase_MID()
            # Decrypt the response
            clear_pkt_r = uncipher_ike_pkt(
                ikev2_crypto=self.ikev2_crypto,
                ikev2_keys=self.ikev2_keys,
                ike_packet=auth_r_pkt,
            )
            self.discussion.append(
                {
                    "initiator": auth_i_pkt,
                    "decrypted_initiator": auth_i_pkt_clear,
                    "responder": auth_r_pkt,
                    "decrypted_responder": clear_pkt_r,
                }
            )
            self.exchange_complete.set()
            # TODO parse the AUTH response
            return True
        except asyncio.TimeoutError:
            logger.error("Client timeout waiting for IKE_AUTH response")
            return False

    async def ikev2_create_child_sa(
        self,
        notify_options: Optional[List[NotifyPayload]] = None,
        notify_extras: Optional[List[NotifyPayload]] = None,
    ) -> bool:
        """
        Perform the CREATE_CHILD_SA exchange to establish a child Security Association.

        :param notify_options: A list of optional notifications for the exchange, defaults to None.
        :type notify_options: Optional[List[NotifyPayload]], optional
        :param notify_extras: A list of notifications to send with the exchange, defaults to None.
        :type notify_extras: Optional[List[NotifyPayload]], optional
        :return: True if the exchange is successful, False otherwise.
        :rtype: bool
        """
        if not self.ikev2_crypto or not self.ikev2_keys:
            logger.error("IKEv2 Crypto or Keys not initialized")
            return False
        self.exchange_complete.clear()
        traffic_selector_client = [
            self.config.ipsec.ts.tsi_ip_range[0],
            self.config.ipsec.ts.tsi_ip_range[1],
            self.config.ipsec.ts.tsi_port_range[0],
            self.config.ipsec.ts.tsi_port_range[1],
        ]
        traffic_selector_server = [
            self.config.ipsec.ts.tsr_ip_range[0],
            self.config.ipsec.ts.tsr_ip_range[1],
            self.config.ipsec.ts.tsr_port_range[0],
            self.config.ipsec.ts.tsr_port_range[1],
        ]

        child_spi_i = secrets.token_bytes(4)
        nonce_i = secrets.token_bytes(32)

        cchild_sa_pkt_i, cchild_sa_pkt_i_clear = forge_ikev2_create_child_sa(
            mode="Initiator",
            spi_i=self.spi_i,
            spi_r=self.spi_r,
            mid=self.MID,
            ikev2_crypto=self.ikev2_crypto,
            ikev2_keys=self.ikev2_keys,
            traffic_selector_client=traffic_selector_client,
            traffic_selector_server=traffic_selector_server,
            ciphers=self.config.ipsec.child_sa.encr,
            key_lengths=self.config.ipsec.child_sa.encr_size,
            prfs=None,
            integrities=self.config.ipsec.child_sa.integ,
            groupdescs=self.config.ipsec.child_sa.groupdesc,
            nonce=nonce_i,
            KE=self.ikev2_crypto.dh.public_key,
            esn=self.config.ipsec.child_sa.esn,
            notify_options=notify_options,
            notify_extras=notify_extras,
            cspi=child_spi_i,
            return_clear=True,
        )
        raw_packet = bytes(cchild_sa_pkt_i)
        self.transport.sendto(raw_packet)
        logger.debug(f"Client sent CREATE_CHILD_SA to {self.remote_addr}")

        try:
            response_data, addr = await asyncio.wait_for(
                self.received_data_queue.get(), timeout=10
            )
            if response_data[:4] == b"\x00" * 4:
                response_data = response_data[4:]
            cchild_sa_pkt_r = IKEv2(response_data)
            logger.debug(
                f"Client received CREATE_CHILD_SA response from {addr}"
            )
            self._increase_MID()
            clear_cchild_sa_pkt_r = uncipher_ike_pkt(
                self.ikev2_crypto, self.ikev2_keys, ike_packet=cchild_sa_pkt_r
            )
            self.discussion.append(
                {
                    "initiator": cchild_sa_pkt_i,
                    "decrypted_initiator": cchild_sa_pkt_i_clear,
                    "responder": cchild_sa_pkt_r,
                    "decrypted_responder": clear_cchild_sa_pkt_r,
                }
            )
            self.exchange_complete.set()
            try:
                clear_cchild_sa_pkt_r["IKEv2_SA"]["IKEv2_Proposal"][
                    "IKEv2_Transform"
                ]
                logger.info("Create Child OK")
            except (ValueError, IndexError):
                logger.info(
                    "Erreur la TOE n'a pas correctement répondu\
                         au paquet init_create_child"
                )
                logger.info("Verification interrompue")
                logger.info(
                    f"SPIs:\nspi_i:{binascii.hexlify(self.spi_i)}\n\
                        spi_r:{binascii.hexlify(self.spi_r)}"
                )
                self.ikev2_keys.show_key()
                return False

            child_spi_r = clear_cchild_sa_pkt_r["IKEv2_SA"][
                "IKEv2_Proposal"
            ].SPI
            child_nonce_r = clear_cchild_sa_pkt_r["IKEv2_Nonce"].nonce
            # HERE CHANGE security association handling in order
            #  to use scapy ipsec SA class

            choix_chiffrement = None
            key_size = None
            choix_integrite = None
            esn = 0
            for transform in clear_cchild_sa_pkt_r["IKEv2_SA"][
                "IKEv2_Transform"
            ].iterpayloads():
                if transform.transform_type == TRANSFORMS_TYPE["Encryption"]:
                    choix_chiffrement = transform.transform_id
                    key_size = transform.key_length
                elif transform.transform_type == TRANSFORMS_TYPE["Integrity"]:
                    choix_integrite = transform.transform_id
                elif (
                    transform.transform_type
                    == TRANSFORMS_TYPE["Extended_Sequence_Number"]
                ):
                    esn = transform.transform_id
                elif transform.transform_type == TRANSFORMS_TYPE["GroupDesc"]:
                    choix_dh = transform.transform_id
            child_crypto = ChildAlgorithm()
            child_crypto.Set_dh(choix_dh)
            child_crypto.set_other_algo(
                choix_chiffrement,
                key_size,
                choix_integrite,
                esn,
            )

            child_keys = set_ikev2_keys_child(
                nonce_child_init=nonce_i,
                nonce_child_resp=child_nonce_r,
                key_sk_d=self.ikev2_keys.sk_d,
                decrypted_packet_with_SAChild=clear_cchild_sa_pkt_r,
                child_algorithm_data=child_crypto,
                ikev2_algorithm_data=self.ikev2_crypto,
            )

            # Map transform IDs to algorithm names for SecurityAssociation
            # TODO Handle multiple encr choice
            crypt_algo_name = get_scapy_encr_tfm_name(
                get_cipher_tfm_name(choix_chiffrement)
            )
            auth_algo_name = get_scapy_integ_tfm_name(
                get_integ_tfm_name(choix_integrite)
                if choix_integrite != 20
                else "NULL"
            )
            logger.debug(f"For curisosity: {child_spi_i}  -  {child_spi_r}")
            # Create the SecurityAssociation instance
            # nat_t_header not used handled manually using UDP sock
            sa = SecurityAssociation(
                proto=ESP,
                spi=int.from_bytes(child_spi_i, "big"),
                crypt_algo=crypt_algo_name,
                crypt_key=child_keys.sk_ei,
                auth_algo=auth_algo_name,
                auth_key=child_keys.sk_ai,
                esn_en=bool(self.config.ipsec.child_sa.esn),
            )

            self.childs[child_spi_i] = sa
            logger.info("Child SA established successfully")
            return True
        except asyncio.TimeoutError:
            logger.error("Client timeout waiting for CREATE_CHILD_SA response")
            return False

    # TODO next method to handle NotifyPayloadClass
    async def ikev2_informational(
        self,
        info_type: str,  # Either "Delete", "Configuration", "Notify" or "Empty"
        SPIs: list[bytes] = None,
        proto: int = 3,  # Assuming ESP
        configuration_data: bytes = None,
        notify: dict[int, bytes] = None,
        next_payload: str = 0,
    ) -> bool:
        """
        Perform an INFORMATIONAL exchange to send optional information based on the type.

        :param info_type: The type of INFORMATIONAL message payload (Delete, Configuration, or Notify).
        :type type: str
        :param SPIs: The list of SPIs of the SA to delete (for Delete type).
        :type spi: list[bytes], optional
        :param configuration_data: Configuration payload data (for Configuration type).
        :type configuration_data: bytes, optional
        :param notify_data: Notify payload data (for Notify type).
        :type notify: dict[int, bytes], optional {'type': int, 'notify': bytes}
        :param next_payload: The type of next payload in the INFORMATIONAL message.
        :type next_payload: str
        :return: True if the exchange is successful, False otherwise.
        :rtype: bool
        :raises ValueError:
        """
        self.exchange_complete.clear()
        payload = None

        match info_type:
            case "Delete":
                if not SPIs:
                    raise ValueError("spi is required for Delete type")
                for spi in SPIs:
                    payload = IKEv2_Delete(
                        proto=proto, SPIsize=len(spi), SPInum=1, SPI=[spi]
                    )
                    next_payload = "Delete"
                    logger.debug(
                        "Prepared Delete payload for INFORMATIONAL exchange."
                    )

            case "Configuration":
                if configuration_data is None:
                    raise ValueError(
                        "configuration_data is required for Configuration type"
                    )
                payload = IKEv2_CP(
                    CFGType=2,  # Assuming CFG_REPLY as an example
                    attributes=[configuration_data],
                )
                next_payload = "CP"
                logger.debug(
                    "Prepared Configuration payload for INFORMATIONAL exchange."
                )

            case "Notify":
                if notify is None:
                    raise ValueError("notify_data is required for Notify type")
                payload = IKEv2_Notify(
                    proto=1,  # Assuming IKE protocol for the notification
                    SPIsize=0,
                    type=notify[
                        "type"
                    ],  # Example Notify type (INITIAL_CONTACT)
                    notify=notify["notify"],
                )
                next_payload = "Notify"
                logger.debug(
                    "Prepared Notify payload for INFORMATIONAL exchange."
                )

            case "Empty":
                payload = b""
                logger.debug("Empty payload for INFORMATIONAL exchange.")
            case _:
                raise ValueError(
                    "Unsupported INFORMATIONAL type specified must be one of 'Delete', 'Configuration', 'Notify', or 'Empty'"
                )

        # Forge the IKEv2 INFORMATIONAL packet
        info_i_pkt = forge_ikev2_informational(
            mode="Initiator",
            spi_i=self.spi_i,
            spi_r=self.spi_r,
            mid=self.MID,
            ikev2_crypto=self.ikev2_crypto,
            ikev2_keys=self.ikev2_keys,
            data=bytes(payload),
            next_payload=next_payload,
        )
        raw_packet = bytes(info_i_pkt)
        self.transport.sendto(raw_packet)
        logger.debug(
            f"Client sent INFORMATIONAL of type {info_type} to {self.remote_addr}"
        )
        self._increase_MID()

        # Store the informational exchange in the discussion log
        self.discussion.append(
            {
                "initiator": info_i_pkt,
                "decrypted_initiator": bytes(payload),
                "responder": None,
            }
        )

        # Await response and handle it
        try:
            response_data, addr = await asyncio.wait_for(
                self.received_data_queue.get(), timeout=10
            )
            if response_data[:4] == b"\x00" * 4:
                response_data = response_data[4:]
            response = IKEv2(response_data)
            if response is not None:
                self.discussion[-1]["responder"] = response
                self.discussion[-1]["decrypted_responder"] = uncipher_ike_pkt(
                    self.ikev2_crypto, self.ikev2_keys, ike_packet=response
                )
            self.exchange_complete.set()
        except asyncio.TimeoutError:
            logger.warning("Client timeout waiting for INFORMATIONAL response")
            logger.warning(
                "No answer for INFORMATIONAL is not an error continuing"
            )
            return True

        # If it was a delete request, remove the SA from self.childs
        if type == "Delete" and spi in self.childs:
            del self.childs[spi]
            logger.debug(f"Deleted child SA with SPI: {spi}")

    async def esp_packet(
        self,
        spi: bytes = None,
        data: bytes = None,
        test: bool = False,
        seq_num: int = None,
        esn_en: bool = True,
    ) -> bool:
        """
        Send an ESP packet encrypted using the Security Association.

        :param spi_r: The responder SPI to use; if None, use the last one in self.childs.
        :type spi_r: bytes, optional
        :param data: The data to be encrypted and sent; if None, use default payload.
        :type data: bytes, optional
        :return: True if the packet was sent and response received successfully, False otherwise.
        :rtype: bool
        :raises ValueError:
        :raises KeyError:
        """
        self.exchange_complete.clear()
        # If no spi_r is given, use the last one in self.childs
        if spi is None:
            if not self.childs:
                logger.error("No Child SAs available to send ESP packet.")
                raise ValueError(
                    "Attempted to perform an ESP exchange but no valid sa!"
                )
            spi = list(self.childs.keys())[-1]

        sa = self.childs.get(spi)
        if sa is None:
            logger.error(f"No Security Association found for SPI {spi}.")
            raise KeyError(f"No sa for SPI {spi}.")

        # If no data is given, use the default payload
        if data is None:
            data = (
                IP(
                    src=self.config.network.ip_src,
                    dst=self.config.network.ip_dst,
                )
                / ICMP()
            )
        else:
            if not isinstance(data, (bytes, Packet)):
                logger.error("Data must be bytes or a Scapy Packet.")
                raise ValueError(
                    "Data for ESP encap must be bytes or a Scapy Packet."
                )

        # Encrypt the data using the SA
        esp_packet = sa.encrypt(data, seq_num=seq_num, esn_en=esn_en)
        esp_packet = esp_packet[IP].payload
        logger.debug(f"ESP: {esp_packet}")
        logger.debug(f"RAW ESP: {raw(esp_packet)}")

        # If NAT-T is enabled, prepend the UDP header by default done by DatagramEndpoint I guess
        if sa.nat_t_header:
            packet_to_send = esp_packet
        else:
            #  TODO check how do I un encap UDP, send raw packet without encap
            packet_to_send = esp_packet

        raw_packet = bytes(packet_to_send)
        if test:
            # Here perform action for tests crypto
            if self.config.esp.tests.crypto == "cipher":
                pkt = bytearray(raw_packet)
                pkt[12:16] = [0xBB, 0xAA, 0xBB, 0xEE]
            elif self.config.esp.tests.crypto == "icv":
                pkt = bytearray(raw_packet)
                pkt[-4:] = [0xBB, 0xAA, 0xBB, 0xEE]
            else:
                raise ValueError(
                    f"Config.esp.tests.crypto is set top an unkown value {self.config.esp.tests.crypto}"
                )
            pass
        self.sendto_raw(raw_packet)  # No NO_ESP MARKER using original sendto
        logger.debug(f"Client sent ESP packet to {self.remote_addr}")

        try:
            response_data, addr = await asyncio.wait_for(
                self.received_data_queue.get(), timeout=10
            )
            # Decrypt the response using SA Remove NAT-T header if present
            if sa.nat_t_header:
                response_packet = UDP(response_data)
                esp_response = response_packet.payload
            else:
                esp_response = ESP(response_data)
            logger.debug(f"Client received ESP response from {addr}")
            if test:
                decrypted_data = sa.decrypt(esp_response)
                # Store the exchanged data if necessary
                self.discussion.append(
                    {
                        "initiator": packet_to_send,
                        "decrypted_initiator": data,
                        "responder": esp_response,
                        "decrypted_responder": decrypted_data,
                    }
                )
                logger.debug(
                    f"Client decrypted ESP response: {decrypted_data.summary()}"
                )
            self.exchange_complete.set()
            return True
        except asyncio.TimeoutError:
            logger.warning("Client timeout waiting for ESP response")
            return False

    def _increase_MID(
        self,
    ) -> None:
        """
        Increment the Message ID counter (MID) for the next exchange.
        """
        self.MID += 1

    def close_SA(self) -> bool:
        """
        Close the Security Association by sending a DELETE payload.

        :return: True if the SA was closed successfully.
        :rtype: bool
        """
        self.ikev2_informational(
            data=raw(IKEv2_Delete()),
            next_payload="Delete",
        )
        return True
