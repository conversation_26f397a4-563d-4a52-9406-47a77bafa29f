import asyncio
from scapy.layers.inet import IP, ICMP
from ipsecdr.utils.logger import logger
from ipsecdr.engine.scenario import TestConfiguration
from ipsecdr.engine.client import IPsecClient
from ipsecdr.engine.server import IPsecServerManager
from ipsecdr.utils.models import Scenario


class Tester:
    def __init__(
        self,
        test_id: int,
        config: TestConfiguration,
        scenario: Scenario,
        packet_pipe: asyncio.Queue,
        mode: str = "client",
    ):
        self.test_id = test_id
        self.config = config
        self.packet_pipe = packet_pipe
        self.mode = mode

        # Extract scenario data using the Scenario model
        self.exchanges = scenario.exchanges
        self.overlay_configs = scenario.overlay_configs
        self.check_functions = scenario.check_functions

    async def run(self):
        if self.mode == "initiator":
            participant = IPsecClient(config=self.config)
            await participant.start()
            logger.info(f"Tester {self.test_id} running in client mode")

            try:
                for idx, exchange in enumerate(self.exchanges):
                    overlay_config = self.overlay_configs[idx]
                    if overlay_config:
                        participant.config.update_from_dict(overlay_config)

                    status, packets = await self._perform_exchange_clt(
                        participant, exchange, idx
                    )
                    if not status:
                        await self.packet_pipe.put(
                            {
                                "test_id": self.test_id,
                                "status": "failed",
                                "exchange": exchange,
                                "packets": packets,
                            }
                        )
                        break
                    else:
                        tagged_packet = {
                            "test_id": self.test_id,
                            "packets": packets,
                            "exchange": exchange,
                            "check_function": self.check_functions[idx],
                            "status": status,
                        }
                        await self.packet_pipe.put(tagged_packet)
            finally:
                await self.packet_pipe.put(
                    {"test_id": self.test_id, "status": "done"}
                )
                try:
                    await participant.stop()
                except Exception as e:
                    logger.debug(
                        f"Problem disconnecting participant in test {self.test_id}: {e}"
                    )
                    await asyncio.sleep(0.1)

        elif self.mode == "responder":
            participant = IPsecServerManager(config=self.config)
            logger.info(f"Tester {self.test_id} running in server mode")

            try:
                await participant.start()
                for idx, exchange in enumerate(self.exchanges):
                    overlay_config = self.overlay_configs[idx]
                    if overlay_config:
                        await participant.update_config(overlay_config)

                    status, packets = await self._perform_exchange_srv(
                        participant, exchange, idx
                    )
                    if not status:
                        await self.packet_pipe.put(
                            {
                                "test_id": self.test_id,
                                "status": "failed",
                                "exchange": exchange,
                                "packets": packets,
                            }
                        )
                        break
                    else:
                        tagged_packet = {
                            "test_id": self.test_id,
                            "packets": packets,
                            "exchange": exchange,
                            "check_function": (
                                self.check_functions[idx]
                                if idx < len(self.check_functions)
                                else None
                            ),
                            "status": status,
                        }
                        await self.packet_pipe.put(tagged_packet)
            finally:
                await self.packet_pipe.put(
                    {"test_id": self.test_id, "status": "done"}
                )
                try:
                    await participant.stop()
                    logger.debug(
                        f"Tester {self.test_id} has stopped the server."
                    )
                except Exception as e:
                    logger.debug(
                        f"Problem stopping server in test {self.test_id}: {e}"
                    )
        else:
            logger.error(f"Unknown mode '{self.mode}' for Tester")
            raise ValueError(f"Unknown mode '{self.mode}'")

    async def _perform_exchange_clt(
        self, participant, exchange: str, idx: int
    ) -> tuple:
        try:
            if exchange in [
                "INIT",
                "AUTH",
                "CREATE_CHILD_SA",
            ]:
                exchange_method = getattr(
                    participant, f"ikev2_{exchange.lower()}"
                )
                status = await exchange_method()
                await participant.exchange_complete.wait()
                packets = self._collect_packets(participant, exchange)
            elif exchange == "INFORMATIONAL":
                logger.debug(f"TESTER if {len(self.exchanges)} == {idx}:")
                if len(self.exchanges) == idx + 1:
                    status = await participant.ikev2_informational(
                        info_type="Delete",
                        SPIs=[participant.spi_i, participant.spi_r],
                        proto=1,
                    )
                    await participant.exchange_complete.wait()
                else:
                    status = await participant.ikev2_informational(
                        info_type="Empty"
                    )
                    await participant.exchange_complete.wait()
                packets = self._collect_packets(
                    participant=participant, exchange=exchange
                )

            elif exchange == "ESP":
                if self.config.esp.tests.sp:
                    data = (
                        IP(
                            src=self.config.network.ip_src,
                            dst=self.config.network.ip_dst_forbid,
                        )
                        / ICMP()
                    )
                    status = await participant.esp_packet(test=True, data=data)
                    await participant.exchange_complete.wait()
                    packets = self._collect_packets(
                        participant=participant, exchange=exchange
                    )

                else:
                    status = await participant.esp_packet()
                    await participant.exchange_complete.wait()
                    packets = self._collect_packets(
                        participant=participant, exchange=exchange
                    )

            else:
                logger.error(f"Unknown exchange type: {exchange}")
                status = False
                packets = None
            return status, packets
        except Exception as e:
            logger.exception(
                f"Exception during exchange '{exchange}' in test {self.test_id}: {e}"
            )
            return False, None

    def _collect_packets(self, participant, exchange):
        try:
            if not participant.discussion:
                logger.warning(
                    f"No discussion data available for exchange '{exchange}' in test {self.test_id}"
                )
                return None

            last_discussion = participant.discussion[-1]
            if exchange == "INIT":
                packets = {
                    "initiator": last_discussion.get("initiator"),
                    "responder": last_discussion.get("responder"),
                }
            else:
                packets = {
                    "initiator": last_discussion.get("decrypted_initiator"),
                    "responder": last_discussion.get("decrypted_responder"),
                }
            return packets
        except Exception as e:
            logger.error(f"Error collecting packets: {e}")
            return None

    async def _perform_exchange_srv(
        self, participant, exchange: str, idx: int
    ) -> tuple:
        try:
            # Wait until a client connects
            while not participant.protocol.discussions:
                await asyncio.sleep(0.1)

            # Get the address of the connected client
            addr = next(iter(participant.protocol.discussions.keys()))
            logger.debug(f"Using client address: {addr}")

            # Trigger the exchange
            # result = participant.trigger_exchange(exchange, addr)

            # if result is None:
            #    logger.error(f"Failed to trigger exchange {exchange}")
            #    return False, None

            # Wait for the exchange to complete
            while not participant.protocol.exchange_complete.is_set():
                await asyncio.sleep(0.1)
            participant.protocol.exchange_complete.clear()

            # Collect packets from the discussion
            discussion = participant.protocol.discussions[addr]["discussion"]
            if not discussion:
                logger.error(
                    f"No discussion found for address {addr} during exchange '{exchange}' in test {self.test_id}"
                )
                return False, None

            last_exchange = discussion[-1]
            if exchange == "INIT":
                packets = {
                    "initiator": last_exchange.get("initiator"),
                    "responder": last_exchange.get("responder"),
                }
            else:
                packets = {
                    "initiator": last_exchange.get("decrypted_initiator"),
                    "responder": last_exchange.get("decrypted_responder"),
                }

            status = True
            return status, packets
        except Exception as e:
            logger.exception(
                f"Exception during exchange '{exchange}' in test {self.test_id}: {e}"
            )
            return False, None
