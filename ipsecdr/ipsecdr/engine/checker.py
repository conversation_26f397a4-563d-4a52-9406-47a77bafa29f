import asyncio
from typing import Any, List, Optional
from ipsecdr.engine.scenario import TestConfiguration
from ipsecdr.engine.checks import functions
from ipsecdr.utils.models import Outcome
from ipsecdr.utils.logger import logger


class Checker:
    """
    Performs validation checks on network packets received during a test scenario.
    Consumes packets from the packet_pipe and determines if the packets match expected behavior.
    """

    def __init__(
        self,
        test_id: int,
        config: TestConfiguration,
        packet_pipe: asyncio.Queue,
        check_functions: List[Any],
        mode: str = "client",
        test_name: Optional[str] = None,
    ):
        self.test_id = test_id
        self.config = config
        self.packet_pipe = packet_pipe
        self.check_functions = check_functions
        logger.debug(f"CF: {self.check_functions}")
        self.mode = mode
        self.test_name = test_name
        self.outcome = Outcome(
            test_id=test_id,
            test_name=test_name,
            result="Unknown",
        )

    async def run(self):
        """
        Run the packet validation checks for the test.
        """
        logger.info(f"Checker {self.test_id} started.")
        try:
            while True:
                item = await self.packet_pipe.get()
                if item.get("test_id") != self.test_id:
                    logger.warning(
                        f"Checker {self.test_id} received packet for different test_id."
                    )
                    continue

                if item.get("status") is False:
                    # Test failed during execution
                    exchange = item.get("exchange", "Unknown")
                    logger.error(
                        f"Test {self.test_id} failed during exchange '{exchange}'."
                    )
                    self.outcome.result = "Failed"
                    self.outcome.reason = f"Exchange '{exchange}' failed."
                    break
                elif "packets" in item:
                    # Received a packet to check
                    # EDIT LOGIC TO FIT OUTCOME
                    packets = item["packets"]
                    exchange = item.get("exchange", "Unknown")
                    check_function_name = item.get("check_function")
                    logger.debug(f"CFN: {check_function_name}")
                    check_function = self.get_check_function(
                        check_function_name
                    )
                    logger.debug(f"Resolved CFN {check_function}")
                    check_result = check_function(
                        packets=packets, exchange=exchange, mode=self.mode
                    )

                    if not check_result["status"]:
                        logger.warning(
                            f"Test {self.test_id} failed during packet check for exchange '{exchange}'."
                        )
                    else:
                        logger.info(
                            f"Test {self.test_id} pass packet check for exchange '{exchange}'."
                        )
                    self.outcome.result = check_result["status"]
                    self.outcome.reason = check_result["reason"]
                    self.outcome.details = {"info": check_result["details"]}
                    logger.debug("Checker gathered an outcome")
                    break
                else:
                    logger.warning(
                        f"Checker {self.test_id} received unexpected item: {item}"
                    )
        except Exception as e:
            logger.exception(f"Exception in Checker {self.test_id}: {e}")
            self.outcome["result"] = "Failed"
            self.outcome["reason"] = str(e)

    def get_check_function(self, name: Optional[str]) -> Any:
        """
        Retrieve the check function by name from a registry of available functions.
        If the name is None or invalid, return the default perform_checks function.

        :param name: The name of the check function to retrieve.
        :return: The check function.
        """
        logger.debug(f"Trying to collect check function: {name}")
        try:
            if name:
                # Correct usage of getattr without 'default=' keyword
                func = getattr(functions, name, self.perform_checks)
            else:
                # If no name is provided, use the default check function
                func = self.perform_checks
        except Exception as e:
            logger.warning(f"Error getting check function: {name}  err: {e}")
            logger.warning("Issuing default check.")
            func = self.perform_checks
        return func

    def perform_checks(self, packets: Any, exchange: str, mode=None) -> bool:
        """
        Perform default checks on the packet.
        """
        if mode is None:
            mode = self.mode
        logger.debug(
            f"Performing default checks on exchange '{exchange}' for test {self.test_id}."
        )
        # Implement default packet validation logic here
        return {"status": True, "reason": "no reason", "details": None}
