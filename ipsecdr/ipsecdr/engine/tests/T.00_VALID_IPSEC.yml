name: T.00_VALID_IPSEC
test_scenarios:
  initiator:
   - valid_client:
      mode: strict
      packets:
        - number: 1  # INIT default
        - number: 2  # AUTH default
        - number: 3  # CCHILD_SA default
        - number: 4  # ESP defaut
        - number: 5  # INFORMATIONAL default
  responder:
    - valid_server:
        mode: strict
        packets:
          - number: 1  # INIT default
          - number: 2  # AUTH default
          - number: 3  # CCHILD_SA default
          - number: 4  # ESP defaut
          - number: 5  # INFORMATIONAL default