name: T.04_WINDOW_SIZE
test_scenarios:
  initiator:
    - window_size_check:
        mode: target
        packets:
          - number: 1
          - number: 2
            exchange: IKE_AUTH
            overlay_config:
              notify_payloads:
                - type: SET_WINDOW_SIZE
                  notification_data: 5  # <PERSON><PERSON> should ignore and default to 1
            check_function: check_window_size_info
          - number: 3
            exchange: INFORMATIONAL
            check_function: check_window_size_info
          - number: 4
            exchange: INFORMATIONAL
            check_function: check_window_size_info
          - number: 5
            exchange: INFORMATIONAL
            check_function: check_window_size_info
          - number: 6
            exchange: INFORMATIONAL
            check_function: check_window_size_info
          - number: 7
            exchange: INFORMATIONAL
            check_function: check_window_size_info
    - window_size_passive:
        mode: strict
        packets:
          - number: 1
            exchange: INIT
            check_function: check_window_size
          - number: 2
            exchange: AUTH
            check_function: check_window_size
          - number: 3
            exchange: CREATE_CHILD_SA
            check_function: check_window_size
          - number: 4
          - number: 5
  responder:
    - window_size_passive:
        mode: strict
        packets:
          - number: 1
            exchange: INIT
            check_function: check_window_size
          - number: 2
            exchange: AUTH
            check_function: check_window_size
          - number: 3
            exchange: CREATE_CHILD_SA
            check_function: check_window_size
          - number: 4
          - number: 5