name: T.05_ESN
test_scenarios:
  initiator:
    - esn_passive_check:
      mode: strict
      packets:
        - number: 1
          exchange: INIT
          check_function: check_esn
        - number: 2
          exchange: AUTH
          check_function: check_esn
        - number: 3
          exchange: CREATE_CHILD_SA
          check_function: check_esn
        - number: 4
        - number: 5
    - esn_valid:
      mode: target
      targets:
        - exchange: CREATE_CHILD_SA
          action: comply
          check_function: check_esn
          overlay_config:
            ipsec:
              child_sa:
                esn: ESN
    - esn_invalid:
      mode: target
      targets:
        - exchange: CREATE_CHILD_SA
          action: comply
          check_function: check_esn
          overlay_config:
            ipsec:
              child_sa:
                esn: NO_ESN
  responder:
    - esn_passive_check:
      mode: strict
      packets:
        - number: 1
          exchange: INIT
          check_function: check_esn
        - number: 2
          exchange: AUTH
          check_function: check_esn
        - number: 3
          exchange: CREATE_CHILD_SA
          check_function: check_esn
        - number: 4
        - number: 5
    - esn_valid:
      mode: target
      targets:
        - exchange: CREATE_CHILD_SA
          action: comply
          check_function: check_esn
          overlay_config:
            ipsec:
              child_sa:
                esn: ESN
    - esn_invalid:
      mode: target
      targets:
        - exchange: CREATE_CHILD_SA
          action: comply
          check_function: check_esn
          overlay_config:
            ipsec:
              child_sa:
                esn: NO_ESN