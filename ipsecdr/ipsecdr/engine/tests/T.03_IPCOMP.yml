name: T.03_<PERSON><PERSON><PERSON>
test_scenarios:
  initiator:
    - ipcomp_client_passive:
        mode: strict
        packets:
          - number: 1
            exchange: INIT
            check_function: check_ipcomp
          - number: 2
            exchange: AUTH
            check_function: check_ipcomp
          - number: 3
            exchange: CREATE_CHILD_SA
            check_function: check_ipcomp
          - number: 4
          - number: 5
    - ipcomp_client_active:
        mode: target
        targets:
          - exchange: CREATE_CHILD_SA
            action: comply
            check_function: check_ipcomp
            notify_extras_child:
              - type: IPCOMP_SUPPORTED
                notification_data: 1  # IPCOMP_OUI
              - type: IPCOMP_SUPPORTED
                notification_data: 2  # IPCOMP_DEFLATE
              - type: IPCOMP_SUPPORTED
                notification_data: 3  # IPCOMP_LZS
              - type: IPCOMP_SUPPORTED
                notification_data: 4  # IPCOMP_LZJH
  responder:
    - ipcomp_server_passive:
        mode: strict
        packets:
          - number: 1
            exchange: INIT
            check_function: check_ipcomp
          - number: 2
            exchange: AUTH
            check_function: check_ipcomp
          - number: 3
            exchange: CREATE_CHILD_SA
            check_function: check_ipcomp
          - number: 4
          - number: 5
    - ipcomp_server_active:
        mode: target
        targets:
          - exchange: CREATE_CHILD_SA
            action: comply
            check_function: check_ipcomp
            notify_extras_child:
              - type: IPCOMP_SUPPORTED
                notification_data: 1  # IPCOMP_OUI
              - type: IPCOMP_SUPPORTED
                notification_data: 2  # IPCOMP_DEFLATE
              - type: IPCOMP_SUPPORTED
                notification_data: 3  # IPCOMP_LZS
              - type: IPCOMP_SUPPORTED
                notification_data: 4  # IPCOMP_LZJH
