import functools
from ipsecdr.core.IKEv2.constants import (
    TRANSFORMS_TYPE,
    CIPHERS_TFM_ID_IPSEC_DR,
    PRF_TFM_ID_IPSEC_DR,
    INTEG_TFM_ID_IPSEC_DR,
    DH_TFM_ID_IPSEC_DR,
    ESN_TFM_ID_IPSEC_DR,
)
from ipsecdr.core.IKEv2.utils import (
    check_for_any_notify,
    get_tfm_name,
    is_notify,
    get_notify,
)
from ipsecdr.utils.models import NotifyPayload
from ipsecdr.utils.logger import logger

# Conformity documents

ANSSI_IKEv2 = "RFC 7296 ANSSI v1.0"
ANSSI_IPSEC = "RFC 4301 ANSSI v1.0"


def log_function_name(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger.debug(f"Calling: {func.__name__}")
        return func(*args, **kwargs)

    return wrapper


@log_function_name
def are_proposals_dr(packet: bytes) -> tuple[bool, list]:
    """
    Given an IKEv2 packet, this function checks if the payload SA
    transforms are complient to ipsec DR. It returns a list containing,
    result analysis by proposal.
    List format:
    [
    {prop_idx: [list_of_wrong_tfm], dr: bool},
    {}, If many proposals
    ]
    :param packet: The IKEv2 packet to check
    :type packet: bytes
    """
    status = True
    results = []
    for idx, proposal in enumerate(packet["IKEv2_SA"].prop.iterpayloads()):
        is_encryption_dr = False
        is_prf_dr = False
        is_integrity_dr = False
        is_groupdesc_dr = False
        is_esn_dr = False
        prop_check = {idx: [], "dr": False}
        esn_in_proposal = False
        integ_in_proposal = False
        prf_in_proposal = False
        encr_in_proposal = False
        dh_in_proposal = False
        missing_proposal = False
        is_proposal_dr = False
        for transform in proposal.trans.iterpayloads():
            transform_type = transform.transform_type
            transform_id = transform.transform_id
            # Check encryption
            if transform_type == TRANSFORMS_TYPE["Encryption"]:
                encr_in_proposal = True
                if transform_id in CIPHERS_TFM_ID_IPSEC_DR.values():
                    is_encryption_dr = True
                else:
                    prop_check[idx].append(
                        {"type": transform_type, "id": transform_id}
                    )
            # Check PRF
            elif transform_type == TRANSFORMS_TYPE["PRF"]:
                prf_in_proposal = True
                if transform_id in PRF_TFM_ID_IPSEC_DR.values():
                    is_prf_dr = True
                else:
                    prop_check[idx].append(
                        {"type": transform_type, "id": transform_id}
                    )
            # Check Integrity
            elif transform_type == TRANSFORMS_TYPE["Integrity"]:
                integ_in_proposal = True
                if transform_id in INTEG_TFM_ID_IPSEC_DR.values():
                    is_integrity_dr = True
                else:
                    prop_check[idx].append(
                        {"type": transform_type, "id": transform_id}
                    )
            # Check GroupDesc (Diffie-Hellman)
            elif transform_type == TRANSFORMS_TYPE["GroupDesc"]:
                dh_in_proposal = True
                if transform_id in DH_TFM_ID_IPSEC_DR.values():
                    is_groupdesc_dr = True
                else:
                    prop_check[idx].append(
                        {"type": transform_type, "id": transform_id}
                    )
            elif transform_type == TRANSFORMS_TYPE["Extended_Sequence_Number"]:
                esn_in_proposal = True
                if transform_id in ESN_TFM_ID_IPSEC_DR.values():
                    is_esn_dr = True
                else:
                    prop_check[idx].append(
                        {"type": transform_type, "id": transform_id}
                    )
            else:
                logger.error(f"Invalid transform type: {transform_type}")
                raise ValueError

        if (
            not esn_in_proposal
        ):  # If there is no esn proposal still can be true
            is_esn_dr = True
        if not integ_in_proposal:
            is_integrity_dr = True

        if not (encr_in_proposal and prf_in_proposal and dh_in_proposal):
            missing_proposal = True
            if not encr_in_proposal:
                prop_check[idx].append({"missing": "ENCR"})
            if not prf_in_proposal:
                prop_check[idx].append({"missing": "PRF"})
            if not dh_in_proposal:
                prop_check[idx].append({"missing": "DH"})

        if (
            is_encryption_dr
            and is_prf_dr
            and is_integrity_dr
            and is_groupdesc_dr
            and is_esn_dr
            and not missing_proposal
        ):
            is_proposal_dr = True

        prop_check["dr"] = is_proposal_dr
        results.append(prop_check)
    for proposal in results:
        if not proposal["dr"]:
            status = False

    return status, results


@log_function_name
def is_sa_refused(packet: bytes) -> tuple[bool, str | list]:
    logger
    if packet.haslayer("IKEv2_SA"):
        return False, "Packet as an embedded SA cannot tell if refusing"
    else:
        notify_found = check_for_any_notify(packet)
        if 14 in notify_found:  # NO_PROPOSAL_CHOSEN
            return True, "NO_PROPOSAL_CHOSEN"
        elif 7 in notify_found:  # INVALID_SYNTAX
            return True, "INVALID_SYNTAX"
        else:
            return True, notify_found


@log_function_name
def build_sa_details(packet: bytes) -> str:
    if not packet.haslayer("IKEv2 SA"):
        logger.warning("Packet do not contain SA payload")
        return ""
    details = ""
    for idx, proposal in enumerate(packet["IKEv2_SA"].prop.iterpayloads()):
        details += f"Proposal {idx}:\n"
        for transform in proposal.trans.iterpayloads():
            transform_type = transform.transform_type
            transform_id = transform.transform_id
            try:
                tfm_type_name, transform_name = get_tfm_name(
                    transform_type, transform_id
                )
            except KeyError:
                details += f"INVALID TRANSFORM type:{transform_type}  id:{transform_id}"
            details += f"\t{tfm_type_name}: {transform_name}\n"
    return details


@log_function_name
def dr_esn_in_sa(packet: bytes) -> tuple[bool, list]:
    results = []
    for idx, proposal in enumerate(packet["IKEv2_SA"].prop.iterpayloads()):
        is_esn_dr = False
        prop_check = {
            idx: [],
        }
        for transform in proposal.trans.iterpayloads():
            transform_type = transform.transform_type
            transform_id = transform.transform_id
            if transform_type not in TRANSFORMS_TYPE.values():
                raise ValueError(
                    f"Invalid transform type detected: {transform_type}"
                )
            if transform_type == TRANSFORMS_TYPE["Extended_Sequence_Number"]:
                if transform_id in ESN_TFM_ID_IPSEC_DR.values():
                    is_esn_dr = True
                else:
                    prop_check[idx].append(
                        {"type": transform_type, "id": transform_id}
                    )
            else:
                continue
        results.append(prop_check)
    return is_esn_dr, results


@log_function_name
def ipcomp_dr(packet: bytes):
    return not is_notify(packet, "IPCOMP_SUPPORTED")


@log_function_name
def window_size_in(packet: bytes):
    if notify := get_notify(packet, "SET_WINDOW_SIZE"):
        logger.debug(f"{notify}")
        return True, NotifyPayload(
            type=notify["type"], notify=notify["notify"]
        )
    return False, None
