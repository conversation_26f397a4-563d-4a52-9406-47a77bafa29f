from typing import Dict
from ipsecdr.engine.checks.helpers import (
    ANSSI_IKEv2,
    #    ANSSI_IPSEC,
    are_proposals_dr,
    is_sa_refused,
    build_sa_details,
    ipcomp_dr,
    dr_esn_in_sa,
    window_size_in,
)
from ipsecdr.utils.logger import logger

# T.01 ALGO_DR INIT ENUM


def check_ike_init_sa(mode: str, packets: Dict, exchange):
    def build_non_dr_details(results) -> str:
        details = ""
        for idx, proposal in enumerate(results):
            if proposal["dr"]:
                continue
            details += f"\nProposal {idx} is not in conformity:\n"
            for transform in proposal[idx]:
                details += f"\tTransform {transform['type']} banned by {ANSSI_IKEv2}\n"
        return details

    logger.debug(f"CHECK: ike_init_sa({mode}, packets, {exchange})")
    # Check proposals and assess that Transforms are consistent with DR referential
    outcome = {
        "result": None,
        "reason": None,
        "details": None,
    }
    details = ""
    err_i, reason_i = is_sa_refused(packet=packets["initiator"])
    err_r, reason_r = is_sa_refused(packet=packets["responder"])
    if mode == "initiator" and err_r is True:  # Responder refused SA
        status_i, results_i = are_proposals_dr(packet=packets["initiator"])
        if status_i:  # We sent DR proposals server shall have accepted NOK
            status = False
            reason = "Sent DR proposals server shall have accepted"
            details = f"Peer refused SA with {reason_r}, we sent:\n"
            details += build_sa_details(packet=packets["initiator"])
        else:  # Server refused invalid proposal OK
            status = True
            reason = "Server refused invalid SA"
    elif mode == "responder" and err_i is True:  # Initiator refused SA
        status_r, results_r = are_proposals_dr(packet=packets["responder"])
        if status_r:  # We sent DR proposals client shall have sent back DR SA
            status = False
            reason = "Sent DR proposals client shall have accepted"
            details = f"Peer refused SA with {reason_i}, we sent:\n"
            details += build_sa_details(packet=packets["responder"])
        else:  # Client refused invalid proposal OK
            status = True
            reason = "Client refused invalid SA"
    else:  # SA need to be processed and checked because received an SA
        status_i, results_i = are_proposals_dr(packet=packets["initiator"])
        status_r, results_r = are_proposals_dr(packet=packets["responder"])

        if mode == "initiator":
            logger.debug(f"I: {status_i}  R: {status_r}")
            logger.debug(f"RESULT: {results_r}")
            if (
                status_i is True
            ):  # Should have then received OK because sent OK
                status = (
                    status_r  # If no dr then status_r false and status false
                )
                reason = "We sent a DR SA and peer sent a DR SA"
                if status is False:
                    reason = "We sent a DR SA but peer did not send a DR SA"
                    details = build_non_dr_details(results_r)
            else:  # Sent NOK SA so should receive either DR SA prop or false
                if status_r:  # Received DR SA
                    status = True
                    reason = "Sent bad SA but peer sent over a DR SA"
                else:
                    status = False
                    reason = "Sent bad SA and peer acknowledged it"
                    details = build_non_dr_details(results_r)

        elif mode == "responder":
            if status_r is True:  # Should received OK because sent OK
                status = status_i
                reason = "We sent a DR SA and peer sent a DR SA"
                if status is False:
                    reason = "We sent a DR SA but peer did not send a DR SA"
                    details = build_non_dr_details(results_i)
            else:  # Sent NOK SA so should receive either DR SA prop or false
                if status_i:  # Received DR SA
                    status = True
                    reason = "Sent bad SA but peer sent over a DR SA"
                else:
                    status = False
                    reason = "Sent bad SA and peer acknowledged it"
                    # Building details about what not DR tfm was found
                    details = build_non_dr_details(results_i)
        else:
            raise ValueError(f"Invalid mode: {mode}")

    outcome["status"] = status
    outcome["reason"] = reason
    outcome["details"] = details

    return outcome


def check_esn(mode: str, packets: Dict, exchange: str):
    """
    Check the ESN (Extended Sequence Number) implementation in the IKEv2/IPSec SA proposals and ESP packets.

    :param mode: The mode of the TOE ('initiator' or 'responder').
    :param packets: A dictionary containing the 'initiator' and 'responder' packets.
    :param exchange: The type of exchange being checked.
    :return: A dictionary with the outcome of the check.
    """
    outcome = {
        "result": None,
        "reason": None,
        "details": None,
    }

    try:
        if mode == "initiator":
            status, results = dr_esn_in_sa(packet=packets["initiator"])

            responder_status, responder_results = dr_esn_in_sa(
                packet=packets["responder"]
            )
            if not responder_status:
                outcome["result"] = False
                outcome["reason"] = (
                    "Responder did not include mandatory ESN in SA proposals."
                )
                outcome["details"] = build_sa_details(packets["responder"])
                return outcome

            outcome["result"] = True
            outcome["reason"] = (
                "ESN is mandatory and correctly implemented by both initiator and responder."
            )
            return outcome

        elif mode == "responder":
            initiator_status, initiator_results = dr_esn_in_sa(
                packet=packets["initiator"]
            )
            if not initiator_status:
                outcome["result"] = False
                outcome["reason"] = (
                    "Initiator did not include mandatory ESN in SA proposals."
                )
                outcome["details"] = build_sa_details(packets["initiator"])
                return outcome

            outcome["result"] = True
            outcome["reason"] = (
                "ESN is mandatory and correctly implemented by both responder and initiator."
            )
            return outcome

        else:
            raise ValueError(f"Invalid mode: {mode}")

    except Exception as e:
        logger.error(f"Error in check_esn: {e}")
        outcome["result"] = False
        outcome["reason"] = f"Exception occurred during ESN check: {e}"
        return outcome


def check_ipcomp(mode: str, packets: Dict, exchange):
    outcome = {
        "result": None,
        "reason": None,
        "details": None,
    }
    try:
        if mode == "initiator":
            status = ipcomp_dr(packet=packets["responder"])
            if not status:
                outcome["result"] = False
                outcome["reason"] = (
                    "Responder included an IPCOMP_SUPPORTED notify."
                )
                return outcome

            outcome["result"] = True
            outcome["reason"] = (
                "Responder did not include an IPCOMP_SUPPORTED notify."
            )
            return outcome

        elif mode == "responder":
            initiator_status, initiator_results = ipcomp_dr(
                packet=packets["initiator"]
            )
            if not initiator_status:
                outcome["result"] = False
                outcome["reason"] = (
                    "Initiator included an IPCOMP_SUPPORTED notify."
                )
                return outcome

            outcome["result"] = True
            outcome["reason"] = (
                "Initiator did not include an IPCOMP_SUPPORTED notify."
            )
            return outcome

        else:
            raise ValueError(f"Invalid mode: {mode}")

    except Exception as e:
        logger.error(f"Error in check_ipcomp: {e}")
        outcome["result"] = False
        outcome["reason"] = f"Exception occurred during IPCOMP check: {e}"
        return outcome


def check_window_size(mode: str, packets: Dict, exchange):
    outcome = {
        "result": None,
        "reason": None,
        "details": None,
    }
    try:
        if mode == "initiator":
            status, notify = window_size_in(packet=packets["responder"])
            if status:
                outcome["result"] = False
                outcome["reason"] = (
                    "Responder included an SET_WINDOW_SIZE notify."
                )
                outcome["details"] = (
                    f"Notify:\n\ttype: {notify.type}\n\tdata: {notify.notify}"
                )
                return outcome

            outcome["result"] = True
            outcome["reason"] = (
                "Responder did not include an SET_WINDOW_SIZE notify."
            )
            return outcome

        elif mode == "responder":
            status, notify = window_size_in(packet=packets["initiator"])
            if status:
                outcome["result"] = False
                outcome["reason"] = (
                    "Initiator included an SET_WINDOW_SIZE notify."
                )
                outcome["details"] = (
                    f"Notify:\n\ttype: {notify.type}\n\tdata: {notify.notify}"
                )
                return outcome

            outcome["result"] = True
            outcome["reason"] = (
                "Initiator did not include an SET_WINDOW_SIZE notify."
            )
            return outcome

        else:
            raise ValueError(f"Invalid mode: {mode}")

    except Exception as e:
        logger.error(f"Error in check_window_size: {e}")
        outcome["result"] = False
        outcome["reason"] = f"Exception occurred during WINDOW_SIZE check: {e}"
        return outcome


def check_window_size_info(mode: str, packets: Dict, exchange):
    outcome = {
        "result": None,
        "reason": None,
        "details": None,
    }
    logger.debug(f"PACKETS: {packets}")
    return outcome
    """
    try:
        if mode == "initiator":
            status, notify = window_size_in_esp(packet=packets["responder"])
            pass
            return outcome

        elif mode == "responder":
            status, notify = window_size_in_esp(
                packet=packets["initiator"]
            )
            pass
            return outcome

        else:
            raise ValueError(f"Invalid mode: {mode}")

    except Exception as e:
        logger.error(f"Error in check_window_size: {e}")
        outcome["result"] = False
        outcome["reason"] = f"Exception occurred during WINDOW_SIZE check: {e}"
        return outcome
    """
