[tool.poetry]
name = "ipsecdr"
version = "0.1.0"
description = ""
authors = ["test <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.12"
cffi = "^1.17.1"
cryptography = "^43.0.1"
rich = "^13.9.2"
scapy = "^2.6.0"
pyyaml = "^6.0.2"
pydantic = "^2.9.2"


[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.2"
pytest-asyncio = "^0.21.0"
pytest-cov = "*"
pytest-xdist = "*"
pytest-mock = "*"
pytest-rerunfailures = "*"
pytest-randomly = "*"
libvirt-python = "^10.8.0"
black = "^24.10.0"
pre-commit = "^4.0.1"
flake8 = "^7.1.1"
isort = "^5.13.2"


[tool.poetry.group.docs]
optional = true

[tool.poetry.group.docs.dependencies]
mkdocs = "^1.6.1"
mkdocs-material = "^9.5.42"
mkdocstrings = {extras = ["python"], version = "^0.26.2"}

[tool.isort]
profile = "black"

[tool.black]
line-length = 79
target-version = ['py312']
skip-string-normalization = false
exclude = '''
/(
    \.git
    | \.mypy_cache
    | \.venv
    | configs
    | site
    | docs
    | garbage
    | .pytest_cache
)/'''

[tool.pytest.ini_options]
log_cli = true
log_cli_level = "INFO"
testpaths = ["tests"]
norecursedirs = ["tests/infra", "tests/utils"]
addopts = "-m 'not real_world'"
markers = [
    "real_world: mark test as requiring real-world infrastructure setup"
]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

