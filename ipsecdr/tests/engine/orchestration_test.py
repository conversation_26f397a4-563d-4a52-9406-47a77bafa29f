import io
import os
import pytest
from unittest.mock import patch
from pathlib import Path

# Import necessary classes from your modules
from typing import Dict
from ipsecdr.engine.orchestration import Orchestrator
from ipsecdr.utils.configuration import IPsecDRConfigParser
from ipsecdr.engine.scenario import TestConfiguration
from ipsecdr.utils.models import Sc<PERSON>rio, TestPoolEntry


# Assuming REPOSITORY_ROOT is the root directory of the project
REPOSITORY_ROOT = Path(__file__).resolve().parents[2]


def test_orchestrator_builds_test_pool_correctly():
    # Mock configuration content for IPsecDR
    mock_config_content = f"""
[ARGS]
timeout = 30
verbose = True
logfile = /var/log/ipsecdr.log
pcap_path = /var/pcap

[IF]
interface = eth0
ipsec_src = ***********
ipsec_dst = ***********
ip_src = ***********
ip_dst = ***********
port_src = 500
port_dst = 500

[CA]
ca_root = {REPOSITORY_ROOT}/configs/tests/ca.pem
pub_key = {REPOSITORY_ROOT}/configs/tests/cert_i.pem
prv_key = {REPOSITORY_ROOT}/configs/tests/key_i.pem
trust_chain = %(pub_key)s

[IKE_SA]
encr = ENCR_AES_GCM_16
encr_size = 256
prf = PRF_HMAC_SHA2_256
integ = AUTH_HMAC_SHA2_256_128
groupdesc = 256randECPgr

[AUTH]
auth_method = ECDSA_SECP256R1_SHA256

[CHILD_SA]
child_sa_encr = ENCR_AES_GCM_16
child_sa_encr_size = 256
child_sa_integ = AUTH_HMAC_SHA2_256_128
child_sa_groupdesc = 256randECPgr
child_sa_esn = ESN
"""

    # Mock scenario content
    mock_scenario_content = """
name: T.00_VALID_IPSEC
test_scenarios:
  initiator:
    - valid_client:
        mode: strict
        packets:
          - number: 1
            exchange: INIT
            overlay_config:
              ipsec:
                ike_sa:
                  encr: ENCR_AES_CTR
            check_function: check_ike_sa_init
          - number: 2
            exchange: AUTH
            overlay_config:
              ipsec:
                auth_method: ECSDSA_BP256R1_SHA256
            check_function: check_auth
          - number: 3
          - number: 4
          - number: 5
    - valid_client2:
        mode: strict
        packets:
          - number: 1
            exchange: INIT
            overlay_config:
              ipsec:
                ike_sa:
                  encr: ENCR_AES_GCM_16
            check_function: check_ike_sa_init
          - number: 2
            exchange: AUTH
            overlay_config:
              ipsec:
                auth_method: ECSDSA_BP256R1_SHA256
            check_function: check_auth
          - number: 3
          - number: 4
          - number: 5
  responder: []
"""

    # Define the scenario file path based on the test scenario name
    test_scenario_name = "T.00_VALID_IPSEC"
    scenario_file_path = f"{test_scenario_name}.yml"

    # Create a dictionary to map file paths to their corresponding mock content
    file_contents = {
        "dummy_path.ini": mock_config_content,
        scenario_file_path: mock_scenario_content,
    }

    # Define a helper function to mock open
    def mocked_open(file, mode="r", *args, **kwargs):
        filename = os.path.basename(file)
        if filename in file_contents:
            return io.StringIO(file_contents[filename])
        else:
            # If the file is not mocked, raise FileNotFoundError
            raise FileNotFoundError(f"No such file or directory: '{file}'")

    # Patch the built-in open function using the helper
    with patch("builtins.open", new=mocked_open):
        # Initialize the configuration parser
        config_parser = IPsecDRConfigParser("dummy_path.ini")

        # Create a TestConfiguration instance from the parsed configuration
        config = TestConfiguration(
            global_config=config_parser.global_config,
            network_config=config_parser.network,
            pki_config=config_parser.pki,
            ipsec_config=config_parser.ipsec,
        )

        # Initialize the Orchestrator with the test scenario
        orchestrator = Orchestrator(
            config=config,
            test_list=[test_scenario_name],
            max_testers=1,
            max_checkers=1,
            mode="initiator",  # Run in client mode
        )

    # Now, check that the test pool is built correctly
    test_pool = orchestrator.test_pool.get(test_scenario_name, {})

    # Since our scenario defines two initiator scenarios: 'valid_client' and 'valid_client2'
    expected_num_test_entries = 2
    assert (
        len(test_pool) == expected_num_test_entries
    ), f"Expected {expected_num_test_entries} tests in the test pool, but got {len(test_pool)}."

    # Sort test entries by test_id
    test_entries = sorted(test_pool.items(), key=lambda x: x[0])

    # Unpack the test entries
    (test_id_1, test_data_1), (test_id_2, test_data_2) = test_entries

    # Check the first scenario - 'valid_client'
    assert (
        test_data_1.test_id == 1
    ), "Test ID for the first scenario should be 1"
    assert (
        test_data_1.test_name == test_scenario_name
    ), "Test name should match the scenario name"
    assert test_data_1.mode == "initiator", "Mode should be 'initiator'"
    assert test_data_1.lock_status == 0, "Lock status should be 0"
    assert test_data_1.outcome is None, "Outcome should be None"
    assert (
        test_data_1.scenario_name == "valid_client"
    ), "Scenario name should be 'valid_client'"
    scenario_obj_1 = test_data_1.scenario

    # Expected values for the first scenario
    expected_exchanges_1 = [
        "INIT",
        "AUTH",
        "CREATE_CHILD_SA",
        "ESP",
        "INFORMATIONAL",
    ]
    expected_overlay_configs_1 = [
        {"ipsec": {"ike_sa": {"encr": "ENCR_AES_CTR"}}},
        {"ipsec": {"auth_method": "ECSDSA_BP256R1_SHA256"}},
        None,
        None,
        None,
    ]
    expected_check_functions_1 = [
        "check_ike_sa_init",
        "check_auth",
        None,
        None,
        None,
    ]

    assert (
        scenario_obj_1.exchanges == expected_exchanges_1
    ), "Exchanges do not match for the first scenario"
    assert (
        scenario_obj_1.overlay_configs == expected_overlay_configs_1
    ), "Overlay configs do not match for the first scenario"
    assert (
        scenario_obj_1.check_functions == expected_check_functions_1
    ), "Check functions do not match for the first scenario"

    # Check the second scenario - 'valid_client2'
    assert (
        test_data_2.test_id == 2
    ), "Test ID for the second scenario should be 2"
    assert (
        test_data_2.test_name == test_scenario_name
    ), "Test name should match the scenario name"
    assert test_data_2.mode == "initiator", "Mode should be 'initiator'"
    assert test_data_2.lock_status == 0, "Lock status should be 0"
    assert test_data_2.outcome is None, "Outcome should be None"
    assert (
        test_data_2.scenario_name == "valid_client2"
    ), "Scenario name should be 'valid_client2'"
    scenario_obj_2 = test_data_2.scenario

    # Expected values for the second scenario
    expected_exchanges_2 = [
        "INIT",
        "AUTH",
        "CREATE_CHILD_SA",
        "ESP",
        "INFORMATIONAL",
    ]
    expected_overlay_configs_2 = [
        {"ipsec": {"ike_sa": {"encr": "ENCR_AES_GCM_16"}}},
        {"ipsec": {"auth_method": "ECSDSA_BP256R1_SHA256"}},
        None,
        None,
        None,
    ]
    expected_check_functions_2 = [
        "check_ike_sa_init",
        "check_auth",
        None,
        None,
        None,
    ]

    assert (
        scenario_obj_2.exchanges == expected_exchanges_2
    ), "Exchanges do not match for the second scenario"
    assert (
        scenario_obj_2.overlay_configs == expected_overlay_configs_2
    ), "Overlay configs do not match for the second scenario"
    assert (
        scenario_obj_2.check_functions == expected_check_functions_2
    ), "Check functions do not match for the second scenario"


@pytest.fixture
def mock_ipsecdr_config():
    """Fixture to provide mocked IPsecDR configuration content."""
    return f"""
[ARGS]
timeout = 30
verbose = True
logfile = /var/log/ipsecdr.log
pcap_path = /var/pcap

[IF]
interface = eth0
ipsec_src = ***********
ipsec_dst = ***********
ip_src = ***********
ip_dst = ***********
port_src = 500
port_dst = 500

[CA]
ca_root = {REPOSITORY_ROOT}/configs/tests/ca.pem
pub_key = {REPOSITORY_ROOT}/configs/tests/cert_i.pem
prv_key = {REPOSITORY_ROOT}/configs/tests/key_i.pem
trust_chain = %(pub_key)s

[IKE_SA]
encr = ENCR_AES_GCM_16
encr_size = 256
prf = PRF_HMAC_SHA2_256
integ = AUTH_HMAC_SHA2_256_128
groupdesc = 256randECPgr

[AUTH]
auth_method = ECDSA_SECP256R1_SHA256

[CHILD_SA]
child_sa_encr = ENCR_AES_GCM_16
child_sa_encr_size = 256
child_sa_integ = AUTH_HMAC_SHA2_256_128
child_sa_groupdesc = 256randECPgr
child_sa_esn = ESN
"""


@pytest.fixture
def mock_enum_scenario():
    """Fixture to provide mocked scenario content with enumerated fields."""
    return """
name: T.01_ENUM_IPSEC_INIT_ALGO_SRV
test_scenarios:
  initiator:
    - valid_client_enum_1:
        mode: target
        targets:
          - exchange: INIT
            action: enum
            check_function: check_ike_sa_init
            fields:
                - ipsec.ike_sa.encr:
                    - ENCR_AES_CCM_8
                    - ENCR_AES_GCM_16
                - ipsec.ike_sa.integ:
                    - AUTH_AES_192_GMAC
    - valid_client_enum_2:
        mode: target
        targets:
          - exchange: INIT
            action: enum
            check_function: check_ike_sa_init
            fields:
                - ipsec.ike_sa.encr:
                    - ENCR_AES_CTR
                    - ENCR_AES_GCM_16
                - ipsec.ike_sa.integ:
                    - AUTH_AES_192_GMAC
  responder: []
"""


def test_orchestrator_with_enum_scenario(
    mock_ipsecdr_config, mock_enum_scenario
):
    """
    Test that the Orchestrator correctly builds the test pool with enumerated scenarios.

    This test mocks the configuration and scenario files, initializes the Orchestrator,
    and verifies that the test pool contains the expected TestPoolEntry instances
    corresponding to each enumerated combination of 'encr' and 'integ'.
    """
    # Define the scenario file name based on the test scenario name
    test_scenario_name = "T.01_ENUM_IPSEC_INIT_ALGO_SRV"
    scenario_file_path = f"{test_scenario_name}.yml"

    # Create StringIO objects for config and scenario files
    config_file = io.StringIO(mock_ipsecdr_config)
    scenario_file = io.StringIO(mock_enum_scenario)

    # Create a dictionary to map file paths to their corresponding mock content
    file_contents: Dict[str, io.StringIO] = {
        "dummy_path.ini": config_file,
        scenario_file_path: scenario_file,
    }

    def mocked_open_helper(file, mode="r", *args, **kwargs):
        """
        Helper function to mock open based on the file name.
        Returns the corresponding StringIO object if the file is mocked.
        Raises FileNotFoundError otherwise.
        """
        filename = os.path.basename(file)
        if filename in file_contents:
            # Reset the StringIO object's cursor to the beginning for each read
            file_contents[filename].seek(0)
            return file_contents[filename]
        else:
            raise FileNotFoundError(f"No such file or directory: '{file}'")

    # Patch the built-in open function using the helper
    with patch("builtins.open", new=mocked_open_helper):
        # Initialize the configuration parser with the mocked config file
        config_parser = IPsecDRConfigParser("dummy_path.ini")

        # Create a TestConfiguration instance from the parsed configuration
        config = TestConfiguration(
            global_config=config_parser.global_config,
            network_config=config_parser.network,
            pki_config=config_parser.pki,
            ipsec_config=config_parser.ipsec,
        )

        # Initialize the Orchestrator with the test scenario
        orchestrator = Orchestrator(
            config=config,
            test_list=[test_scenario_name],
            max_testers=1,
            max_checkers=1,
            mode="initiator",  # Run in client mode
        )

    # Access the test pool from the orchestrator
    test_pool: Dict[int, TestPoolEntry] = orchestrator.test_pool.get(
        test_scenario_name, {}
    )

    # Since our scenario defines two initiator scenarios, each with two enumerated 'encr' options,
    # we expect a total of 4 TestPoolEntry instances
    expected_num_test_entries = 4
    assert (
        len(test_pool) == expected_num_test_entries
    ), f"Expected {expected_num_test_entries} tests in the test pool, but got {len(test_pool)}."

    # Define the expected combinations of 'encr' and 'integ'
    expected_combinations = [
        {
            "encr": "ENCR_AES_CCM_8",
            "integ": "AUTH_AES_192_GMAC",
        },
        {
            "encr": "ENCR_AES_GCM_16",
            "integ": "AUTH_AES_192_GMAC",
        },
        {
            "encr": "ENCR_AES_CTR",
            "integ": "AUTH_AES_192_GMAC",
        },
        {
            "encr": "ENCR_AES_GCM_16",
            "integ": "AUTH_AES_192_GMAC",
        },
    ]

    # Iterate over each TestPoolEntry and verify the scenarios
    for test_id, test_entry in test_pool.items():
        # Access the Scenario model instance
        scenario: Scenario = test_entry.scenario

        # Assertions for exchanges
        expected_exchanges = [
            "INIT",
        ]
        assert (
            scenario.exchanges == expected_exchanges
        ), f"Test ID {test_id}: Exchanges do not match. Expected {expected_exchanges}, got {scenario.exchanges}"

        # Assertions for check_functions
        expected_check_functions = [
            "check_ike_sa_init",
        ]
        assert (
            scenario.check_functions == expected_check_functions
        ), f"Test ID {test_id}: Check functions do not match. Expected {expected_check_functions}, got {scenario.check_functions}"

        # Assertions for overlay_configs
        expected_overlay_configs = [
            [
                {
                    "ipsec": {
                        "ike_sa": {
                            "encr": "ENCR_AES_CCM_8",
                            "integ": "AUTH_AES_192_GMAC",
                        }
                    }
                }
            ],
        ]
        expected_overlay_configs.append(
            [
                {
                    "ipsec": {
                        "ike_sa": {
                            "encr": "ENCR_AES_GCM_16",
                            "integ": "AUTH_AES_192_GMAC",
                        }
                    }
                },
            ]
        )
        expected_overlay_configs.append(
            [
                {
                    "ipsec": {
                        "ike_sa": {
                            "encr": "ENCR_AES_CTR",
                            "integ": "AUTH_AES_192_GMAC",
                        }
                    }
                },
            ]
        )
        assert (
            scenario.overlay_configs in expected_overlay_configs
        ), f"Test ID {test_id}: Overlay configs do not match. Expected {expected_overlay_configs}, got {scenario.overlay_configs}"

        # Extract 'encr' and 'integ' from the first overlay_config (INIT exchange)
        sa_payload = (
            scenario.overlay_configs[0].get("ipsec", {}).get("ike_sa", {})
        )
        encr = sa_payload.get("encr")
        integ = sa_payload.get("integ")

        # Form the combination dictionary
        combo = {"encr": encr, "integ": integ}

        # Assert that the combination is one of the expected ones
        assert (
            combo in expected_combinations
        ), f"Test ID {test_id}: Unexpected combination: {combo}"

        # Remove the matched combination to ensure no duplicates
        expected_combinations.remove(combo)

    # Optionally, verify that all expected combinations were matched
    assert (
        not expected_combinations
    ), "Not all expected combinations were found in the test pool."
