import io
import pytest
from unittest.mock import patch
from ipsecdr.engine.scenario import TestScenarioParser  # Updated import
from tests.support.logger import logger


def test_parse_strict_mode_with_exchange():
    # Test a basic strict mode scenario with multiple exchanges
    test_config_content = """
    mode: strict
    packets:
      - number: 1
        exchange: AUTH
        overlay_config: overlay_auth
        check_function: check_auth
      - number: 2
        overlay_config: overlay_create_child_sa
        check_function: check_create_child_sa
      - number: 3
        exchange: INFORMATIONAL
        overlay_config: overlay_informational
        check_function: check_informational
    """
    test_config_file = io.StringIO(test_config_content)

    with patch("builtins.open", return_value=test_config_file):
        parser = TestScenarioParser("dummy_path.yaml")
        scenario_data_list = parser.get_scenario()

    # Since it's strict mode, we expect one scenario dict
    assert len(scenario_data_list) == 1
    scenario_data = scenario_data_list[0]

    expected_exchanges = ["AUTH", "CREATE_CHILD_SA", "INFORMATIONAL"]
    expected_overlay_configs = [
        "overlay_auth",
        "overlay_create_child_sa",
        "overlay_informational",
    ]
    expected_check_functions = [
        "check_auth",
        "check_create_child_sa",
        "check_informational",
    ]

    assert scenario_data["exchanges"] == expected_exchanges
    assert scenario_data["overlay_configs"] == expected_overlay_configs
    assert scenario_data["check_functions"] == expected_check_functions


def test_parse_strict_mode_missing_fields():
    # Test handling of missing optional fields
    test_config_content = """
    mode: strict
    packets:
      - number: 1
      - number: 2
        overlay_config: overlay_auth
      - number: 3
        exchange: CREATE_CHILD_SA
    """
    test_config_file = io.StringIO(test_config_content)

    with patch("builtins.open", return_value=test_config_file):
        parser = TestScenarioParser("dummy_path.yaml")
        scenario_data_list = parser.get_scenario()

    assert len(scenario_data_list) == 1
    scenario_data = scenario_data_list[0]

    expected_exchanges = ["INIT", "AUTH", "CREATE_CHILD_SA"]
    expected_overlay_configs = [None, "overlay_auth", None]
    expected_check_functions = [None, None, None]

    assert scenario_data["exchanges"] == expected_exchanges
    assert scenario_data["overlay_configs"] == expected_overlay_configs
    assert scenario_data["check_functions"] == expected_check_functions


def test_parse_invalid_mode():
    # Test for an invalid mode value, expecting an error
    test_config_content = """
    mode: unknown
    packets:
      - number: 1
    """
    test_config_file = io.StringIO(test_config_content)

    with patch("builtins.open", return_value=test_config_file):
        with pytest.raises(ValueError, match="Unknown mode"):
            parser = TestScenarioParser("dummy_path.yaml")
            parser.get_scenario()


def test_parse_strict_mode_invalid_packet_number():
    # Test for an invalid packet number format (non-integer)
    test_config_content = """
    mode: strict
    packets:
      - number: "first"
        overlay_config: overlay_auth
    """
    test_config_file = io.StringIO(test_config_content)

    with patch("builtins.open", return_value=test_config_file):
        with pytest.raises(ValueError):
            parser = TestScenarioParser("dummy_path.yaml")
            parser.get_scenario()


def test_parse_strict_mode_default_exchange():
    # Test a basic strict mode scenario with multiple exchanges
    test_config_content = """
    mode: strict
    packets:
      - number: 1
        exchange: INIT
        overlay_config: overlay_auth
        check_function: check_auth
      - number: 2
        exchange: CREATE_CHILD_SA
        overlay_config: overlay_create_child_sa
        check_function: check_create_child_sa
      - number: 3
      - number: 4
        exchange: INFORMATIONAL
        overlay_config: overlay_info
    """
    test_config_file = io.StringIO(test_config_content)

    with patch("builtins.open", return_value=test_config_file):
        parser = TestScenarioParser("dummy_path.yaml")
        scenario_data_list = parser.get_scenario()

    # Since it's strict mode, we expect one scenario dict
    assert len(scenario_data_list) == 1
    scenario_data = scenario_data_list[0]

    expected_exchanges = ["INIT", "CREATE_CHILD_SA", "ESP", "INFORMATIONAL"]
    expected_overlay_configs = [
        "overlay_auth",
        "overlay_create_child_sa",
        None,
        "overlay_info",
    ]
    expected_check_functions = [
        "check_auth",
        "check_create_child_sa",
        None,
        None,
    ]

    assert scenario_data["exchanges"] == expected_exchanges
    assert scenario_data["overlay_configs"] == expected_overlay_configs
    assert scenario_data["check_functions"] == expected_check_functions


def test_parse_strict_mode_discontinue_nums_and_inconsistent():
    # Test a basic strict mode scenario with multiple exchanges
    test_config_content = """
    mode: strict
    packets:
      - number: 1
        exchange: INIT
        overlay_config: overlay_auth
        check_function: check_auth
      - number: 2
        exchange: CREATE_CHILD_SA
        overlay_config: overlay_create_child_sa
        check_function: check_create_child_sa
      - number: 3
      - number: 444
        exchange: INFORMATIONAL
        overlay_config: overlay_info
    """
    test_config_file = io.StringIO(test_config_content)

    with patch("builtins.open", return_value=test_config_file):
        parser = TestScenarioParser("dummy_path.yaml")
        scenario_data_list = parser.get_scenario()

    # Since it's strict mode, we expect one scenario dict
    assert len(scenario_data_list) == 1
    scenario_data = scenario_data_list[0]

    expected_exchanges = [
        "INIT",
        "CREATE_CHILD_SA",
        "ESP",
        "444-INFORMATIONAL",
    ]
    expected_overlay_configs = [
        "overlay_auth",
        "overlay_create_child_sa",
        None,
        "overlay_info",
    ]
    expected_check_functions = [
        "check_auth",
        "check_create_child_sa",
        None,
        None,
    ]

    assert scenario_data["exchanges"] == expected_exchanges
    assert scenario_data["overlay_configs"] == expected_overlay_configs
    assert scenario_data["check_functions"] == expected_check_functions


def test_parse_strict_mode_discontinue_nums():
    # Test a basic strict mode scenario with multiple exchanges
    test_config_content = """
    mode: strict
    packets:
      - number: 1
        exchange: INIT
        overlay_config: overlay_auth
        check_function: check_auth
      - number: 2
      - number: 3
      - number: 444
        exchange: INFORMATIONAL
        overlay_config: overlay_info
    """
    test_config_file = io.StringIO(test_config_content)

    with patch("builtins.open", return_value=test_config_file):
        parser = TestScenarioParser("dummy_path.yaml")
        scenario_data_list = parser.get_scenario()

    # Since it's strict mode, we expect one scenario dict
    assert len(scenario_data_list) == 1
    scenario_data = scenario_data_list[0]

    expected_exchanges = [
        "INIT",
        "AUTH",
        "CREATE_CHILD_SA",
        "444-INFORMATIONAL",
    ]
    expected_overlay_configs = ["overlay_auth", None, None, "overlay_info"]
    expected_check_functions = ["check_auth", None, None, None]

    assert scenario_data["exchanges"] == expected_exchanges
    assert scenario_data["overlay_configs"] == expected_overlay_configs
    assert scenario_data["check_functions"] == expected_check_functions


def test_parse_strict_mode_discontinue_nums2():
    # Test a basic strict mode scenario with multiple exchanges
    test_config_content = """
    mode: strict
    packets:
      - number: 1
        exchange: INIT
        overlay_config: overlay_auth
        check_function: check_auth
      - number: 2
      - number: 3
      - number: 444
        exchange: ESP
        overlay_config: overlay_esp
    """
    test_config_file = io.StringIO(test_config_content)

    with patch("builtins.open", return_value=test_config_file):
        parser = TestScenarioParser("dummy_path.yaml")
        scenario_data_list = parser.get_scenario()

    # Since it's strict mode, we expect one scenario dict
    assert len(scenario_data_list) == 1
    scenario_data = scenario_data_list[0]

    expected_exchanges = ["INIT", "AUTH", "CREATE_CHILD_SA", "444-ESP"]
    expected_overlay_configs = ["overlay_auth", None, None, "overlay_esp"]
    expected_check_functions = ["check_auth", None, None, None]

    assert scenario_data["exchanges"] == expected_exchanges
    assert scenario_data["overlay_configs"] == expected_overlay_configs
    assert scenario_data["check_functions"] == expected_check_functions


def test_scenario_parser_with_new_enum_config():
    # Test the scenario parser with the new enum config structure
    test_config_content = """
    mode: target
    targets:
      - exchange: INIT
        action: enum
        check_function: check_ike_sa_init
        fields:
             - ipsec.ike_sa.encr: 
                - ENCR_AES_CBC
                - ENCR_AES_GCM_16
             - ipsec.ike_sa.prf: 
                - PRF_HMAC_SHA1
    """
    test_config_file = io.StringIO(test_config_content)

    with patch("builtins.open", return_value=test_config_file):
        parser = TestScenarioParser("dummy_path.yaml")
        scenario_dicts = parser.get_scenario()

    # We expect 2 scenarios (2 ENCR options * 1 PRF option)
    expected_num_scenarios = 2

    assert len(scenario_dicts) == expected_num_scenarios

    expected_overlay_configs = [
        {
            "ipsec": {
                "ike_sa": {
                    "encr": "ENCR_AES_CBC",
                    "prf": "PRF_HMAC_SHA1",
                }
            }
        },
        {
            "ipsec": {
                "ike_sa": {
                    "encr": "ENCR_AES_GCM_16",
                    "prf": "PRF_HMAC_SHA1",
                }
            }
        },
    ]

    actual_overlay_configs = []
    for scenario_data in scenario_dicts:
        overlay_config = scenario_data["overlay_configs"][0]
        actual_overlay_configs.append(overlay_config)

    assert actual_overlay_configs == expected_overlay_configs


def test_parse_target_mode_with_enum():
    # Test a target mode scenario with enumeration using the new config structure
    test_config_content = """
    mode: target
    targets:
      - exchange: INIT
        action: enum
        check_function: check_ike_sa_init
        fields:
            - ipsec.ike_sa.encr:
                - ENCR_AES_CBC
                - ENCR_AES_GCM_16
            - ipsec.ike_sa.prf:
                - PRF_HMAC_SHA1
                - PRF_HMAC_SHA2_256
            - ipsec.ike_sa.integ:
                - AUTH_HMAC_SHA1_96
            - ipsec.ike_sa.groupdesc:
                - GROUP_19
                - GROUP_20
    """
    test_config_file = io.StringIO(test_config_content)

    with patch("builtins.open", return_value=test_config_file):
        parser = TestScenarioParser("dummy_path.yaml")
        scenario_dicts = parser.get_scenario()

    expected_num_scenarios = 8  # 2 ENCR * 2 PRF * 1 INTEG * 2 DH
    assert len(scenario_dicts) == expected_num_scenarios

    expected_combinations = [
        ("ENCR_AES_CBC", "PRF_HMAC_SHA1", "AUTH_HMAC_SHA1_96", "GROUP_19"),
        ("ENCR_AES_CBC", "PRF_HMAC_SHA1", "AUTH_HMAC_SHA1_96", "GROUP_20"),
        ("ENCR_AES_CBC", "PRF_HMAC_SHA2_256", "AUTH_HMAC_SHA1_96", "GROUP_19"),
        ("ENCR_AES_CBC", "PRF_HMAC_SHA2_256", "AUTH_HMAC_SHA1_96", "GROUP_20"),
        ("ENCR_AES_GCM_16", "PRF_HMAC_SHA1", "AUTH_HMAC_SHA1_96", "GROUP_19"),
        ("ENCR_AES_GCM_16", "PRF_HMAC_SHA1", "AUTH_HMAC_SHA1_96", "GROUP_20"),
        (
            "ENCR_AES_GCM_16",
            "PRF_HMAC_SHA2_256",
            "AUTH_HMAC_SHA1_96",
            "GROUP_19",
        ),
        (
            "ENCR_AES_GCM_16",
            "PRF_HMAC_SHA2_256",
            "AUTH_HMAC_SHA1_96",
            "GROUP_20",
        ),
    ]

    actual_combinations = []
    for scenario_data in scenario_dicts:
        overlay_config = scenario_data["overlay_configs"][0]
        ike_sa_config = overlay_config.get("ipsec", {}).get("ike_sa", {})
        encr = ike_sa_config.get("encr")
        prf = ike_sa_config.get("prf")
        integ = ike_sa_config.get("integ")
        dh = ike_sa_config.get("groupdesc")
        combo = (encr, prf, integ, dh)
        actual_combinations.append(combo)

    assert set(actual_combinations) == set(expected_combinations)


def test_parse_target_mode_with_invalid_field():
    # Test for invalid field in the config fields
    test_config_content = """
    mode: target
    targets:
      - exchange: INIT
        action: enum
        check_function: check_invalid_field
        fields:
            - ipsec.invalid_field: 
                - VALUE1
                - VALUE2
    """
    test_config_file = io.StringIO(test_config_content)

    with patch("builtins.open", return_value=test_config_file):
        with pytest.raises(
            ValueError,
            match="Invalid field 'ipsec.invalid_field' in config fields",
        ):
            parser = TestScenarioParser("dummy_path.yaml")
            parser.get_scenario()


def test_parse_target_mode_with_enum_and_comply():
    # Test for a target mode with multiple actions, including 'enum' and 'comply' or missing action
    test_configs = [
        # Config with 'comply' action specified
        """
        mode: target
        targets:
          - exchange: AUTH
            action: comply
            check_function: check_auth_compliance
            overlay_config: auth_overlay_config
          - exchange: CREATE_CHILD_SA
            action: enum
            check_function: check_child_sa_enum
            fields:
                 - ipsec.child_sa.encr: 
                    - ENCR_AES_CCM_8
                    - ENCR_AES_GCM_16
                 - ipsec.child_sa.integ: 
                    - AUTH_AES_192_GMAC
        """,
        # Equivalent config without 'action' specified for AUTH
        """
        mode: target
        targets:
          - exchange: AUTH
            check_function: check_auth_compliance
            overlay_config: auth_overlay_config
          - exchange: CREATE_CHILD_SA
            action: enum
            check_function: check_child_sa_enum
            fields:
                 - ipsec.child_sa.encr:
                    - ENCR_AES_CCM_8
                    - ENCR_AES_GCM_16
                 - ipsec.child_sa.integ:
                    - AUTH_AES_192_GMAC
        """,
    ]

    for test_config_content in test_configs:
        test_config_file = io.StringIO(test_config_content)

        with patch("builtins.open", return_value=test_config_file):
            parser = TestScenarioParser("dummy_path.yaml")
            scenario_dicts = parser.get_scenario()
        # We expect 2 scenarios (since ENCR has 2 options, PRF has 1)
        expected_num_scenarios = 2
        assert len(scenario_dicts) == expected_num_scenarios

        for scenario_data in scenario_dicts:
            exchanges = scenario_data["exchanges"]
            overlay_configs = scenario_data["overlay_configs"]
            check_functions = scenario_data["check_functions"]

            # Exchanges should be ['AUTH', 'CREATE_CHILD_SA']
            assert exchanges == ["AUTH", "CREATE_CHILD_SA"]
            # Check functions should match
            assert check_functions == [
                "check_auth_compliance",
                "check_child_sa_enum",
            ]

            # Overlay configs should match
            overlay_auth_config = overlay_configs[0]
            assert overlay_auth_config == "auth_overlay_config"

            overlay_create_child_sa_config = overlay_configs[1]
            logger.warning(overlay_create_child_sa_config)
            ike_sa_config = overlay_create_child_sa_config.get(
                "ipsec", {}
            ).get("child_sa", {})
            logger.debug(ike_sa_config)
            encr = ike_sa_config.get("encr")
            prf = ike_sa_config.get("integ")
            # Check that ENCR is one of the expected values
            assert encr in ["ENCR_AES_CCM_8", "ENCR_AES_GCM_16"]
            assert prf == "AUTH_AES_192_GMAC"

        # Optionally, check that all combinations are covered
        expected_combinations = [
            ("ENCR_AES_CCM_8", "AUTH_AES_192_GMAC"),
            ("ENCR_AES_GCM_16", "AUTH_AES_192_GMAC"),
        ]

        actual_combinations = []
        for scenario_data in scenario_dicts:
            overlay_create_child_sa_config = scenario_data["overlay_configs"][
                1
            ]
            ike_sa_config = overlay_create_child_sa_config.get(
                "ipsec", {}
            ).get("child_sa", {})
            combo = (ike_sa_config.get("encr"), ike_sa_config.get("integ"))
            actual_combinations.append(combo)

        assert set(actual_combinations) == set(expected_combinations)


def test_parse_target_mode_missing_action():
    # Test that missing 'action' key is treated as 'comply' or default
    test_config_content = """
    mode: target
    targets:
      - exchange: AUTH
        check_function: check_auth_compliance
        overlay_config: auth_overlay_config
      - exchange: CREATE_CHILD_SA
        check_function: check_child_sa
        overlay_config: child_sa_overlay_config
    """
    test_config_file = io.StringIO(test_config_content)

    with patch("builtins.open", return_value=test_config_file):
        parser = TestScenarioParser("dummy_path.yaml")
        scenario_dicts = parser.get_scenario()
    # We expect 1 scenario since there is no enumeration
    expected_num_scenarios = 1
    assert len(scenario_dicts) == expected_num_scenarios

    scenario_data = scenario_dicts[0]
    exchanges = scenario_data["exchanges"]
    overlay_configs = scenario_data["overlay_configs"]
    check_functions = scenario_data["check_functions"]

    # Exchanges should be ['AUTH', 'CREATE_CHILD_SA']
    assert exchanges == ["AUTH", "CREATE_CHILD_SA"]
    # Check functions should match
    assert check_functions == ["check_auth_compliance", "check_child_sa"]

    # Overlay configs should match
    assert overlay_configs == [
        "auth_overlay_config",
        "child_sa_overlay_config",
    ]


def test_parse_strict_mode_with_multiple_entries_in_ike_sa():
    # Test a strict mode scenario where overlay_config contains multiple entries under ike_sa
    test_config_content = """
    mode: strict
    packets:
      - number: 1
        exchange: INIT
        overlay_config:
          ipsec:
            ike_sa:
              - encr: ENCR_AES_CTR
              - integ: AUTH_HMAC_SHA1_160
        check_function: check_ike_sa_init
      - number: 2
        exchange: AUTH
        overlay_config:
          ipsec:
            ike_sa:
              - auth_method: ECSDSA_BP256R1_SHA256
    """
    test_config_file = io.StringIO(test_config_content)

    with patch("builtins.open", return_value=test_config_file):
        parser = TestScenarioParser("dummy_path.yaml")
        scenario_data_list = parser.get_scenario()

    # Since it's strict mode, we expect one scenario dict
    assert len(scenario_data_list) == 1
    scenario_data = scenario_data_list[0]

    expected_exchanges = ["INIT", "AUTH"]
    expected_overlay_configs = [
        {
            "ipsec": {
                "ike_sa": {
                    "encr": "ENCR_AES_CTR",
                    "integ": "AUTH_HMAC_SHA1_160",
                }
            }
        },
        {"ipsec": {"ike_sa": {"auth_method": "ECSDSA_BP256R1_SHA256"}}},
    ]

    assert (
        scenario_data["exchanges"] == expected_exchanges
    ), "Exchanges do not match"
    assert (
        scenario_data["overlay_configs"] == expected_overlay_configs
    ), "Overlay configs do not match"
    assert scenario_data["check_functions"] == [
        "check_ike_sa_init",
        None,
    ], "Check functions do not match"


def test_parse_target_mode_with_overlay_config_having_multiple_entries():
    # Test target mode where overlay_config contains multiple entries under ike_sa
    test_config_content = """
    mode: target
    targets:
      - exchange: INIT
        action: comply
        overlay_config:
          ipsec:
            ike_sa:
              - encr: ENCR_AES_CTR
              - integ: AUTH_HMAC_SHA1_160
        check_function: check_ike_sa_init
    """
    test_config_file = io.StringIO(test_config_content)

    with patch("builtins.open", return_value=test_config_file):
        parser = TestScenarioParser("dummy_path.yaml")
        scenario_dicts = parser.get_scenario()

    # Since it's target mode with no enumeration, we expect one scenario dict
    assert len(scenario_dicts) == 1
    scenario_data = scenario_dicts[0]

    expected_exchanges = ["INIT"]
    expected_overlay_configs = [
        {
            "ipsec": {
                "ike_sa": {
                    "encr": "ENCR_AES_CTR",
                    "integ": "AUTH_HMAC_SHA1_160",
                }
            }
        },
    ]
    expected_check_functions = ["check_ike_sa_init"]

    assert (
        scenario_data["exchanges"] == expected_exchanges
    ), "Exchanges do not match"
    assert (
        scenario_data["overlay_configs"] == expected_overlay_configs
    ), "Overlay configs do not match"
    assert (
        scenario_data["check_functions"] == expected_check_functions
    ), "Check functions do not match"


def test_scenario_parser_with_dependencies():
    mock_scenario_content = """
    mode: target
    targets:
      - exchange: INIT
        action: enum
        check_function: check_ike_sa_init
        fields:
            - ipsec.ike_sa.encr:
                - ENCR_AES_GCM_16
                - ENCR_3DES
                - ENCR_BLOWFISH
            - ipsec.ike_sa.encr_size: any
    """
    #     "ENCR_AES_GCM_16": (20, True, True, True, [128, 192, 256]),
    # "ENCR_BLOWFISH": (7, False, True, True, [128]),
    #  "ENCR_3DES": (3, False, True, True, [192]),
    # Mock the scenario file reading
    scenario_file = io.StringIO(mock_scenario_content)
    with patch("builtins.open", return_value=scenario_file):
        parser = TestScenarioParser("dummy_path.yml")
        scenarios = parser.get_scenario()
    overlay_configs = []
    # Extract overlay configurations
    for scenario in scenarios:
        overlay_configs.append(scenario["overlay_configs"])

    combinations = []
    for config in overlay_configs:
        encr = config[0]["ipsec"]["ike_sa"]["encr"]
        encr_size = config[0]["ipsec"]["ike_sa"]["encr_size"]
        combinations.append((encr, encr_size))

    # Expected valid combinations
    expected_combinations = [
        ("ENCR_AES_GCM_16", 128),
        ("ENCR_AES_GCM_16", 192),
        ("ENCR_AES_GCM_16", 256),
        ("ENCR_3DES", 192),
        ("ENCR_BLOWFISH", 128),
    ]

    assert sorted(combinations) == sorted(
        expected_combinations
    ), "Parser generated invalid combinations."
