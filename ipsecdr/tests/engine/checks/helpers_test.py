import pytest
from scapy.contrib.ikev2 import (
    IKEv2,
    IKEv2_Transform,
    IKEv2_Notify,
)
from ipsecdr.engine.checks.helpers import (
    are_proposals_dr,
    is_sa_refused,
    build_sa_details,
    dr_esn_in_sa,
    ipcomp_dr,
    window_size_in,
)
from ipsecdr.core.IKEv2.constants import (
    TRANSFORMS_TYPE,
    CIPHERS_TFM_ID_IPSEC_DR,
    PRF_TFM_ID_IPSEC_DR,
    INTEG_TFM_ID_IPSEC_DR,
    DH_TFM_ID_IPSEC_DR,
)
from ipsecdr.core.IKEv2.utils import get_tfm_name
from ipsecdr.core.IKEv2.payloads import SecurityAssociation


def test_are_proposals_dr_with_dr_proposal():
    # Build an IKEv2 packet with an SA payload that is compliant with IPSEC DR
    # Use transforms that are in the IPSEC DR lists
    ciphers = [13]  # ENCR_AES_CTR
    key_ciphers = [256]
    prfs = [5]  # PRF_HMAC_SHA2_256
    integrities = [12]  # AUTH_HMAC_SHA2_256_128
    groupdescs = [19]  # DH_GROUP_19

    sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
    )

    ikev2_packet = IKEv2() / sa_payload

    status, results = are_proposals_dr(ikev2_packet)

    assert status is True, "Proposals should be DR compliant"
    assert len(results) == 1, "Should have one proposal"
    for proposal in results:
        assert proposal["dr"] is True, "Proposal should be DR compliant"
        idx = list(proposal.keys())[0]
        assert (
            proposal[idx] == []
        ), "There should be no non-compliant transforms"


def test_are_proposals_dr_with_non_dr_proposal():
    # Build an IKEv2 packet with an SA payload that is not compliant with IPSEC DR
    ciphers = [12]  # ENCR_DES_CBC, not DR compliant
    key_ciphers = [128]
    prfs = [1]  # PRF_HMAC_MD5, not DR compliant
    integrities = [1]  # AUTH_HMAC_MD5_96, not DR compliant
    groupdescs = [2]  # MODP_1024, not DR compliant

    sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
    )

    ikev2_packet = IKEv2() / sa_payload

    status, results = are_proposals_dr(ikev2_packet)

    assert status is False, "Proposals should not be DR compliant"
    assert len(results) == 1, "Should have one proposal"
    for proposal in results:
        assert proposal["dr"] is False, "Proposal should not be DR compliant"
        idx = list(proposal.keys())[0]
        non_dr_transforms = proposal[idx]
        assert (
            len(non_dr_transforms) > 0
        ), "Non-compliant transforms should be listed"
        for tfm in non_dr_transforms:
            tfm_type = tfm["type"]
            tfm_id = tfm["id"]
            tfm_type_name, tfm_name = get_tfm_name(tfm_type, tfm_id)
            if tfm_type == TRANSFORMS_TYPE["Encryption"]:
                assert (
                    tfm_id not in CIPHERS_TFM_ID_IPSEC_DR.values()
                ), "Encryption transform incorrectly marked as non-compliant"
            elif tfm_type == TRANSFORMS_TYPE["PRF"]:
                assert (
                    tfm_id not in PRF_TFM_ID_IPSEC_DR.values()
                ), "PRF transform incorrectly marked as non-compliant"
            elif tfm_type == TRANSFORMS_TYPE["Integrity"]:
                assert (
                    tfm_id not in INTEG_TFM_ID_IPSEC_DR.values()
                ), "Integrity transform incorrectly marked as non-compliant"
            elif tfm_type == TRANSFORMS_TYPE["GroupDesc"]:
                assert (
                    tfm_id not in DH_TFM_ID_IPSEC_DR.values()
                ), "DH Group transform incorrectly marked as non-compliant"


""" TODO Update SecurityAssociation payload to be able to send SA with multiple proposal
def test_are_proposals_dr_with_mixed_proposals():
    # Build an IKEv2 packet with multiple proposals, some compliant, some not
    compliant_sa_payload = SecurityAssociation(
        next_payload="SA",
        ciphers=[12],  # ENCR_AES_CBC
        key_ciphers=[256],
        prfs=[5],  # PRF_HMAC_SHA2_256
        integrities=[12],  # AUTH_HMAC_SHA2_256_128
        groupdescs=[19],  # DH_GROUP_19
    )

    non_compliant_sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=[1],  # ENCR_DES_CBC
        key_ciphers=[64],
        prfs=[1],  # PRF_HMAC_MD5
        integrities=[1],  # AUTH_HMAC_MD5_96
        groupdescs=[2],  # MODP_1024
    )

    ikev2_packet = IKEv2() / compliant_sa_payload / non_compliant_sa_payload

    status, results = are_proposals_dr(ikev2_packet)

    assert status is False, "Overall status should be False since not all proposals are compliant"
    assert len(results) == 2, "Should have two proposals"

    # Check first proposal (compliant)
    proposal0 = results[0]
    assert proposal0["dr"] is True, "First proposal should be DR compliant"
    idx0 = list(proposal0.keys())[0]
    assert proposal0[idx0] == [], "First proposal should have no non-compliant transforms"

    # Check second proposal (non-compliant)
    proposal1 = results[1]
    assert proposal1["dr"] is False, "Second proposal should not be DR compliant"
    idx1 = list(proposal1.keys())[0]
    non_dr_transforms = proposal1[idx1]
    assert len(non_dr_transforms) > 0, "Second proposal should list non-compliant transforms"
"""


def test_is_sa_refused_with_notify_no_proposal_chosen():
    # Build an IKEv2 packet without SA payload, but with Notify payload indicating NO_PROPOSAL_CHOSEN (14)
    notify_payload = IKEv2_Notify(
        next_payload="None",
        type=14,  # NO_PROPOSAL_CHOSEN
        notify=b"",
    )

    ikev2_packet = IKEv2() / notify_payload

    refused, reason = is_sa_refused(ikev2_packet)

    assert refused is True, "SA should be considered refused"
    assert (
        reason == "NO_PROPOSAL_CHOSEN"
    ), "Reason should be NO_PROPOSAL_CHOSEN"


def test_is_sa_refused_with_notify_invalid_syntax():
    # Build an IKEv2 packet without SA payload, but with Notify payload indicating INVALID_SYNTAX (7)
    notify_payload = IKEv2_Notify(
        next_payload="None",
        type=7,  # INVALID_SYNTAX
        notify=b"",
    )

    ikev2_packet = IKEv2() / notify_payload

    refused, reason = is_sa_refused(ikev2_packet)

    assert refused is True, "SA should be considered refused"
    assert reason == "INVALID_SYNTAX", "Reason should be INVALID_SYNTAX"


def test_is_sa_refused_with_sa_payload():
    # Build an IKEv2 packet with an SA payload
    ciphers = [12]  # ENCR_AES_CBC
    key_ciphers = [256]
    prfs = [5]  # PRF_HMAC_SHA2_256
    integrities = [12]  # AUTH_HMAC_SHA2_256_128
    groupdescs = [19]  # DH_GROUP_19

    sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
    )

    ikev2_packet = IKEv2() / sa_payload

    refused, reason = is_sa_refused(ikev2_packet)

    assert refused is False, "SA should not be considered refused"
    assert (
        reason == "Packet as an embedded SA cannot tell if refusing"
    ), "Reason should indicate SA is present"


def test_is_sa_refused_with_no_notify_or_sa():
    # Build an IKEv2 packet without SA payload and without relevant Notify payload
    ikev2_packet = IKEv2()

    refused, reason = is_sa_refused(ikev2_packet)

    assert refused is True, "SA should be considered refused"
    assert isinstance(reason, list), "Reason should be a list of notify types"
    assert len(reason) == 0, "No relevant Notify payloads found"


def test_build_sa_details():
    # Build an IKEv2 packet with an SA payload
    ciphers = [12]  # ENCR_AES_CBC
    key_ciphers = [256]
    prfs = [5]  # PRF_HMAC_SHA2_256
    integrities = [12]  # AUTH_HMAC_SHA2_256_128
    groupdescs = [19]  # DH_GROUP_19

    sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
    )

    ikev2_packet = IKEv2() / sa_payload

    details = build_sa_details(ikev2_packet)

    expected_details = (
        "Proposal 0:\n"
        "\tENCR: ENCR_AES_CBC\n"
        "\tPRF: PRF_HMAC_SHA2_256\n"
        "\tINTEG: AUTH_HMAC_SHA2_256_128\n"
        "\tDH: 256randECPgr\n"
    )

    assert (
        details.strip() == expected_details.strip()
    ), "SA details do not match expected"


""" TODO Update SecurityAssociation payload to be able to send SA with multiple proposal
def test_build_sa_details_with_multiple_proposals():
    # Build an IKEv2 packet with multiple SA proposals
    sa_payload_1 = SecurityAssociation(
        next_payload="SA",
        ciphers=[12],
        key_ciphers=[256],
        prfs=[5],
        integrities=[12],
        groupdescs=[19],
    )

    sa_payload_2 = SecurityAssociation(
        next_payload="None",
        ciphers=[14],
        key_ciphers=[256],
        prfs=[5],
        integrities=[12],
        groupdescs=[20],
    )

    ikev2_packet = IKEv2() / sa_payload_1 / sa_payload_2

    details = build_sa_details(ikev2_packet)

    expected_details = (
        f"Proposal 0:\n"
        f"\tEncryption Algorithm: ENCR_AES_CBC\n"
        f"\tPseudo-Random Function: PRF_HMAC_SHA2_256\n"
        f"\tIntegrity Algorithm: AUTH_HMAC_SHA2_256_128\n"
        f"\tDiffie-Hellman Group: DH_GROUP_19\n"
        f"Proposal 1:\n"
        f"\tEncryption Algorithm: ENCR_AES_CCM_8\n"
        f"\tPseudo-Random Function: PRF_HMAC_SHA2_256\n"
        f"\tIntegrity Algorithm: AUTH_HMAC_SHA2_256_128\n"
        f"\tDiffie-Hellman Group: DH_GROUP_20\n"
    )

    assert details.strip() == expected_details.strip(), "SA details with multiple proposals do not match expected"
"""


def test_build_sa_details_with_no_sa():
    # Build an IKEv2 packet without an SA payload
    ikev2_packet = IKEv2()

    details = build_sa_details(ikev2_packet)

    assert details == "", "Details should be empty when no SA is present"


def test_are_proposals_dr_with_missing_transforms():
    # Build an IKEv2 packet with an SA payload missing some transforms
    # For example, missing PRF or Integrity transform
    ciphers = [12]  # ENCR_AES_CBC
    key_ciphers = [256]
    prfs = []  # Missing PRF
    integrities = []  # Missing Integrity
    groupdescs = [19]  # DH_GROUP_19

    sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
    )

    ikev2_packet = IKEv2() / sa_payload

    # The function should handle missing transforms and still process correctly
    status, results = are_proposals_dr(ikev2_packet)

    assert (
        status is False
    ), "Proposals should not be DR compliant due to missing transforms"
    assert len(results) == 1, "Should have one proposal"
    proposal = results[0]
    assert proposal["dr"] is False, "Proposal should not be DR compliant"
    idx = list(proposal.keys())[0]
    non_dr_transforms = proposal[idx]
    # Since missing transforms are considered acceptable in the code, need to verify the logic
    # In the original code, missing Integrity transform is acceptable
    # Missing PRF is not acceptable
    # For this test, we can accept that the proposal is not compliant due to missing PRF
    assert {"type": 1, "id": 12} in non_dr_transforms, "AES CBC not DR"
    assert {
        "missing": "PRF"
    } in non_dr_transforms, "Missing PRF should be identified as non-compliant"


def test_are_proposals_dr_with_esn_absent():
    # Test when ESN is not included in the proposal, should default to acceptable
    ciphers = [13]  # ENCR_AES_CTR
    key_ciphers = [256]
    prfs = [5]
    integrities = [12]
    groupdescs = [19]
    esn = None  # ESN not included

    sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        esn=esn,
    )

    ikev2_packet = IKEv2() / sa_payload

    status, results = are_proposals_dr(ikev2_packet)

    assert (
        status is True
    ), "Proposals should be DR compliant even without ESN transform"
    proposal = results[0]
    assert proposal["dr"] is True, "Proposal should be DR compliant"
    idx = list(proposal.keys())[0]
    assert proposal[idx] == [], "There should be no non-compliant transforms"


def test_are_proposals_dr_with_invalid_transform_type():
    # Build a proposal with an invalid transform type to test error handling
    ciphers = [12]
    key_ciphers = [256]
    prfs = [5]
    integrities = [12]
    groupdescs = [19]

    # Manually construct an invalid transform
    invalid_transform = IKEv2_Transform(
        transform_type=99,  # Invalid transform type
        transform_id=999,
        length=8,
    )

    sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
    )

    # Append the invalid transform to the proposal
    sa_payload.prop.trans /= invalid_transform

    ikev2_packet = IKEv2() / sa_payload

    with pytest.raises(ValueError):
        are_proposals_dr(ikev2_packet)


def test_build_sa_details_with_invalid_transform_type():
    # Test build_sa_details with a proposal containing an invalid transform type
    ciphers = [12]
    key_ciphers = [256]
    prfs = [5]
    integrities = [12]
    groupdescs = [19]

    invalid_transform = IKEv2_Transform(
        transform_type=99,  # Invalid transform type
        transform_id=999,
        length=8,
    )

    sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
    )

    sa_payload.prop.trans /= invalid_transform

    ikev2_packet = IKEv2() / sa_payload
    details = build_sa_details(ikev2_packet)

    # The function should handle the invalid transform gracefully
    expected_details_start = "Proposal 0:\n"
    assert details.startswith(
        expected_details_start
    ), "Details should start with proposal information"

    # The invalid transform should be reported appropriately
    assert (
        "INVALID TRANSFORM" in details
    ), "Invalid transform type should be indicated in details"


def test_is_sa_refused_with_multiple_notifies():
    # Build an IKEv2 packet with multiple Notify payloads
    notify_payload1 = IKEv2_Notify(
        next_payload="Notify",
        type=14,  # NO_PROPOSAL_CHOSEN
        notify=b"",
    )

    notify_payload2 = IKEv2_Notify(
        next_payload="None",
        type=7,  # INVALID_SYNTAX
        notify=b"",
    )

    ikev2_packet = IKEv2() / notify_payload1 / notify_payload2

    refused, reason = is_sa_refused(ikev2_packet)

    assert refused is True, "SA should be considered refused"
    assert (
        reason == "NO_PROPOSAL_CHOSEN"
    ), "Reason should be NO_PROPOSAL_CHOSEN"

    # The function returns the first matching notify type in the order of preference


def test_are_proposals_dr_with_esn_present():
    # Test when ESN is included and compliant
    ciphers = [13]
    key_ciphers = [256]
    prfs = [5]
    integrities = [12]
    groupdescs = [19]
    esn = 1  # ESN, which is acceptable in DR

    sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        esn=esn,
    )

    ikev2_packet = IKEv2() / sa_payload

    status, results = are_proposals_dr(ikev2_packet)

    assert status is True, "Proposals should be DR compliant with ESN"
    proposal = results[0]
    assert proposal["dr"] is True, "Proposal should be DR compliant"


def test_are_proposals_dr_with_non_compliant_esn():
    # Test when ESN is included and not compliant
    ciphers = [13]
    key_ciphers = [256]
    prfs = [5]
    integrities = [12]
    groupdescs = [19]
    esn = 0  # ESN is disabled, which is not acceptable

    sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        esn=esn,
    )

    ikev2_packet = IKEv2() / sa_payload

    status, results = are_proposals_dr(ikev2_packet)

    assert status is False, "Proposals should not be DR compliant due to ESN"
    proposal = results[0]
    assert proposal["dr"] is False, "Proposal should not be DR compliant"
    idx = list(proposal.keys())[0]
    non_dr_transforms = proposal[idx]
    assert any(
        tfm["type"] == TRANSFORMS_TYPE["Extended_Sequence_Number"]
        for tfm in non_dr_transforms
    ), "Non-compliant ESN should be identified"


def test_ipcomp_dr_no_notify():
    """
    Test ipcomp_dr with a packet that does not include IPCOMP_SUPPORTED notify.
    Should return True.
    """
    # Build an IKEv2 packet without IPCOMP_SUPPORTED notify
    ciphers = [13]  # ENCR_AES_CTR
    key_ciphers = [256]
    prfs = [5]
    integrities = [12]
    groupdescs = [19]

    sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
    )

    ikev2_packet = IKEv2() / sa_payload

    result = ipcomp_dr(ikev2_packet)

    assert result is True, "IPCOMP_SUPPORTED notify should not be present."


def test_ipcomp_dr_with_notify():
    """
    Test ipcomp_dr with a packet that includes IPCOMP_SUPPORTED notify.
    Should return False.
    """
    # Build an IKEv2 packet with IPCOMP_SUPPORTED notify
    ciphers = [13]  # ENCR_AES_CTR
    key_ciphers = [256]
    prfs = [5]
    integrities = [12]
    groupdescs = [19]
    sa_payload = SecurityAssociation(
        next_payload="Notify",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
    )

    notify_payload = IKEv2_Notify(
        next_payload="None",
        type="IPCOMP_SUPPORTED",  # Assuming "IPCOMP_SUPPORTED" maps to a numeric value
        notify=b"",  # Empty notify data
    )

    ikev2_packet = IKEv2() / sa_payload / notify_payload

    result = ipcomp_dr(ikev2_packet)

    assert result is False, "IPCOMP_SUPPORTED notify should be present."


def test_ipcomp_dr_with_multiple_notifies():
    """
    Test ipcomp_dr with a packet that includes multiple notify payloads, one of which is IPCOMP_SUPPORTED.
    Should return False.
    """
    # Build an IKEv2 packet with multiple notify payloads including IPCOMP_SUPPORTED
    ciphers = [13]  # ENCR_AES_CTR
    key_ciphers = [256]
    prfs = [5]
    integrities = [12]
    groupdescs = [19]
    sa_payload = SecurityAssociation(
        next_payload="Notify",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
    )

    notify_payload1 = IKEv2_Notify(
        next_payload="Notify",
        type=7,  # INVALID_SYNTAX
        notify=b"",
    )

    notify_payload2 = IKEv2_Notify(
        next_payload="None",
        type=14,  # NO_PROPOSAL_CHOSEN
        notify=b"",
    )

    notify_payload3 = IKEv2_Notify(
        next_payload="None",
        type="IPCOMP_SUPPORTED",  # Assuming "IPCOMP_SUPPORTED" maps to a numeric value
        notify=b"",
    )

    ikev2_packet = (
        IKEv2()
        / sa_payload
        / notify_payload1
        / notify_payload2
        / notify_payload3
    )

    result = ipcomp_dr(ikev2_packet)

    assert (
        result is False
    ), "IPCOMP_SUPPORTED notify should be detected among multiple notifies."


def test_window_size_in_present():
    """
    Test window_size_in with a packet that includes SET_WINDOW_SIZE notify.
    Should return (True, notify_payload).
    """
    # Build an IKEv2 packet with SET_WINDOW_SIZE notify
    ciphers = [13]  # ENCR_AES_CTR
    key_ciphers = [256]
    prfs = [5]
    integrities = [12]
    groupdescs = [19]
    sa_payload = SecurityAssociation(
        next_payload="Notify",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
    )

    notify_payload = IKEv2_Notify(
        next_payload="None",
        type="SET_WINDOW_SIZE",  # Assuming "SET_WINDOW_SIZE" maps to a numeric value
        notify=b"\x00\x00\x00\x05",  # Example notification data (5)
    )

    ikev2_packet = IKEv2() / sa_payload / notify_payload

    result, notify = window_size_in(ikev2_packet)

    assert result is True, "SET_WINDOW_SIZE notify should be present."
    assert notify is not None, "Notify payload should be returned."
    assert (
        notify.type == "SET_WINDOW_SIZE"
    ), "Notify type should be SET_WINDOW_SIZE."


def test_window_size_in_absent():
    """
    Test window_size_in with a packet that does not include SET_WINDOW_SIZE notify.
    Should return (False, None).
    """
    # Build an IKEv2 packet without SET_WINDOW_SIZE notify
    sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=[13],
        key_ciphers=[256],
        prfs=[5],
        integrities=[12],
        groupdescs=[19],
    )

    ikev2_packet = IKEv2() / sa_payload

    result, notify = window_size_in(ikev2_packet)

    assert result is False, "SET_WINDOW_SIZE notify should not be present."
    assert notify is None, "No notify payload should be returned."


def test_window_size_in_with_multiple_notifies():
    """
    Test window_size_in with a packet that includes multiple notify payloads, including SET_WINDOW_SIZE.
    Should return (True, SET_WINDOW_SIZE notify).
    """
    # Build an IKEv2 packet with multiple notify payloads including SET_WINDOW_SIZE
    ciphers = [13]  # ENCR_AES_CTR
    key_ciphers = [256]
    prfs = [5]
    integrities = [12]
    groupdescs = [19]
    sa_payload = SecurityAssociation(
        next_payload="Notify",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
    )

    notify_payload1 = IKEv2_Notify(
        next_payload="Notify",
        type=7,  # INVALID_SYNTAX
        notify=b"",
    )

    notify_payload2 = IKEv2_Notify(
        next_payload="None",
        type="SET_WINDOW_SIZE",  # Assuming "SET_WINDOW_SIZE" maps to a numeric value
        notify=b"\x00\x00\x00\x05",  # Example notification data (5)
    )

    notify_payload3 = IKEv2_Notify(
        next_payload="None",
        type=14,  # NO_PROPOSAL_CHOSEN
        notify=b"",
    )

    ikev2_packet = (
        IKEv2()
        / sa_payload
        / notify_payload1
        / notify_payload2
        / notify_payload3
    )

    result, notify = window_size_in(ikev2_packet)

    assert (
        result is True
    ), "SET_WINDOW_SIZE notify should be detected among multiple notifies."
    assert notify is not None, "Notify payload should be returned."
    assert (
        notify.type == "SET_WINDOW_SIZE"
    ), "Notify type should be SET_WINDOW_SIZE."


def test_dr_esn_in_sa_compliant():
    """
    Test dr_esn_in_sa with an SA proposal that includes a compliant ESN transform.
    """
    ciphers = [13]  # ENCR_AES_CTR
    key_ciphers = [256]
    prfs = [5]  # PRF_HMAC_SHA2_256
    integrities = [12]  # AUTH_HMAC_SHA2_256_128
    groupdescs = [19]  # DH_GROUP_19
    esn = 1  # ESN enabled

    sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        esn=esn,
    )

    ikev2_packet = IKEv2() / sa_payload

    status, results = dr_esn_in_sa(ikev2_packet)

    assert status is True, "ESN should be compliant."
    assert len(results) == 1, "Should have one proposal."


def test_dr_esn_in_sa_non_compliant():
    """
    Test dr_esn_in_sa with an SA proposal that includes a non-compliant ESN transform.
    """
    ciphers = [13]  # ENCR_AES_CTR
    key_ciphers = [256]
    prfs = [5]  # PRF_HMAC_SHA2_256
    integrities = [12]  # AUTH_HMAC_SHA2_256_128
    groupdescs = [19]  # DH_GROUP_19
    esn = 0  # ESN disabled, not compliant

    sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        esn=esn,
    )

    ikev2_packet = IKEv2() / sa_payload

    status, results = dr_esn_in_sa(ikev2_packet)

    assert status is False, "ESN should be non-compliant when disabled."
    assert len(results) == 1, "Should have one proposal."
    proposal = results[0]
    from tests.support.logger import logger

    logger.debug(f"proposal {proposal}")
    assert (
        proposal[0][0]["type"] == 5
    ), "Proposal should contain ESN non-compliant."
    assert (
        proposal[0][0]["id"] == 0
    ), "Proposal should contain ESN non-compliant."


def test_dr_esn_in_sa_no_esn():
    """
    Test dr_esn_in_sa with an SA proposal that does not include an ESN transform.
    """
    ciphers = [13]  # ENCR_AES_CTR
    key_ciphers = [256]
    prfs = [5]  # PRF_HMAC_SHA2_256
    integrities = [12]  # AUTH_HMAC_SHA2_256_128
    groupdescs = [19]  # DH_GROUP_19
    esn = None  # ESN not included

    sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        esn=esn,
    )

    ikev2_packet = IKEv2() / sa_payload

    status, results = dr_esn_in_sa(ikev2_packet)

    assert (
        status is False
    ), "dr_esn_in_sa should return False by default when absent."
    assert len(results) == 1, "Should have one proposal."
    proposal = results[0]
    idx = 0
    assert proposal[idx] == [], "There should be no ESN transforms."


def test_dr_esn_in_sa_invalid_transform_type():
    """
    Test dr_esn_in_sa with an SA proposal that includes an invalid transform type.
    """
    ciphers = [13]  # ENCR_AES_CTR
    key_ciphers = [256]
    prfs = [5]  # PRF_HMAC_SHA2_256
    integrities = [12]  # AUTH_HMAC_SHA2_256_128
    groupdescs = [19]  # DH_GROUP_19
    esn = 1  # ESN enabled

    # Manually construct an invalid transform
    invalid_transform = IKEv2_Transform(
        transform_type=99,  # Invalid transform type
        transform_id=999,
        length=8,
    )

    sa_payload = SecurityAssociation(
        next_payload="None",
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        esn=esn,
    )

    # Append the invalid transform to the proposal
    sa_payload.prop.trans /= invalid_transform

    ikev2_packet = IKEv2() / sa_payload

    with pytest.raises(ValueError):
        dr_esn_in_sa(ikev2_packet)
