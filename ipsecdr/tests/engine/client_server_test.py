import pytest
from ipsecdr.engine.client import IPsecClient


@pytest.mark.asyncio
async def test_init_exchange(ipsec_setup):
    client_config, server_manager = ipsec_setup
    await server_manager.start()
    client = IPsecClient(client_config)
    try:
        await client.start()
        result = await client.ikev2_init()
        assert result, "INIT exchange failed"
    finally:
        await client.stop()
    await server_manager.stop()


@pytest.mark.asyncio
async def test_auth_exchange(ipsec_setup):
    client_config, server_manager = ipsec_setup
    await server_manager.start()
    client = IPsecClient(client_config)
    await client.start()
    result_init = await client.ikev2_init()
    assert result_init, "INIT exchange failed"
    result_auth = await client.ikev2_auth()
    assert result_auth, "AUTH exchange failed"
    await client.stop()
    await server_manager.stop()


@pytest.mark.asyncio
async def test_create_child_sa_exchange(ipsec_setup):
    client_config, server_manager = ipsec_setup
    await server_manager.start()
    client = IPsecClient(client_config)
    await client.start()
    result_init = await client.ikev2_init()
    assert result_init, "INIT exchange failed"
    result_auth = await client.ikev2_auth()
    assert result_auth, "AUTH exchange failed"
    result_child_sa = await client.ikev2_create_child_sa()
    assert result_child_sa, "CREATE_CHILD_SA exchange failed"
    await client.stop()
    await server_manager.stop()


@pytest.mark.asyncio
async def test_informational_exchange(ipsec_setup):
    client_config, server_manager = ipsec_setup
    await server_manager.start()
    client = IPsecClient(client_config)
    await client.start()
    result_init = await client.ikev2_init()
    assert result_init, "INIT exchange failed"
    result_auth = await client.ikev2_auth()
    assert result_auth, "AUTH exchange failed"

    # Sending INFORMATIONAL with a DELETE payload to close the SA
    await client.ikev2_informational(
        info_type="Delete",
        proto=1,
        SPIs=[client.spi_i, client.spi_r],
        next_payload="Delete",
    )
    # Since INFORMATIONAL exchange may not have a response, we assume success
    await client.stop()
    await server_manager.stop()


@pytest.mark.asyncio
async def test_basic_auth_with_sa_creation(ipsec_setup):
    client_config, server_manager = ipsec_setup
    await server_manager.start()
    client = IPsecClient(client_config)
    await client.start()
    result_init = await client.ikev2_init()
    assert result_init, "INIT exchange failed"
    result_auth = await client.ikev2_auth()
    assert result_auth, "AUTH exchange failed"
    # At this point, the IKE SA should be established
    await client.stop()
    await server_manager.stop()


"""
@pytest.mark.asyncio
async def test_childless_authentication(ipsec_setup):
    client_config, server_config = ipsec_setup

    # Create an overlay for the childless configuration
    childless_overlay = {"childless": True}
    client_config.update_from_dict(childless_overlay)
    server_config.update_from_dict(childless_overlay)

    client = IPsecClient(client_config)
    await client.start()
    result_init = await client.ikev2_init()
    assert result_init, "INIT exchange failed"
    result_auth = await client.ikev2_auth()
    assert result_auth, "AUTH exchange failed"
    # Verify that no CHILD SA was created
    assert (
        len(client.childs) == 0
    ), "CHILD SA was created in CHILDLESS authentication"
    await client.stop()
await server_manager.stop()"""
