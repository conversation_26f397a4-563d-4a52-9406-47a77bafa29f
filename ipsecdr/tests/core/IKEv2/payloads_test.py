import pytest
import secrets
from scapy.contrib.ikev2 import (
    IKEv2,
    IKEv2_CERTREQ,
    IKEv2_Encrypted,
    IKEv2_TSi,
    IKEv2_TSr,
    IKEv2_CP,
    IKEv2_AUTH,
    IKEv2_CERT,
    IKEv2_IDi,
    IKEv2_IDr,
    IKEv2_Notify,
    IKEv2_Nonce,
    IKEv2_KE,
    IKEv2_SA,
    IKEv2_Proposal,
    IPv4TrafficSelector,
)
from ipsecdr.core.IKEv2.payloads import (
    CERTREQ,
    Encrypted,
    TS,
    CP,
    gen_authentication_data,
    AUTH,
    CERT,
    ID,
    handle_notify_list,
    Notify,
    Nonce,
    KeyExchange,
    SecurityAssociation,
    prepare_ikev2_pkt,
)
from ipsecdr.core.IKEv2.utils import (
    get_notify_key,
)
from ipsecdr.core.IKEv2.constants import (
    TRANSFORMS_TYPE,
)
from ipsecdr.utils.models import NotifyPayload


def test_CERTREQ_valid(cert_paths):
    cert_path = cert_paths["ca_cert_path"]

    # Test with default parameters
    certreq_payload = CERTREQ(cert_path=cert_path)
    assert isinstance(
        certreq_payload, IKEv2_CERTREQ
    ), "CERTREQ did not return IKEv2_CERTREQ instance"
    assert certreq_payload.cert_encoding == 4  # Default cert_type
    assert certreq_payload.next_payload == 0
    assert certreq_payload.cert_authority, "cert_authority is empty"
    # Check that cert_authority length is correct (SHA256 hash length)
    assert (
        len(certreq_payload.cert_authority) == 32
    ), "cert_authority length is not 32 bytes (SHA256 hash)"


def test_CERTREQ_invalid_cert_type(cert_paths, caplog):
    # caplog is a pytest fixture to capture log output
    cert_path = cert_paths["ca_cert_path"]
    invalid_cert_type = 999  # Invalid cert_type

    certreq_payload = CERTREQ(cert_path=cert_path, cert_type=invalid_cert_type)
    assert (
        certreq_payload is None
    ), "CERTREQ should return None for invalid cert_type"
    assert "ERROR: Le choix du type de certificat" in caplog.text


def test_CERTREQ_invalid_cert_path():
    invalid_cert_path = "/non/existent/cert.pem"
    with pytest.raises(FileNotFoundError):
        CERTREQ(cert_path=invalid_cert_path)


def test_CERTREQ_invalid_htype(cert_paths):
    cert_path = cert_paths["ca_cert_path"]
    invalid_htype = 999  # Invalid htype
    # The function defaults to SHA1 if htype != 2
    certreq_payload = CERTREQ(cert_path=cert_path, htype=invalid_htype)
    assert isinstance(certreq_payload, IKEv2_CERTREQ)
    # Since htype != 2, it should use SHA1
    # Check that cert_authority length is 20 bytes (SHA1 hash length)
    assert (
        len(certreq_payload.cert_authority) == 20
    ), "cert_authority length is not 20 bytes (SHA1 hash)"


def test_Encrypted_valid():
    next_payload = "None"
    encrypted_data = b"\x01\x02\x03\x04"
    # Let Scapy compute the length
    encrypted_payload = Encrypted(
        next_payload=next_payload,
        length=None,  # Let Scapy compute length
        encrypted_chunk=encrypted_data,
    )
    assert isinstance(encrypted_payload, IKEv2_Encrypted)
    assert encrypted_payload.next_payload == 0  # Once in scapy pld None ~ 0
    assert encrypted_payload.load == encrypted_data
    # Build the packet and check that length field is set correctly
    raw_payload = bytes(encrypted_payload)
    # The length field is at offset 2-4 (big endian)
    length_field = int.from_bytes(raw_payload[2:4], byteorder="big")
    # The total length should be the length of the payload
    expected_length = len(raw_payload)
    assert (
        length_field == expected_length
    ), f"Length field {length_field} != expected length {expected_length}"


def test_Encrypted_empty_data():
    next_payload = "None"
    encrypted_data = b""
    encrypted_payload = Encrypted(
        next_payload=next_payload,
        length=None,
        encrypted_chunk=encrypted_data,
    )
    assert isinstance(encrypted_payload, IKEv2_Encrypted)
    assert encrypted_payload.load == encrypted_data
    # Check that the length field is correctly set
    raw_payload = bytes(encrypted_payload)
    length_field = int.from_bytes(raw_payload[2:4], byteorder="big")
    expected_length = len(raw_payload)
    assert (
        length_field == expected_length
    ), f"Length field {length_field} != expected length {expected_length}"
    # Check that the total length is as expected
    assert length_field == 4 + len(
        encrypted_data
    ), "Length field does not match expected length"


def test_TS_initiator_valid():
    mode = "Initiator"
    next_payload = "None"
    number_of_TSs = 1
    traffic_selector = ["***********", "*************", 0, 65535]

    ts_payload = TS(
        mode=mode,
        next_payload=next_payload,
        number_of_TSs=number_of_TSs,
        traffic_selector=traffic_selector,
    )
    assert isinstance(ts_payload, IKEv2_TSi)
    assert ts_payload.number_of_TSs == number_of_TSs
    ts = ts_payload.traffic_selector[0]
    assert isinstance(ts, IPv4TrafficSelector)
    assert ts.TS_type == 7  # TS_IPV4_ADDR_RANGE
    assert ts.starting_address_v4 == traffic_selector[0]
    assert ts.ending_address_v4 == traffic_selector[1]
    assert ts.start_port == traffic_selector[2]
    assert ts.end_port == traffic_selector[3]
    # Check that the payload builds correctly
    raw_payload = bytes(ts_payload)
    # The length field in the payload should match the length of the payload
    length_field = int.from_bytes(raw_payload[2:4], byteorder="big")
    expected_length = len(raw_payload)
    assert (
        length_field == expected_length
    ), f"Length field {length_field} != expected length {expected_length}"


def test_TS_responder_valid():
    mode = "Response"
    next_payload = "None"
    number_of_TSs = 1
    traffic_selector = ["********", "**********", 0, 65535]

    ts_payload = TS(
        mode=mode,
        next_payload=next_payload,
        number_of_TSs=number_of_TSs,
        traffic_selector=traffic_selector,
    )
    assert isinstance(ts_payload, IKEv2_TSr)
    assert ts_payload.number_of_TSs == number_of_TSs
    ts = ts_payload.traffic_selector[0]
    assert isinstance(ts, IPv4TrafficSelector)
    assert ts.TS_type == 7  # TS_IPV4_ADDR_RANGE
    assert ts.starting_address_v4 == traffic_selector[0]
    assert ts.ending_address_v4 == traffic_selector[1]
    assert ts.start_port == traffic_selector[2]
    assert ts.end_port == traffic_selector[3]
    raw_payload = bytes(ts_payload)
    length_field = int.from_bytes(raw_payload[2:4], byteorder="big")
    expected_length = len(raw_payload)
    assert (
        length_field == expected_length
    ), f"Length field {length_field} != expected length {expected_length}"


def test_TS_invalid_mode():
    mode = "InvalidMode"
    next_payload = "None"
    number_of_TSs = 1
    traffic_selector = ["***********", "*************", 0, 65535]

    with pytest.raises(ValueError):
        TS(
            mode=mode,
            next_payload=next_payload,
            number_of_TSs=number_of_TSs,
            traffic_selector=traffic_selector,
        )


def test_TS_invalid_traffic_selector():
    mode = "Initiator"
    next_payload = "None"
    number_of_TSs = 1
    traffic_selector = ["***********"]  # Incomplete list

    with pytest.raises(IndexError):
        TS(
            mode=mode,
            next_payload=next_payload,
            number_of_TSs=number_of_TSs,
            traffic_selector=traffic_selector,
        )


def test_TS_unsupported_number_of_TSs():
    mode = "Initiator"
    next_payload = "None"
    number_of_TSs = 2  # Only 1 supported
    traffic_selector = ["***********", "*************", 0, 65535]

    ts_payload = TS(
        mode=mode,
        next_payload=next_payload,
        number_of_TSs=number_of_TSs,
        traffic_selector=traffic_selector,
    )
    # Function does not raise error, but only one TS is supported
    assert ts_payload.number_of_TSs == number_of_TSs
    # Check that only one traffic selector is present

    assert (
        len(ts_payload.fields["traffic_selector"]) == 1
    ), f"Expected only 1 traffic selector, got {len(ts_payload.fields["traffic_selector"])}"


def test_CP_basic():
    next_payload = "None"
    cfg_type = 1
    attributes = None  # Since attributes handling is not fully implemented

    cp_payload = CP(
        next_payload=next_payload,
        cfg_type=cfg_type,
        attributes=attributes,
    )
    assert isinstance(cp_payload, IKEv2_CP)
    assert cp_payload.next_payload == 0
    assert cp_payload.CFGType == cfg_type
    # Since attributes are not implemented, check that attributes field is empty or default
    assert not cp_payload.attributes[
        0
    ], "Attributes should be empty or default"


def test_gen_authentication_data_initiator(ikev2_init_setup):
    # Extract necessary data from the fixture
    ikev2_crypto_i = ikev2_init_setup["ikev2_crypto_i"]
    ikev2_keys_i = ikev2_init_setup["ikev2_keys_i"]
    init_i_pkt = ikev2_init_setup["init_i_pkt"]
    init_r_pkt = ikev2_init_setup["init_r_pkt"]
    id_data = b"InitiatorID"
    auth_method = 9  # ECDSA_SECP256R1_SHA256

    auth_data = gen_authentication_data(
        mode="Initiator",
        init_i_pkt=init_i_pkt,
        init_r_pkt=init_r_pkt,
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        id_data=id_data,
        auth_method=auth_method,
    )
    assert isinstance(auth_data, bytes)
    assert auth_data, "Authentication data is empty"


def test_gen_authentication_data_responder(ikev2_init_setup):
    # Similar to the initiator test but for the responder
    ikev2_crypto_r = ikev2_init_setup["ikev2_crypto_r"]
    ikev2_keys_r = ikev2_init_setup["ikev2_keys_r"]
    init_i_pkt = ikev2_init_setup["init_i_pkt"]
    init_r_pkt = ikev2_init_setup["init_r_pkt"]
    id_data = b"ResponderID"
    auth_method = 9  # ECDSA_SECP256R1_SHA256

    auth_data = gen_authentication_data(
        mode="Response",
        init_i_pkt=init_i_pkt,
        init_r_pkt=init_r_pkt,
        ikev2_crypto=ikev2_crypto_r,
        ikev2_keys=ikev2_keys_r,
        id_data=id_data,
        auth_method=auth_method,
    )
    assert isinstance(auth_data, bytes)
    assert auth_data, "Authentication data is empty"


def test_gen_authentication_data_invalid_mode(ikev2_init_setup):
    ikev2_crypto_i = ikev2_init_setup["ikev2_crypto_i"]
    ikev2_keys_i = ikev2_init_setup["ikev2_keys_i"]
    init_i_pkt = ikev2_init_setup["init_i_pkt"]
    init_r_pkt = ikev2_init_setup["init_r_pkt"]
    id_data = b"InitiatorID"
    auth_method = 9

    with pytest.raises(ValueError):
        gen_authentication_data(
            mode="InvalidMode",
            init_i_pkt=init_i_pkt,
            init_r_pkt=init_r_pkt,
            ikev2_crypto=ikev2_crypto_i,
            ikev2_keys=ikev2_keys_i,
            id_data=id_data,
            auth_method=auth_method,
        )


def test_gen_authentication_data_missing_keys(ikev2_init_setup):
    ikev2_crypto_i = ikev2_init_setup["ikev2_crypto_i"]
    init_i_pkt = ikev2_init_setup["init_i_pkt"]
    init_r_pkt = ikev2_init_setup["init_r_pkt"]
    id_data = b"InitiatorID"
    auth_method = 9

    with pytest.raises(AttributeError):
        gen_authentication_data(
            mode="Initiator",
            init_i_pkt=init_i_pkt,
            init_r_pkt=init_r_pkt,
            ikev2_crypto=ikev2_crypto_i,
            ikev2_keys=None,  # Missing keys
            id_data=id_data,
            auth_method=auth_method,
        )


def test_AUTH_valid():
    next_payload = "None"
    auth_method = 9  # ECDSA_SECP256R1_SHA256
    auth_data = b"authentication_data"

    auth_payload = AUTH(
        next_payload=next_payload,
        auth_method=auth_method,
        auth_data=auth_data,
    )
    assert isinstance(auth_payload, IKEv2_AUTH)
    assert auth_payload.next_payload == 0
    assert auth_payload.auth_type == auth_method
    assert auth_payload.load == auth_data
    # Check that the length field is correct
    raw_payload = bytes(auth_payload)
    length_field = int.from_bytes(raw_payload[2:4], byteorder="big")
    expected_length = len(raw_payload)
    assert (
        length_field == expected_length
    ), f"Length field {length_field} != expected length {expected_length}"


def test_CERT_valid(ikev2_init_setup):
    ikev2_keys = ikev2_init_setup["ikev2_keys_i"]  # Use initiator's keys

    cert_payload = CERT(ikev2_keys=ikev2_keys)
    assert isinstance(cert_payload, IKEv2_CERT)
    # If multiple certificates, cert_payload will be a chain
    # Check that cert_data is not empty
    payloads = [p for p in cert_payload.iterpayloads()]
    assert len(payloads) == len(
        ikev2_keys.certificat_trusted_chain
    ), "Number of certificates in payload does not match trusted chain"
    for p in payloads:
        assert p.cert_data, "Certificate data is empty"
        assert p.cert_encoding == 4  # X509_CERT_SIG


def test_ID_initiator():
    mode = "Initiator"
    next_payload = "None"
    id_type = "FQDN"
    id_data = "ClientID"

    id_payload = ID(
        mode=mode,
        next_payload=next_payload,
        id_type=id_type,
        id_data=id_data,
    )
    assert isinstance(id_payload, IKEv2_IDi)
    assert id_payload.IDtype == 2  # FQDN = 2
    assert id_payload.ID == id_data.encode("ascii")
    # Check that the length field is correct
    raw_payload = bytes(id_payload)
    length_field = int.from_bytes(raw_payload[2:4], byteorder="big")
    expected_length = len(raw_payload)
    assert (
        length_field == expected_length
    ), f"Length field {length_field} != expected length {expected_length}"


def test_ID_responder():
    mode = "Response"
    next_payload = "None"
    id_type = "FQDN"
    id_data = "server.example.com"

    id_payload = ID(
        mode=mode,
        next_payload=next_payload,
        id_type=id_type,
        id_data=id_data,
    )
    assert isinstance(id_payload, IKEv2_IDr)
    assert id_payload.IDtype == 2
    assert id_payload.ID == id_data.encode("ascii")
    raw_payload = bytes(id_payload)
    length_field = int.from_bytes(raw_payload[2:4], byteorder="big")
    expected_length = len(raw_payload)
    assert (
        length_field == expected_length
    ), f"Length field {length_field} != expected length {expected_length}"


def test_ID_invalid_mode():
    mode = "InvalidMode"
    next_payload = "None"
    id_type = "ID_KEY_ID"
    id_data = "ClientID"

    with pytest.raises(ValueError):
        ID(
            mode=mode,
            next_payload=next_payload,
            id_type=id_type,
            id_data=id_data,
        )


def test_handle_notify_list_valid():
    notify_list = [
        NotifyPayload(type="INITIAL_CONTACT", notify=b""),
        NotifyPayload(type="COOKIE", notify=b"cookie_data"),
    ]

    notify_payloads = handle_notify_list(
        notify_list=notify_list,
        next_payload="None",
    )
    assert notify_payloads
    # Since payloads can be a chain, we iterate over them
    types = []
    notifies = []
    for payload in notify_payloads.iterpayloads():
        assert isinstance(payload, IKEv2_Notify)
        types.append(payload.type)
        notifies.append(payload.notify)
    assert types == [
        get_notify_key("INITIAL_CONTACT"),
        get_notify_key("COOKIE"),
    ]
    assert notifies == [b"", b"cookie_data"]
    # Check that the next_payload fields are set correctly
    payloads = [p for p in notify_payloads.iterpayloads()]
    for i, p in enumerate(payloads):
        expected_next_payload = 0 if i == len(payloads) - 1 else 41
        assert (
            p.next_payload == expected_next_payload
        ), f"Incorrect next_payload for payload {i}"


def test_handle_notify_list_empty():
    notify_list = []
    notify_payloads = handle_notify_list(notify_list=notify_list)
    assert notify_payloads is None, "Expected None when notify_list is empty"


def test_Notify_valid():
    next_payload = "None"
    notify_type = "COOKIE"
    notify_data = b"cookie_data"

    notify_payload = Notify(
        next_payload=next_payload,
        notify_type=notify_type,
        notify=notify_data,
    )
    assert isinstance(notify_payload, IKEv2_Notify)
    assert notify_payload.next_payload == 0
    assert notify_payload.type == get_notify_key(notify_type)
    assert notify_payload.notify == notify_data
    # Check that the length field is correct
    raw_payload = bytes(notify_payload)
    length_field = int.from_bytes(raw_payload[2:4], byteorder="big")
    expected_length = len(raw_payload)
    assert (
        length_field == expected_length
    ), f"Length field {length_field} != expected length {expected_length}"


def test_Notify_invalid_notify_type():
    next_payload = "None"
    notify_type = "INVALID_NOTIFY"
    notify_data = b""

    with pytest.raises(KeyError):
        Notify(
            next_payload=next_payload,
            notify_type=notify_type,
            notify=notify_data,
        )


def test_Nonce_valid():
    next_payload = "None"
    nonce_data = secrets.token_bytes(32)

    nonce_payload = Nonce(
        next_payload=next_payload,
        nonce=nonce_data,
    )
    assert isinstance(nonce_payload, IKEv2_Nonce)
    assert nonce_payload.next_payload == 0
    assert nonce_payload.nonce == nonce_data
    # Check that the length field is correct
    raw_payload = bytes(nonce_payload)
    length_field = int.from_bytes(raw_payload[2:4], byteorder="big")
    expected_length = len(raw_payload)
    assert (
        length_field == expected_length
    ), f"Length field {length_field} != expected length {expected_length}"


def test_KeyExchange_valid():
    next_payload = "None"
    group = 19  # SECP256R1
    ke_data = secrets.token_bytes(64)

    ke_payload = KeyExchange(
        next_payload=next_payload,
        group=group,
        ke=ke_data,
    )
    assert isinstance(ke_payload, IKEv2_KE)
    assert ke_payload.next_payload == 0
    assert ke_payload.group == group
    assert ke_payload.ke == ke_data
    # Check that the length field is correct
    raw_payload = bytes(ke_payload)
    length_field = int.from_bytes(raw_payload[2:4], byteorder="big")
    expected_length = len(raw_payload)
    assert (
        length_field == expected_length
    ), f"Length field {length_field} != expected length {expected_length}"


def test_SecurityAssociation_valid():
    next_payload = "None"
    ciphers = [12]  # ENCR_AES_CBC
    key_ciphers = [256]
    prfs = [5]  # PRF_HMAC_SHA2_256
    groupdescs = [19]  # SECP256R1
    integrities = [12]  # AUTH_HMAC_SHA2_256_128

    sa_payload = SecurityAssociation(
        next_payload=next_payload,
        ciphers=ciphers,
        key_ciphers=key_ciphers,
        prfs=prfs,
        groupdescs=groupdescs,
        integrities=integrities,
    )
    assert isinstance(sa_payload, IKEv2_SA)
    proposal = sa_payload.prop
    assert isinstance(proposal, IKEv2_Proposal)
    assert proposal.proto == 1  # IKE protocol
    # Verify the transforms
    transforms = [tfm for tfm in proposal.trans.iterpayloads()]
    expected_transforms = (
        len(ciphers) + len(prfs) + len(integrities) + len(groupdescs)
    )
    assert (
        len(transforms) == expected_transforms
    ), "Number of transforms does not match"
    # Verify each transform
    for tfm in transforms:
        if tfm.transform_type == TRANSFORMS_TYPE["Encryption"]:
            assert (
                tfm.transform_id in ciphers
            ), "Unexpected cipher transform ID"
            assert tfm.key_length in key_ciphers, "Unexpected key length"
        elif tfm.transform_type == TRANSFORMS_TYPE["PRF"]:
            assert tfm.transform_id in prfs, "Unexpected PRF transform ID"
        elif tfm.transform_type == TRANSFORMS_TYPE["Integrity"]:
            assert (
                tfm.transform_id in integrities
            ), "Unexpected integrity transform ID"
        elif tfm.transform_type == TRANSFORMS_TYPE["GroupDesc"]:
            assert (
                tfm.transform_id in groupdescs
            ), "Unexpected groupdesc transform ID"
        else:
            assert False, f"Unexpected transform type: {tfm.transform_type}"


def test_prepare_ikev2_pkt(ikev2_init_setup):
    spi_i = ikev2_init_setup["spi_i"]
    spi_r = ikev2_init_setup["spi_r"]
    mid = 1
    mode = "Initiator"
    xchg_type = "IKE_AUTH"
    next_payload = "None"
    ikev2_crypto = ikev2_init_setup["ikev2_crypto_i"]
    ikev2_keys = ikev2_init_setup["ikev2_keys_i"]
    plaintext_chunk = IKEv2_Nonce(nonce=b"Test payload")

    ikev2_packet = prepare_ikev2_pkt(
        spi_i=spi_i,
        spi_r=spi_r,
        mid=mid,
        mode=mode,
        xchg_type=xchg_type,
        next_payload=next_payload,
        ikev2_crypto=ikev2_crypto,
        ikev2_keys=ikev2_keys,
        ikev2_frame=plaintext_chunk,
    )
    assert isinstance(ikev2_packet, IKEv2)
    assert ikev2_packet.init_SPI == spi_i
    assert ikev2_packet.resp_SPI == spi_r
    assert ikev2_packet.exch_type == 35  # IKE_AUTH
    assert ikev2_packet.flags == mode
    # Check that the Encrypted payload is present
    encrypted_payload = ikev2_packet[IKEv2_Encrypted]
    assert encrypted_payload is not None, "Encrypted payload is missing"
    # Since the payload is encrypted, we cannot verify the inner contents without decrypting
    # Optionally, we can attempt to decrypt and verify the contents, but that would be complex
    # For now, we can check that the length fields are correct
    total_length = len(bytes(ikev2_packet))
    assert (
        ikev2_packet.length == total_length
    ), f"Packet length {ikev2_packet.length} != actual length {total_length}"
