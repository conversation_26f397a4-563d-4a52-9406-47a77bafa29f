import secrets
import pytest
from scapy.contrib.ikev2 import IKEv2, IKEv2_Delete
from ipsecdr.core.IKEv2.informational import forge_ikev2_informational
from ipsecdr.core.IKEv2.utils import uncipher_ike_pkt, get_notify_key
from ipsecdr.core.IKEv2.payloads import Notify
from tests.support.helpers import create_ikev2_sa


def test_forge_ikev2_informational_basic():
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)
    (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        _,
        _,
        _,
        _,
    ) = create_ikev2_sa(spi_i=spi_i, spi_r=spi_r)

    mode = "Initiator"
    mid = 3  # Message ID after IKE_AUTH exchange
    data = b"Test informational dummy data"
    next_payload = "None"

    informational_packet = forge_ikev2_informational(
        mode=mode,
        spi_i=spi_i,
        spi_r=spi_r,
        mid=mid,
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        data=data,
        next_payload=next_payload,
    )

    assert isinstance(
        informational_packet, IKEv2
    ), "Returned packet is not an instance of IKEv2"
    assert informational_packet.init_SPI == spi_i, "Incorrect Initiator SPI"
    assert informational_packet.resp_SPI == spi_r, "Incorrect Responder SPI"
    assert (
        informational_packet.exch_type == 37
    ), "Incorrect exchange type for INFORMATIONAL"
    assert informational_packet.flags == mode, "Incorrect mode in packet flags"

    encrypted_payload = informational_packet["IKEv2_Encrypted"]
    assert encrypted_payload, "Encrypted payload is missing"

    decrypted_payload = uncipher_ike_pkt(
        ikev2_crypto=ikev2_crypto_r,
        ikev2_keys=ikev2_keys_r,
        ike_packet=informational_packet,
    )

    data = data + b"\x00"

    assert decrypted_payload.load == data, "Decrypted data mismatch"


def test_forge_ikev2_informational_with_notify():
    # Step 1: Generate cryptographic parameters and initial IKEv2 packets
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)
    (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        _,
        _,
        _,
        _,
    ) = create_ikev2_sa(spi_i=spi_i, spi_r=spi_r)

    # Step 2: Define test inputs
    mode = "Response"
    mid = 3
    notify_type = "COOKIE"
    notify_data = b"cookie_data"
    next_payload = "Notify"

    # Build the Notify payload data
    notify_payload = Notify(
        next_payload="None",
        notify_type=notify_type,
        notify=notify_data,
    )

    # Step 3: Call the forge_ikev2_informational function
    informational_packet = forge_ikev2_informational(
        mode=mode,
        spi_i=spi_i,
        spi_r=spi_r,
        mid=mid,
        ikev2_crypto=ikev2_crypto_r,
        ikev2_keys=ikev2_keys_r,
        data=bytes(notify_payload),
        next_payload=next_payload,
    )

    # Step 4: Basic assertions
    assert isinstance(
        informational_packet, IKEv2
    ), "Returned packet is not an instance of IKEv2"
    assert (
        informational_packet.exch_type == 37
    ), "Incorrect exchange type for INFORMATIONAL"
    assert informational_packet.flags == mode, "Incorrect mode in packet flags"

    # Step 5: Decrypt the encrypted payload
    decrypted_payload = uncipher_ike_pkt(
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        ike_packet=informational_packet,
    )

    # Step 6: Verify the Notify payload
    assert decrypted_payload.haslayer("IKEv2_Notify"), "Notify payload missing"
    notify_layer = decrypted_payload["IKEv2_Notify"]
    assert notify_layer.type == get_notify_key(
        notify_type
    ), "Notify type mismatch"
    assert notify_layer.notify == notify_data, "Notify data mismatch"


def test_forge_ikev2_informational_invalid_parameters():
    # Attempt to forge an informational packet with invalid parameters
    # Expecting the function to raise an exception or handle the error
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)
    ikev2_crypto = None  # Invalid ikev2_crypto
    ikev2_keys = None  # Invalid ikev2_keys
    mode = "Initiator"
    mid = 1

    with pytest.raises(Exception):
        forge_ikev2_informational(
            mode=mode,
            spi_i=spi_i,
            spi_r=spi_r,
            mid=mid,
            ikev2_crypto=ikev2_crypto,
            ikev2_keys=ikev2_keys,
            data=b"Test",
            next_payload="None",
        )


def test_forge_ikev2_informational_no_data():
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)
    (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        _,
        _,
        _,
        _,
    ) = create_ikev2_sa(spi_i=spi_i, spi_r=spi_r)

    mode = "Initiator"
    mid = 3

    informational_packet = forge_ikev2_informational(
        mode=mode,
        spi_i=spi_i,
        spi_r=spi_r,
        mid=mid,
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        data=b"",  # No data
        next_payload="None",
    )

    # Assertions
    assert isinstance(
        informational_packet, IKEv2
    ), "Returned packet is not an instance of IKEv2"
    assert informational_packet.init_SPI == spi_i, "Incorrect Initiator SPI"

    decrypted_payload = uncipher_ike_pkt(
        ikev2_crypto=ikev2_crypto_r,
        ikev2_keys=ikev2_keys_r,
        ike_packet=informational_packet,
    )
    assert (
        decrypted_payload.payload.load == b"\x00"
    ), "Expected no payload in decrypted data"


def test_forge_ikev2_informational_with_delete_payload():
    # Test forging an informational packet with a Delete payload
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)
    (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        _,
        _,
        _,
        _,
    ) = create_ikev2_sa(spi_i=spi_i, spi_r=spi_r)

    mode = "Initiator"
    mid = 3
    next_payload = "Delete"

    # Build the Delete payload
    delete_payload = IKEv2_Delete(
        next_payload="None",
        proto=1,  # IKEv2
        SPInum=1,
        SPI=spi_r,
    )

    informational_packet = forge_ikev2_informational(
        mode=mode,
        spi_i=spi_i,
        spi_r=spi_r,
        mid=mid,
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        data=bytes(delete_payload),
        next_payload=next_payload,
    )

    # Assertions
    assert isinstance(
        informational_packet, IKEv2
    ), "Returned packet is not an instance of IKEv2"
    assert (
        informational_packet.exch_type == 37
    ), "Incorrect exchange type for INFORMATIONAL"

    # Decrypt and verify the Delete payload
    decrypted_payload = uncipher_ike_pkt(
        ikev2_crypto=ikev2_crypto_r,
        ikev2_keys=ikev2_keys_r,
        ike_packet=informational_packet,
    )

    assert decrypted_payload.haslayer(
        "IKEv2_Delete"
    ), "Delete payload is missing in decrypted data"
    delete_layer = decrypted_payload["IKEv2_Delete"]
    assert delete_layer.SPI[0] == spi_r, "SPI in Delete payload mismatch"


def test_forge_ikev2_informational_with_multiple_payloads():
    # Test forging an informational packet with multiple payloads
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)
    (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        _,
        _,
        _,
        _,
    ) = create_ikev2_sa(spi_i=spi_i, spi_r=spi_r)

    mode = "Initiator"
    mid = 3

    # Build multiple payloads
    notify_payload = Notify(
        next_payload="Delete",
        notify_type="COOKIE",
        notify=b"cookie_data",
    )
    delete_payload = IKEv2_Delete(
        next_payload="None",
        proto=1,  # IKEv2
        SPInum=1,
        SPI=spi_r,
    )
    payload_data = bytes(notify_payload / delete_payload)

    informational_packet = forge_ikev2_informational(
        mode=mode,
        spi_i=spi_i,
        spi_r=spi_r,
        mid=mid,
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        data=payload_data,
        next_payload="Notify",
    )
    assert isinstance(
        informational_packet, IKEv2
    ), "Returned packet is not an instance of IKEv2"

    decrypted_payload = uncipher_ike_pkt(
        ikev2_crypto=ikev2_crypto_r,
        ikev2_keys=ikev2_keys_r,
        ike_packet=informational_packet,
    )

    assert decrypted_payload.haslayer("IKEv2_Notify"), "Notify payload missing"
    notify_layer = decrypted_payload["IKEv2_Notify"]
    assert notify_layer.notify == b"cookie_data", "Notify data mismatch"

    assert decrypted_payload.haslayer(IKEv2_Delete), "Delete payload missing"
    delete_layer = decrypted_payload[IKEv2_Delete]
    assert delete_layer.SPI[0] == spi_r, "SPI in Delete payload mismatch"
