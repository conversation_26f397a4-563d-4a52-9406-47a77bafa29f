import secrets
from scapy.contrib.ikev2 import IKEv2
from ipsecdr.core.IKEv2.auth import forge_ikev2_auth
from ipsecdr.core.IKEv2.utils import uncipher_ike_pkt
from ipsecdr.utils.models import NotifyPayload
from tests.support.helpers import (
    create_random_ikev2_init,
    create_ikev2_sa,
)


def test_forge_ikev2_auth_basic():
    # Use random cryptographic values
    # and initial packets generated by create_random_ikev2_sa
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)
    (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        init_i_pkt,
        init_r_pkt,
    ) = create_random_ikev2_init(
        spi_i=spi_i,
        spi_r=spi_r,
    )
    # Define test inputs for the forge_ikev2_auth function
    mode = "Initiator"
    spi_i = 0x12345678
    spi_r = 0x87654321
    mid = 1
    esn = 0
    notify_options = [NotifyPayload(type="INITIAL_CONTACT", notify=b"")]
    idi_type = 1  # IDi type (example value)
    idi_data = "***********"  # Example IDi data
    idr_type = 1  # IDr type (example value)
    idr_data = "***********"  # Example IDr data
    traffic_selector_client = ["***********", "*************", 0, 65535]
    traffic_selector_server = ["***********", "*************", 0, 65535]
    auth_method = 9  # Example authentication method (like RSA)
    # Call the forge_ikev2_auth function for Initiator
    ikev2_auth_packet = forge_ikev2_auth(
        mode=mode,
        spi_i=spi_i,
        spi_r=spi_r,
        mid=mid,
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        esn=esn,
        notify_options=notify_options,
        idi_type=idi_type,
        idi_data=idi_data,
        idr_type=idr_type,
        idr_data=idr_data,
        traffic_selector_client=traffic_selector_client,
        traffic_selector_server=traffic_selector_server,
        init_i_pkt=init_i_pkt,  # Initiator's IKE_SA_INIT packet
        init_r_pkt=init_r_pkt,  # Responder's IKE_SA_INIT packet
        auth_method=auth_method,
    )

    # Basic assertions to check the validity of the returned packet
    assert isinstance(
        ikev2_auth_packet, IKEv2
    )  # Check if the return type is correct
    assert ikev2_auth_packet.init_SPI == spi_i  # Verify the initiator SPI
    assert ikev2_auth_packet.resp_SPI == spi_r  # Verify the responder SPI
    assert ikev2_auth_packet["IKEv2_Encrypted"]
    # Verify the IDi and IDr payloads


def test_forge_ikev2_auth_full():
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)
    (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        init_i_pkt,
        init_r_pkt,
    ) = create_random_ikev2_init(
        spi_i=spi_i,
        spi_r=spi_r,
    )

    # Define test inputs for AUTH
    mode = "Initiator"
    spi_i = 0x12345678
    spi_r = 0x87654321
    mid = 1
    esn = 0
    notify_options = [
        NotifyPayload(type="HTTP_CERT_LOOKUP_SUPPORTED", notify=b"")
    ]
    idi_type = 1  # IDi type example
    idi_data = "***********"
    idr_type = 1  # IDr type example
    idr_data = "***********"
    traffic_selector_client = ["***********", "*************", 0, 65535]
    traffic_selector_server = ["***********", "*************", 0, 65535]
    auth_method = 9  # Example authentication method

    # Call the forge_ikev2_auth function for Initiator
    ikev2_auth_packet = forge_ikev2_auth(
        mode=mode,
        spi_i=spi_i,
        spi_r=spi_r,
        mid=mid,
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        esn=esn,
        notify_options=notify_options,
        idi_type=idi_type,
        idi_data=idi_data,
        idr_type=idr_type,
        idr_data=idr_data,
        traffic_selector_client=traffic_selector_client,
        traffic_selector_server=traffic_selector_server,
        init_i_pkt=init_i_pkt,  # Initiator's IKE_SA_INIT packet
        init_r_pkt=init_r_pkt,  # Responder's IKE_SA_INIT packet
        auth_method=auth_method,
    )

    # Basic assertions to check the validity of the returned packet
    assert isinstance(
        ikev2_auth_packet, IKEv2
    ), "IKEv2_AUTH packet is not IKEv2"
    assert (
        ikev2_auth_packet.init_SPI == spi_i
    ), "Incorrect Initiator SPI in AUTH"
    assert (
        ikev2_auth_packet.resp_SPI == spi_r
    ), "Incorrect Responder SPI in AUTH"

    # Verify encryption
    assert ikev2_auth_packet[
        "IKEv2_Encrypted"
    ], "IKEv2_AUTH payload is not encrypted"
    decrypted_auth = uncipher_ike_pkt(
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        ike_packet=ikev2_auth_packet,
    )
    # Verify IDi and IDr
    if decrypted_auth.haslayer("IKEv2_IDi"):
        assert decrypted_auth["IKEv2_IDi"].ID == idi_data, "IDi mismatch"
    if decrypted_auth.haslayer("IKEv2_IDr"):
        assert decrypted_auth["IKEv2_IDr"].ID == idr_data, "IDr mismatch"

    # Verify traffic selectors (TSi and TSr)
    assert (
        decrypted_auth["IKEv2_TSi"]["IPv4TrafficSelector"].starting_address_v4
        == traffic_selector_client[0]
    ), "TSi IP mismatch"
    assert (
        decrypted_auth["IKEv2_TSr"]["IPv4TrafficSelector"].starting_address_v4
        == traffic_selector_server[0]
    ), "TSr IP mismatch"


def test_ikev2_auth_full():
    # Test IKEv2_AUTH message generation and encryption
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)
    (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        init_i_pkt,
        init_r_pkt,
        ikev2_auth_i_pkt,
        ikev2_auth_r_pkt,
    ) = create_ikev2_sa(spi_i=spi_i, spi_r=spi_r)
    traffic_selector_client = ["***********", "*************", 0, 65535]
    traffic_selector_server = ["***********", "*************", 0, 65535]
    # Basic assertions to check the validity of returned packets
    assert isinstance(
        ikev2_auth_i_pkt, IKEv2
    ), "IKEv2_AUTH packet is not IKEv2"
    assert (
        ikev2_auth_i_pkt.init_SPI == spi_i
    ), "Incorrect Initiator SPI in AUTH"
    assert (
        ikev2_auth_i_pkt.resp_SPI == spi_r
    ), "Incorrect Responder SPI in AUTH"
    assert isinstance(
        ikev2_auth_r_pkt, IKEv2
    ), "IKEv2_AUTH packet is not IKEv2"
    assert (
        ikev2_auth_r_pkt.init_SPI == spi_i
    ), "Incorrect Initiator SPI in AUTH"
    assert (
        ikev2_auth_r_pkt.resp_SPI == spi_r
    ), "Incorrect Responder SPI in AUTH"
    # Verify encryption
    assert ikev2_auth_i_pkt[
        "IKEv2_Encrypted"
    ], "IKEv2_AUTH payload is not encrypted"
    assert ikev2_auth_r_pkt[
        "IKEv2_Encrypted"
    ], "IKEv2_AUTH payload is not encrypted"
    decrypted_auth_i = uncipher_ike_pkt(
        ikev2_crypto=ikev2_crypto_r,
        ikev2_keys=ikev2_keys_r,
        ike_packet=ikev2_auth_i_pkt,
    )
    decrypted_auth_r = uncipher_ike_pkt(
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        ike_packet=ikev2_auth_r_pkt,
    )

    # Verify IDi and IDr
    if decrypted_auth_i.haslayer("IKEv2_IDi"):
        assert (
            decrypted_auth_i["IKEv2_IDi"].ID == "***********"
        ), "Initiator IDi mismatch"
        assert (
            decrypted_auth_i["IKEv2_IDi"].IDtype == 1
        ), "Initiator IDi type mismatch"
    if decrypted_auth_i.haslayer("IKEv2_IDr"):
        assert (
            decrypted_auth_i["IKEv2_IDr"].IDtype == 1
        ), "Initiator IDr type mismatch"
        assert (
            decrypted_auth_i["IKEv2_IDr"].ID == "***********"
        ), "Initiator IDir mismatch"

    if decrypted_auth_r.haslayer("IKEv2_IDr"):
        assert (
            decrypted_auth_r["IKEv2_IDr"].IDtype == 1
        ), "Responder IDr type mismatch"
        assert (
            decrypted_auth_r["IKEv2_IDr"].ID == "***********"
        ), "Responder IDr mismatch"

    # Verify traffic selectors (TSi and TSr)
    assert (
        decrypted_auth_i["IKEv2_TSi"][
            "IPv4TrafficSelector"
        ].starting_address_v4
        == traffic_selector_client[0]
    ), "TSi IP mismatch"
    assert (
        decrypted_auth_i["IKEv2_TSr"][
            "IPv4TrafficSelector"
        ].starting_address_v4
        == traffic_selector_server[0]
    ), "TSr IP mismatch"
    assert (
        decrypted_auth_r["IKEv2_TSi"][
            "IPv4TrafficSelector"
        ].starting_address_v4
        == traffic_selector_client[0]
    ), "TSi IP mismatch"
    assert (
        decrypted_auth_r["IKEv2_TSr"][
            "IPv4TrafficSelector"
        ].starting_address_v4
        == traffic_selector_server[0]
    ), "TSr IP mismatch"
