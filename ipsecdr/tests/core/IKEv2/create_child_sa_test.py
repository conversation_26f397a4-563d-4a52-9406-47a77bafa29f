import pytest
import secrets
from scapy.contrib.ikev2 import IKEv2
from ipsecdr.core.IKEv2.create_child import forge_ikev2_create_child_sa
from ipsecdr.core.IKEv2.utils import (
    uncipher_ike_pkt,
    check_sa_transforms_match,
    get_notify_key,
)
from ipsecdr.utils.models import NotifyPayload
from tests.support.helpers import (
    create_ikev2_sa,
)


def test_forge_ikev2_create_child_sa_basic():
    # Step 1: Generate cryptographic parameters and initial IKEv2 packets
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)
    (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        init_i_pkt,
        init_r_pkt,
        ikev2_auth_i_pkt,
        ikev2_auth_r_pkt,
    ) = create_ikev2_sa(spi_i=spi_i, spi_r=spi_r)
    # Step 2: Define test inputs for the forge_ikev2_create_child_sa function
    mode = "Response"
    mid = 2  # Increment message ID
    esn = 1  # Extended Sequence Numbers
    notify_options = [NotifyPayload(type="REKEY_SA", notify=b"")]
    traffic_selector_client = ["*************", "*************", 0, 65535]
    traffic_selector_server = ["********", "**********", 0, 65535]
    ciphers = [20]  # Example cipher ENCR_AES_CTR
    key_lengths = [256]
    prfs = [5]  # PRF_HMAC_SHA2_256
    integrities = None  # AUTH_HMAC_SHA2_256_128
    groupdescs = [19]  # SECP256R1
    nonce = secrets.token_bytes(32)
    KE = ikev2_crypto_i.dh.public_key  # Use the initiator's DH public key
    notify_extras = [NotifyPayload(type="INITIAL_CONTACT", notify=b"")]

    # Step 3: Call the forge_ikev2_create_child_sa function
    create_child_sa_packet = forge_ikev2_create_child_sa(
        mode=mode,
        spi_i=spi_i,
        spi_r=spi_r,
        mid=mid,
        ikev2_crypto=ikev2_crypto_r,
        ikev2_keys=ikev2_keys_r,
        traffic_selector_client=traffic_selector_client,
        traffic_selector_server=traffic_selector_server,
        ciphers=ciphers,
        key_lengths=key_lengths,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        nonce=nonce,
        KE=KE,
        esn=esn,
        notify_options=notify_options,
        notify_extras=notify_extras,
    )

    # Step 4: Basic assertions to check the validity of the returned packet
    assert isinstance(
        create_child_sa_packet, IKEv2
    ), "Returned packet is not an instance of IKEv2"
    assert create_child_sa_packet.init_SPI == spi_i, "Incorrect Initiator SPI"
    assert create_child_sa_packet.resp_SPI == spi_r, "Incorrect Responder SPI"
    assert (
        create_child_sa_packet.exch_type == 36
    ), "Incorrect exchange type for CREATE_CHILD_SA"
    assert (
        create_child_sa_packet.flags == mode
    ), "Incorrect mode in packet flags"

    # Step 5: Check the Encrypted payload
    encrypted_payload = create_child_sa_packet["IKEv2_Encrypted"]
    assert encrypted_payload, "Encrypted payload is missing"

    # Step 6: Decrypt the encrypted payload for further verification
    decrypted_payload = uncipher_ike_pkt(
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        ike_packet=create_child_sa_packet,
    )

    # Step 7: Verify the decrypted payload contains expected payloads
    payload_types = [p.name for p in decrypted_payload.iterpayloads()]
    expected_payloads = [
        "IKEv2 SA",
        "IKEv2 Nonce",
        "IKEv2 Key Exchange",
        "IKEv2 Traffic Selector - Initiator",
        "IKEv2 Traffic Selector - Responder",
    ]

    for payload in expected_payloads:
        assert payload in payload_types, f"Missing expected payload: {payload}"

    # Step 8: Verify the Nonce
    nonce_payload = decrypted_payload["IKEv2_Nonce"]
    assert nonce_payload.nonce == nonce, "Nonce mismatch"

    # Step 9: Verify the Key Exchange payload
    ke_payload = decrypted_payload["IKEv2_KE"]
    assert ke_payload.group == groupdescs[0], "DH Group mismatch in KE payload"
    assert ke_payload.ke == KE, "KE data mismatch"

    # Step 10: Verify Traffic Selectors
    tsi_payload = decrypted_payload["IKEv2_TSi"]
    tsr_payload = decrypted_payload["IKEv2_TSr"]

    # Check TSi
    assert tsi_payload.number_of_TSs == 1, "Incorrect number of TSi"
    ts_client = tsi_payload["IPv4TrafficSelector"]
    assert (
        ts_client.start_port == traffic_selector_client[2]
    ), "TSi start port mismatch"
    assert (
        ts_client.end_port == traffic_selector_client[3]
    ), "TSi end port mismatch"
    assert (
        ts_client.starting_address_v4 == traffic_selector_client[0]
    ), "TSi IP mismatch"

    # Check TSr
    assert tsr_payload.number_of_TSs == 1, "Incorrect number of TSr"
    ts_server = tsr_payload["IPv4TrafficSelector"]
    assert (
        ts_server.start_port == traffic_selector_server[2]
    ), "TSr start port mismatch"
    assert (
        ts_server.end_port == traffic_selector_server[3]
    ), "TSr end port mismatch"
    assert (
        ts_server.starting_address_v4 == traffic_selector_server[0]
    ), "TSr IP mismatch"

    # Step 11: Verify Security Association Payload
    sa_payload = decrypted_payload["IKEv2_SA"]
    # Additional checks on SA payload can be added here
    expected_tfms = {
        "encryption": [20],  # Example cipher ENCR_AES_CTR
        "prf": [5],  # PRF_HMAC_SHA2_256
        "integrity": [],  # None AEAD
        "groupdesc": [19],  # SECP256R1
    }
    sa_result = check_sa_transforms_match(sa_payload, expected_tfms)
    assert sa_result is True
    # Step 12: Verify Notify Payloads if any
    if notify_options:
        notify_payloads = [
            p
            for p in decrypted_payload.iterpayloads()
            if p.name == "IKEv2 Notify"
        ]
        assert (
            len(notify_payloads) == len(notify_options) + 1
        ), "Mismatch in number of Notify payloads"  # Because of options and extras notify
        for notify_payload, expected_notify in zip(
            notify_payloads, notify_options
        ):
            assert notify_payload.type == get_notify_key(
                expected_notify.type
            ), "Notify type mismatch"
            assert (
                notify_payload.notify == expected_notify.notify
            ), "Notify data mismatch"


def test_forge_ikev2_create_child_sa_initiator():
    # Similar to the basic test but with 'Initiator' mode
    # Step 1: Generate cryptographic parameters and initial IKEv2 packets
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)
    (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        _,
        _,
        _,
        _,
    ) = create_ikev2_sa(spi_i=spi_i, spi_r=spi_r)

    # Step 2: Define test inputs
    mode = "Initiator"
    mid = 2
    esn = 0  # No Extended Sequence Numbers
    notify_options = None  # No notify options
    traffic_selector_client = ["********", "********00", 0, 65535]
    traffic_selector_server = ["***********", "*************", 0, 65535]
    ciphers = [12]  # ENCR_AES_CBC
    key_lengths = [128]
    prfs = [2]  # PRF_HMAC_MD5
    integrities = [2]  # AUTH_HMAC_MD5_96
    groupdescs = []  # No DH group
    nonce = secrets.token_bytes(32)
    KE = None  # No Key Exchange data
    notify_extras = None

    # Step 3: Call the function
    create_child_sa_packet = forge_ikev2_create_child_sa(
        mode=mode,
        spi_i=spi_i,
        spi_r=spi_r,
        mid=mid,
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        traffic_selector_client=traffic_selector_client,
        traffic_selector_server=traffic_selector_server,
        ciphers=ciphers,
        key_lengths=key_lengths,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        nonce=nonce,
        KE=KE,
        esn=esn,
        notify_options=notify_options,
        notify_extras=notify_extras,
    )

    # Step 4: Assertions
    assert isinstance(
        create_child_sa_packet, IKEv2
    ), "Returned packet is not an instance of IKEv2"
    assert (
        create_child_sa_packet.flags == mode
    ), "Incorrect mode in packet flags"

    # Step 5: Decrypt the packet
    decrypted_payload = uncipher_ike_pkt(
        ikev2_crypto=ikev2_crypto_r,
        ikev2_keys=ikev2_keys_r,
        ike_packet=create_child_sa_packet,
    )

    # Step 6: Verify that KE payload is absent
    assert not decrypted_payload.haslayer(
        "IKEv2_KE"
    ), "KE payload should be absent"

    # Step 7: Verify the Nonce
    nonce_payload = decrypted_payload["IKEv2_Nonce"]
    assert nonce_payload.nonce == nonce, "Nonce mismatch"

    # Additional checks can be added as necessary


def test_forge_ikev2_create_child_sa_with_multiple_ciphers():
    # Test with multiple ciphers
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)
    (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        _,
        _,
        _,
        _,
    ) = create_ikev2_sa(spi_i=spi_i, spi_r=spi_r)

    mode = "Response"
    mid = 2
    esn = 0
    notify_options = None
    traffic_selector_client = ["***********", "***********00", 0, 65535]
    traffic_selector_server = ["********", "**********", 0, 65535]
    ciphers = [12, 13, 14]  # ENCR_AES_CBC, ENCR_AES_CTR, ENCR_AES_CCM_8
    key_lengths = [128, 256]
    prfs = [5, 7]  # PRF_HMAC_SHA2_256, PRF_AES128_CMAC
    integrities = [12]  # AUTH_HMAC_SHA2_256_128
    groupdescs = [20]  # SECP384R1
    nonce = secrets.token_bytes(32)
    KE = ikev2_crypto_i.dh.public_key
    notify_extras = None

    create_child_sa_packet = forge_ikev2_create_child_sa(
        mode=mode,
        spi_i=spi_i,
        spi_r=spi_r,
        mid=mid,
        ikev2_crypto=ikev2_crypto_r,
        ikev2_keys=ikev2_keys_r,
        traffic_selector_client=traffic_selector_client,
        traffic_selector_server=traffic_selector_server,
        ciphers=ciphers,
        key_lengths=key_lengths,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        nonce=nonce,
        KE=KE,
        esn=esn,
        notify_options=notify_options,
        notify_extras=notify_extras,
    )

    # Assertions
    assert isinstance(create_child_sa_packet, IKEv2), "Invalid packet type"

    decrypted_payload = uncipher_ike_pkt(
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        ike_packet=create_child_sa_packet,
    )

    sa_payload = decrypted_payload["IKEv2_SA"]
    transforms = []
    for prop in sa_payload.prop.iterpayloads():
        for tfm in prop.trans.iterpayloads():
            transforms.append(tfm)
    encryption_tfm_ids = [
        tfm.transform_id for tfm in transforms if tfm.transform_type == 1
    ]
    from tests.support.logger import logger

    logger.debug(f"{set(encryption_tfm_ids)} == {set(ciphers)}")
    assert set(encryption_tfm_ids) == set(
        ciphers
    ), "Encryption transforms mismatch"

    # Additional checks on key lengths and other transforms can be added


def test_forge_ikev2_create_child_sa_invalid_parameters():
    # Test with invalid parameters
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)
    ikev2_crypto = None  # Invalid ikev2_crypto
    ikev2_keys = None  # Invalid ikev2_keys

    with pytest.raises(Exception):
        forge_ikev2_create_child_sa(
            mode="Response",
            spi_i=spi_i,
            spi_r=spi_r,
            mid=2,
            ikev2_crypto=ikev2_crypto,
            ikev2_keys=ikev2_keys,
            traffic_selector_client=[],
            traffic_selector_server=[],
            ciphers=[],
            key_lengths=[],
            prfs=[],
            integrities=[],
            groupdescs=[],
            nonce=b"",
            KE=None,
            esn=0,
            notify_options=None,
            notify_extras=None,
        )


def test_forge_ikev2_create_child_sa_with_no_notify_options():
    # Test when notify_options is None
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)
    (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        _,
        _,
        _,
        _,
    ) = create_ikev2_sa(spi_i=spi_i, spi_r=spi_r)

    mode = "Initiator"
    mid = 2
    esn = 0
    notify_options = None  # No notify options
    traffic_selector_client = ["***********", "***********00", 0, 65535]
    traffic_selector_server = ["********", "**********", 0, 65535]
    ciphers = [12]  # ENCR_AES_CBC
    key_lengths = [128]
    prfs = [5]  # PRF_HMAC_SHA2_256
    integrities = [12]  # AUTH_HMAC_SHA2_256_128
    groupdescs = [19]  # SECP256R1
    nonce = secrets.token_bytes(32)
    KE = ikev2_crypto_i.dh.public_key
    notify_extras = None

    create_child_sa_packet = forge_ikev2_create_child_sa(
        mode=mode,
        spi_i=spi_i,
        spi_r=spi_r,
        mid=mid,
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        traffic_selector_client=traffic_selector_client,
        traffic_selector_server=traffic_selector_server,
        ciphers=ciphers,
        key_lengths=key_lengths,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        nonce=nonce,
        KE=KE,
        esn=esn,
        notify_options=notify_options,
        notify_extras=notify_extras,
    )

    # Assertions
    assert isinstance(create_child_sa_packet, IKEv2), "Invalid packet type"

    decrypted_payload = uncipher_ike_pkt(
        ikev2_crypto=ikev2_crypto_r,
        ikev2_keys=ikev2_keys_r,
        ike_packet=create_child_sa_packet,
    )

    notify_payloads = [
        p for p in decrypted_payload.iterpayloads() if p.name == "IKEv2 Notify"
    ]
    assert len(notify_payloads) == 0, "Unexpected Notify payloads found"

    # Additional verification to be added later as needed
