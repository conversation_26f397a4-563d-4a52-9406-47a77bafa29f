import pytest
import secrets
from itertools import product
from scapy.compat import raw
from scapy.contrib.ikev2 import (
    IKEv2,
    IKEv2_Nonce,
    IKEv2_Notify,
)
from ipsecdr.core.crypto.ikev2_crypto import IKEv2Algorithm
from ipsecdr.core.IKEv2.init import forge_ikev2_init
from ipsecdr.core.IKEv2.payloads import prepare_ikev2_pkt
from ipsecdr.core.IKEv2.utils import (
    encrypt_ike_chunk,
    uncipher_ike_pkt,
    set_ikev2_crypto,
    set_ikev2_keys,
    gen_hash_data,
    rebuild_header,
    check_sa_transforms_match,
    get_tfm_name,
    is_notify,
    get_notify_key,
    get_notify_id,
    get_notify_name,
    check_for_any_notify,
    is_cookie,
    get_cookie,
    get_auth_name,
    groupdesc_key,
)
from ipsecdr.core.IKEv2.constants import DH_TFM_ID
from ipsecdr.utils.models import NotifyPayload
from tests.support.packets import init_i


def test_uncipher_ike_pkt(ikev2_init_setup):
    # Unpack variables
    spi_i = ikev2_init_setup["spi_i"]
    spi_r = ikev2_init_setup["spi_r"]
    ikev2_crypto_i = ikev2_init_setup["ikev2_crypto_i"]
    ikev2_crypto_r = ikev2_init_setup["ikev2_crypto_r"]
    ikev2_keys_i = ikev2_init_setup["ikev2_keys_i"]
    ikev2_keys_r = ikev2_init_setup["ikev2_keys_r"]
    plaintext_chunk = ikev2_init_setup["plaintext_chunk"]

    # Prepare the plaintext chunk
    padded_chunk_i = ikev2_crypto_i.chiffrement.data_padding(
        raw(plaintext_chunk)
    )
    padded_chunk_r = ikev2_crypto_r.chiffrement.data_padding(
        raw(plaintext_chunk)
    )
    assert padded_chunk_i == padded_chunk_r

    # Prepare the encrypted packet from the Responder
    ikev2_frame_r = prepare_ikev2_pkt(
        spi_i=spi_i,
        spi_r=spi_r,
        mid=1,
        mode="Response",
        xchg_type="IKE_AUTH",
        next_payload="None",
        ikev2_crypto=ikev2_crypto_r,
        ikev2_keys=ikev2_keys_r,
        ikev2_frame=plaintext_chunk,
    )

    # Decrypt the packet using the Initiator's keys and crypto
    decrypted_packet_r = uncipher_ike_pkt(
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        ike_packet=ikev2_frame_r,
    )

    # Extract the decrypted payload
    decrypted_payload_r = bytes(decrypted_packet_r.payload)

    # Verify that the decrypted payload matches the original plaintext
    assert decrypted_payload_r == padded_chunk_r

    # Prepare the encrypted packet from the Initiator
    ikev2_frame_i = prepare_ikev2_pkt(
        spi_i=spi_i,
        spi_r=spi_r,
        mid=1,
        mode="Initiator",
        xchg_type="IKE_AUTH",
        next_payload="None",
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        ikev2_frame=plaintext_chunk,
    )

    # Decrypt the packet using the Responder's keys and crypto
    decrypted_packet_i = uncipher_ike_pkt(
        ikev2_crypto=ikev2_crypto_r,
        ikev2_keys=ikev2_keys_r,
        ike_packet=ikev2_frame_i,
    )

    # Extract the decrypted payload
    decrypted_payload_i = bytes(decrypted_packet_i.payload)

    # Verify that the decrypted payload matches the original plaintext
    assert decrypted_payload_i == padded_chunk_i


def test_set_ikev2_crypto(ikev2_init_setup):
    ikev2_crypto_i = ikev2_init_setup["ikev2_crypto_i"]
    # Verify that the algorithms are set correctly for Initiator
    assert ikev2_crypto_i.choix_chiffrement == 20  # ENCR_AES_GCM_16
    assert ikev2_crypto_i.key_chiffrement == 32 * 8  # 256 bits
    assert ikev2_crypto_i.choix_prf == 5  # PRF_HMAC_SHA2_256
    assert ikev2_crypto_i.choix_integrite == 0  # No integrity


def test_set_ikev2_keys(ikev2_init_setup):
    ikev2_crypto_i = ikev2_init_setup["ikev2_crypto_i"]
    ikev2_keys_i = ikev2_init_setup["ikev2_keys_i"]
    # Verify that the keys are set and have correct lengths
    assert len(ikev2_keys_i.sk_d) == ikev2_crypto_i.prf.key_size
    assert len(ikev2_keys_i.sk_ei) == ikev2_crypto_i.chiffrement.key_size
    assert len(ikev2_keys_i.sk_er) == ikev2_crypto_i.chiffrement.key_size
    # For AES-GCM, sk_ai and sk_ar may not be used
    # Uncomment if applicable
    # assert len(ikev2_keys_i.sk_ai) == 0


def test_gen_hash_data(ikev2_init_setup):
    ikev2_crypto_r = ikev2_init_setup["ikev2_crypto_r"]
    ikev2_keys_r = ikev2_init_setup["ikev2_keys_r"]
    plaintext_chunk = ikev2_init_setup["plaintext_chunk"]
    spi_i = ikev2_init_setup["spi_i"]
    spi_r = ikev2_init_setup["spi_r"]

    # Encrypt a chunk using AES-GCM (assuming ENCR_AES_GCM)
    iv, encrypted_chunk, padded_chunk = encrypt_ike_chunk(
        ikev2_crypto=ikev2_crypto_r,
        encryption_key=ikev2_keys_r.sk_er,
        chunk=plaintext_chunk,
    )

    # Prepare an IKEv2 frame (before encryption)
    ikev2_frame = IKEv2(
        init_SPI=spi_i,
        resp_SPI=spi_r,
        next_payload="Encrypted",
        exch_type="IKE_AUTH",
        flags="Response",  # Response flag
        id=1,
        length=len(encrypted_chunk) + len(IKEv2()) + 16,
    )

    # Generate hash data for AES-GCM mode
    if ikev2_crypto_r.chiffrement.AES == "GCM":
        auth_key = ikev2_keys_r.sk_er  # Using response encryption key
    else:
        auth_key = ikev2_keys_r.sk_ar  # For regular integrity

    # Call the function to generate hash data
    h_data = gen_hash_data(
        ikev2_crypto=ikev2_crypto_r,
        auth_key=auth_key,
        ikev2_frame=ikev2_frame,
        padded_chunk=padded_chunk,
        iv=iv,
    )

    # Verify the hash data is the correct length for AES-GCM
    if ikev2_crypto_r.chiffrement.AES == "GCM":
        expected_length = ikev2_crypto_r.chiffrement.ICV_size
        assert len(h_data) == expected_length
    else:
        expected_length = ikev2_crypto_r.integrite.hash_size
        assert len(h_data) == expected_length

    # Ensure the hash data is not empty
    assert h_data


def test_rebuild_header(ikev2_init_setup):
    ikev2_crypto_r = ikev2_init_setup["ikev2_crypto_r"]
    ikev2_keys_r = ikev2_init_setup["ikev2_keys_r"]
    plaintext_chunk = ikev2_init_setup["plaintext_chunk"]
    spi_i = ikev2_init_setup["spi_i"]
    spi_r = ikev2_init_setup["spi_r"]

    # Encrypt a chunk first to simulate a real IKEv2 packet
    iv, encrypted_chunk, padded_chunk = encrypt_ike_chunk(
        ikev2_crypto=ikev2_crypto_r,
        encryption_key=ikev2_keys_r.sk_er,
        chunk=plaintext_chunk,
    )

    # Create a sample IKEv2 packet
    ikev2_pkt_with_hdr = (
        raw(
            IKEv2(
                init_SPI=spi_i,
                resp_SPI=spi_r,
                next_payload="Encrypted",
                exch_type="IKE_AUTH",
                flags="Response",  # Response flag
                id=1,
                length=len(encrypted_chunk) + len(IKEv2()) + 16,
            )
        )
        + encrypted_chunk
    )

    # Simulate decryption by taking the raw chunk
    #  (we're assuming correct decryption)
    decrypted_chunk = raw(
        plaintext_chunk
    )  # As decrypted data should match original

    # Call the rebuild_header function
    rebuilt_packet = rebuild_header(
        decrypted=decrypted_chunk,  # decrypted payload
        ikev2_pkt_with_hdr=ikev2_pkt_with_hdr,  # original encrypted packet
    )

    # Verify that the rebuilt packet contains the correct decrypted payload
    assert raw(rebuilt_packet.payload) == raw(plaintext_chunk)

    # Verify that the header matches the original packet header
    assert rebuilt_packet.init_SPI == spi_i
    assert rebuilt_packet.resp_SPI == spi_r
    assert rebuilt_packet.exch_type == 35  # 35 == IKE_AUTH
    assert rebuilt_packet.flags == "Response"


def test_encryption_decryption_combinations(cert_paths):
    # Access cert paths
    cert_i_path = cert_paths["cert_i_path"]
    key_i_path = cert_paths["key_i_path"]
    cert_r_path = cert_paths["cert_r_path"]
    key_r_path = cert_paths["key_r_path"]
    ca_cert_path = cert_paths["ca_cert_path"]

    # Generate SPIs
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)

    # Define the transforms to test
    AUTH_METHODS = {
        "ECDSA_SECP256R1_SHA256": 9,
        "ECDSA_BP256R1_SHA256": 214,
        "ECSDSA_SECP256R1_SHA256": 225,
        "ECSDSA_BP256R1_SHA256": 228,
    }

    PRF_TFM_IDS = {"PRF_HMAC_SHA2_256": 5}

    INTEG_TFM_IDS = {"AUTH_HMAC_SHA2_256_128": 12, "None": 0}  # Use 0 for None

    DH_TFM_IDS = {
        "SECP256R1": 19,
        "brainpoolP256r1": 28,
    }

    ENCR_TFM_IDS = {
        "ENCR_AES_CTR": 13,
        "ENCR_AES_GCM": 20,
    }

    # Generate combinations
    combinations = list(
        product(
            ENCR_TFM_IDS.items(),
            INTEG_TFM_IDS.items(),
            DH_TFM_IDS.items(),
            AUTH_METHODS.items(),
        )
    )

    # For each combination, perform the test
    for (
        (encr_name, encr_id),
        (integ_name, integ_id),
        (dh_name, dh_id),
        (auth_name, auth_id),
    ) in combinations:
        # Skip invalid combinations
        # If ENCR_AES_GCM, integ_id should be 0
        if encr_name == "ENCR_AES_GCM" and integ_id != 0:
            continue
        if encr_name != "ENCR_AES_GCM" and integ_id == 0:
            continue

        # Set up cryptographic parameters
        ciphers = [encr_id]
        key_lengths = [256]  # Use 256 bits key size
        prfs = [PRF_TFM_IDS["PRF_HMAC_SHA2_256"]]
        integrities = [integ_id]
        groupdescs = [dh_id]

        # Initialize IKEv2Algorithm instances for Initiator and Responder
        ikev2_crypto_i = IKEv2Algorithm(choix_dh=dh_id)
        ikev2_crypto_r = IKEv2Algorithm(choix_dh=dh_id)

        # Generate nonces
        nonce_i = secrets.token_bytes(32)
        nonce_r = secrets.token_bytes(32)

        # Generate Key Exchange data
        KE_i = ikev2_crypto_i.dh.public_key
        KE_r = ikev2_crypto_r.dh.public_key

        # Forge IKE_SA_INIT packets for Initiator and Responder
        init_i_pkt = forge_ikev2_init(
            mode="Initiator",
            spi_i=spi_i,
            spi_r=spi_r,
            ciphers=ciphers,
            key_lengths=key_lengths,
            prfs=prfs,
            integrities=integrities,
            groupdescs=groupdescs,
            nonce=nonce_i,
            cookie=None,
            KE=KE_i,
            notify_extras=[NotifyPayload(type="INITIAL_CONTACT", notify=b"")],
            mid=1,
        )

        init_r_pkt = forge_ikev2_init(
            mode="Responder",
            spi_i=spi_i,
            spi_r=spi_r,
            ciphers=ciphers,
            key_lengths=key_lengths,
            prfs=prfs,
            integrities=integrities,
            groupdescs=groupdescs,
            nonce=nonce_r,
            cookie=None,
            KE=KE_r,
            notify_extras=[NotifyPayload(type="INITIAL_CONTACT", notify=b"")],
            mid=1,
        )

        # Set up cryptographic algorithms based on received packets
        ikev2_crypto_i = set_ikev2_crypto(
            ikev2_pkt=init_r_pkt,
            ikev2_crypto=ikev2_crypto_i,
        )

        ikev2_crypto_r = set_ikev2_crypto(
            ikev2_pkt=init_i_pkt,
            ikev2_crypto=ikev2_crypto_r,
        )

        # Derive keys
        ikev2_keys_i = set_ikev2_keys(
            ke=init_r_pkt["IKEv2"]["IKEv2_KE"].ke,
            nonce_i=nonce_i,
            nonce_r=nonce_r,
            spi_i=spi_i,
            spi_r=spi_r,
            ikev2_crypto=ikev2_crypto_i,
            pub_cert=str(cert_i_path),
            key_cert=str(key_i_path),
            trust_chain=[str(ca_cert_path)],
        )

        ikev2_keys_r = set_ikev2_keys(
            ke=init_i_pkt["IKEv2"]["IKEv2_KE"].ke,
            nonce_i=nonce_i,
            nonce_r=nonce_r,
            spi_i=spi_i,
            spi_r=spi_r,
            ikev2_crypto=ikev2_crypto_r,
            pub_cert=str(cert_r_path),
            key_cert=str(key_r_path),
            trust_chain=[str(ca_cert_path)],
        )

        # Verify that keys match
        assert ikev2_keys_i.sk_ei == ikev2_keys_r.sk_ei
        assert ikev2_keys_i.sk_er == ikev2_keys_r.sk_er
        assert ikev2_keys_i.sk_ai == ikev2_keys_r.sk_ai
        assert ikev2_keys_i.sk_ar == ikev2_keys_r.sk_ar

        # Prepare the plaintext chunk
        plaintext_chunk = IKEv2_Nonce(nonce=b"Test plaintext for encryption")
        padded_chunk_i = ikev2_crypto_i.chiffrement.data_padding(
            raw(plaintext_chunk)
        )
        padded_chunk_r = ikev2_crypto_r.chiffrement.data_padding(
            raw(plaintext_chunk)
        )

        assert padded_chunk_i == padded_chunk_r

        # Prepare the encrypted packet from the Responder
        ikev2_frame_r = prepare_ikev2_pkt(
            spi_i=spi_i,
            spi_r=spi_r,
            mid=1,
            mode="Response",
            xchg_type="IKE_AUTH",
            next_payload="None",
            ikev2_crypto=ikev2_crypto_r,
            ikev2_keys=ikev2_keys_r,
            ikev2_frame=plaintext_chunk,
        )

        # Decrypt the packet using the Initiator's keys and crypto
        decrypted_packet_r = uncipher_ike_pkt(
            ikev2_crypto=ikev2_crypto_i,
            ikev2_keys=ikev2_keys_i,
            ike_packet=ikev2_frame_r,
        )

        # Extract the decrypted payload
        decrypted_payload_r = bytes(decrypted_packet_r.payload)

        # Verify that the decrypted payload matches the original plaintext
        assert decrypted_payload_r == padded_chunk_r

        # Prepare the encrypted packet from the Initiator
        ikev2_frame_i = prepare_ikev2_pkt(
            spi_i=spi_i,
            spi_r=spi_r,
            mid=1,
            mode="Initiator",
            xchg_type="IKE_AUTH",
            next_payload="None",
            ikev2_crypto=ikev2_crypto_i,
            ikev2_keys=ikev2_keys_i,
            ikev2_frame=plaintext_chunk,
        )

        # Decrypt the packet using the Responder's keys and crypto
        decrypted_packet_i = uncipher_ike_pkt(
            ikev2_crypto=ikev2_crypto_r,
            ikev2_keys=ikev2_keys_r,
            ike_packet=ikev2_frame_i,
        )

        # Extract the decrypted payload
        decrypted_payload_i = bytes(decrypted_packet_i.payload)

        # Verify that the decrypted payload matches the original plaintext
        assert decrypted_payload_i == padded_chunk_i


@pytest.mark.parametrize(
    "config_transforms, expected_result",
    [
        # Existing test cases
        (  # 0
            {
                "encryption": [12],  # ENCR_AES_CBC (mismatch)
                "prf": [5],  # PRF_HMAC_SHA2_256
                "integrity": [12],  # AUTH_HMAC_SHA2_256_128
                "groupdesc": [19],  # 256randECPgr
            },
            False,
        ),
        (  # 1
            {
                "encryption": [12],  # ENCR_AES_CBC (mismatch)
                "prf": [5],  # PRF_HMAC_SHA2_256
                "integrity": [12],  # AUTH_HMAC_SHA2_256_128
                "groupdesc": [28],  # brainpoolP256r1
            },
            False,
        ),
        (  # 2
            {
                "encryption": [13],  # ENCR_AES_CTR
                "prf": [5],  # PRF_HMAC_SHA2_256
                "integrity": [12],  # AUTH_HMAC_SHA2_256_128
                "groupdesc": [19],  # 256randECPgr
            },
            True,
        ),
        (  # 3
            {
                "encryption": [20],  # ENCR_AES_GCM16
                "prf": [5],  # PRF_HMAC_SHA2_256
                "integrity": [],  # No integrity
                "groupdesc": [28],  # 256randECPgr
            },
            True,
        ),
        (  # 4
            {
                "encryption": [20],  # ENCR_AES_GCM16
                "prf": [5],  # PRF_HMAC_SHA2_256
                "integrity": [],  # No integrity
                "groupdesc": [19],  # 256randECPgr
            },
            True,
        ),
        (  # 5
            {
                "encryption": [20],  # ENCR_AES_GCM16
                "prf": [7],  # PRF_HMAC_SHA2_384
                "integrity": [],  # No integrity
                "groupdesc": [19, 28],  # 256randECPgr or brainpoolP256r1
            },
            True,
        ),
        (  # 6
            {
                "encryption": [12, 20],  # ENCR_AES_CBC or ENCR_AES_GCM16
                "prf": [5],  # PRF_HMAC_SHA2_256
                "integrity": [12, []],  # AUTH_HMAC_SHA2_256_128
                "groupdesc": [19],  # 256randECPgr
            },
            True,  # Subset match
        ),
        (  # 7
            {
                "encryption": [13],  # ENCR_AES_CTR
                "prf": [6],  # PRF_HMAC_SHA2_384 (mismatch)
                "integrity": [12],  # AUTH_HMAC_SHA2_256_128
                "groupdesc": [19],  # 256randECPgr
            },
            False,  # PRF mismatch
        ),
        (  # 8
            {
                "encryption": [12],  # ENCR_AES_CBC
                "prf": [5],  # PRF_HMAC_SHA2_256
                "integrity": [
                    12,
                    [],
                ],  # AUTH_HMAC_SHA2_256_128 or No Integrity
                "groupdesc": [28],  # brainpoolP256r1
            },
            False,  # Subset match
        ),
        (
            {
                "encryption": [20],  # ENCR_AES_GCM16
                "prf": [5],  # PRF_HMAC_SHA2_256
                "integrity": [],  # No integrity
                "groupdesc": [14],  # 2048MODPgr (mismatch)
            },
            False,  # GroupDesc mismatch
        ),
        (
            {
                "encryption": [20],  # ENCR_AES_GCM16
                "prf": [5],  # PRF_HMAC_SHA2_256
                "integrity": [],  # No integrity
                "groupdesc": [19, 20],  # 256randECPgr or 2048MODPgr
            },
            True,  # Subset match
        ),
        (
            {
                "encryption": [12],  # ENCR_AES_CBC
                "prf": [5],  # PRF_HMAC_SHA2_256
                "integrity": [12],  # AUTH_HMAC_SHA2_256_128
                "groupdesc": [28],  # brainpoolP256r1
            },
            False,  # Exact match
        ),
        (
            {
                "encryption": [20],  # ENCR_AES_GCM16
                "prf": [5],  # PRF_HMAC_SHA2_256
                "integrity": [],  # No integrity
                "groupdesc": [19],  # 256randECPgr
            },
            True,  # Subset match
        ),
        (
            {
                "encryption": [20],  # ENCR_AES_GCM16
                "prf": [5],  # PRF_HMAC_SHA2_256
                "integrity": [],  # No integrity
                "groupdesc": [29],  # brainpoolP384r1 (mismatch)
            },
            False,  # Group mismatch
        ),
        (
            {
                "encryption": [12],  # ENCR_AES_CBC
                "prf": [5],  # PRF_HMAC_SHA2_256
                "integrity": [],  # No integrity
                "groupdesc": [19],  # 256randECPgr
            },
            False,  # Subset match
        ),
        (  # 15
            {
                "encryption": [20],  # ENCR_AES_GCM16
                "prf": [7],  # PRF_HMAC_SHA2_512
                "integrity": [],  # No integrity
                "groupdesc": [28],  # brainpoolP256r1
            },
            True,  # PRF mismatch
        ),
    ],
)
def test_check_sa_transforms_match(config_transforms, expected_result):
    # Extract Security Association (SA) proposals from the IKEv2 packet
    sa_proposals = init_i.payload["IKEv2_SA"]
    result = check_sa_transforms_match(sa_proposals, config_transforms)
    assert result == expected_result


def test_get_tfm_name_valid():
    # Test valid transform types and IDs
    test_cases = [
        (1, 12, ("ENCR", "ENCR_AES_CBC")),  # Encryption
        (2, 5, ("PRF", "PRF_HMAC_SHA2_256")),  # PRF
        (3, 12, ("INTEG", "AUTH_HMAC_SHA2_256_128")),  # Integrity
        (4, 19, ("DH", "256randECPgr")),  # Diffie-Hellman Group
        (5, 0, ("ESN", "NO_ESN")),  # Extended Sequence Numbers
    ]
    for tfm_type, tfm_id, expected in test_cases:
        result = get_tfm_name(tfm_type, tfm_id)
        assert (
            result == expected
        ), f"Failed for tfm_type={tfm_type}, tfm_id={tfm_id}"


def test_get_tfm_name_invalid():
    # Test invalid transform types and IDs
    invalid_cases = [
        (1, 9999),  # Invalid encryption ID
        (6, 5),  # Invalid transform type
    ]
    for tfm_type, tfm_id in invalid_cases:
        with pytest.raises(KeyError):
            get_tfm_name(tfm_type, tfm_id)


def test_get_notify_key():
    # Test with valid notify names
    assert get_notify_key("INITIAL_CONTACT") == 16384
    assert get_notify_key("COOKIE") == 16390
    # Test with invalid notify name
    assert get_notify_key("INVALID_NOTIFY") is None


def test_get_notify_name():
    # Test with valid notify IDs
    assert get_notify_name(16384) == "INITIAL_CONTACT"
    assert get_notify_name(16390) == "COOKIE"
    # Test with invalid notify ID
    with pytest.raises(KeyError):
        get_notify_name(99999)


def test_get_notify_id():
    # Test with valid notify names
    assert get_notify_id("INITIAL_CONTACT") == 16384
    assert get_notify_id("COOKIE") == 16390
    # Test with invalid notify name
    with pytest.raises(KeyError):
        get_notify_id("INVALID_NOTIFY")


def test_is_notify():
    # Create an IKEv2 packet with a notify payload
    notify_payload = IKEv2_Notify(
        next_payload=0,
        type=16384,
        proto=0,
        notify=b"",
    )
    ikev2_packet = IKEv2() / notify_payload

    # Test for existing notify
    assert is_notify(ikev2_packet, "INITIAL_CONTACT") is True
    # Test for non-existing notify
    assert is_notify(ikev2_packet, "COOKIE") is False
    # Test with invalid notify name
    assert is_notify(ikev2_packet, "INVALID_NOTIFY") is False


def test_check_for_any_notify():
    # Create an IKEv2 packet with multiple notify payloads
    notify_payload1 = IKEv2_Notify(
        next_payload=0,
        type=16384,  # INITIAL_CONTACT
        proto=0,
        notify=b"",
    )
    notify_payload2 = IKEv2_Notify(
        next_payload=0,
        type=16390,  # COOKIE
        proto=0,
        notify=b"",
    )
    ikev2_packet = IKEv2() / notify_payload1 / notify_payload2

    # Check for any notify types
    notify_types = check_for_any_notify(ikev2_packet)
    assert notify_types == [16384, 16390]


def test_is_cookie():
    # Create an IKEv2 packet with a COOKIE notify payload
    cookie_value = b"test_cookie_value"
    notify_payload = IKEv2_Notify(
        next_payload=0,
        type=get_notify_id("COOKIE"),
        proto=0,
        notify=cookie_value,
    )
    ikev2_packet = IKEv2() / notify_payload

    # Test if cookie is present
    assert is_cookie(ikev2_packet) is True

    # Test with packet without cookie
    ikev2_packet_no_cookie = IKEv2()
    assert is_cookie(ikev2_packet_no_cookie) is False


def test_get_cookie():
    # Create an IKEv2 packet with a COOKIE notify payload
    cookie_value = b"test_cookie_value"
    notify_payload = IKEv2_Notify(
        next_payload=0,
        type=get_notify_id("COOKIE"),
        proto=0,
        notify=cookie_value,
    )
    ikev2_packet = IKEv2() / notify_payload

    # Retrieve the cookie
    retrieved_cookie = get_cookie(ikev2_packet)
    assert retrieved_cookie == cookie_value

    # Test with packet without cookie
    ikev2_packet_no_cookie = IKEv2()
    retrieved_cookie = get_cookie(ikev2_packet_no_cookie)
    assert retrieved_cookie == b""


def test_get_auth_name():
    # Test with valid authentication method IDs
    assert get_auth_name(9) == "ECDSA_SECP256R1_SHA256"
    assert get_auth_name(14) == "DIGITAL_SIGNATURE"

    # Test with invalid authentication method ID
    with pytest.raises(KeyError):
        get_auth_name(999)


def test_groupdesc_key_valid():
    # Test with valid group descriptors
    test_cases = [
        (DH_TFM_ID["768MODPgr"], 96),
        (DH_TFM_ID["1024MODPgr"], 128),
        (DH_TFM_ID["256randECPgr"], 64),
    ]
    for groupdesc, expected_length in test_cases:
        key = groupdesc_key(groupdesc)
        assert isinstance(key, bytes)
        assert (
            len(key) == expected_length
        ), f"Incorrect key length for groupdesc {groupdesc}"


def test_groupdesc_key_invalid():
    # Test with invalid group descriptor
    invalid_groupdesc = 999
    result = groupdesc_key(invalid_groupdesc)
    assert result is None


# TODO
# set_ikev2_keys_child
# error  case, invalid packets etc
