import secrets
from scapy.contrib.ikev2 import IKEv2
from ipsecdr.core.IKEv2.init import forge_ikev2_init
from ipsecdr.utils.models import NotifyPayload
from tests.support.helpers import (
    create_random_ikev2_sa,
    create_random_ikev2_init,
)


def test_forge_ikev2_init_basic():
    # Define test inputs
    mode = "Initiator"
    spi_i = 0x12345678
    spi_r = 0x87654321
    ciphers = [12]  # "ENCR_AES_CBC"
    key_lengths = [256]
    prfs = [5]  # "PRF_HMAC_SHA2_256"
    integrities = [12]  # "AUTH_HMAC_SHA2_256_128"
    groupdescs = [19]  # Group 19 is a common DH group (SECP256R1)
    nonce = b"\x01\x02\x03\x04\x05\x06\x07\x08"
    cookie = None
    KE = b"\x09\x0A\x0B\x0C\x0D\x0E\x0F\x10"
    notify_extras = [NotifyPayload(type="INITIAL_CONTACT", notify=b"")]
    mid = 1

    # Call the function
    ikev2_packet = forge_ikev2_init(
        mode=mode,
        spi_i=spi_i,
        spi_r=spi_r,
        ciphers=ciphers,
        key_lengths=key_lengths,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        nonce=nonce,
        cookie=cookie,
        KE=KE,
        notify_extras=notify_extras,
        mid=mid,
    )

    # Basic assertions
    assert isinstance(
        ikev2_packet, IKEv2
    )  # Check if the return type is correct
    assert ikev2_packet.init_SPI == spi_i  # Verify the initiator SPI
    assert ikev2_packet.resp_SPI == spi_r  # Verify the responder SPI

    assert ikev2_packet["IKEv2_Nonce"].nonce == nonce  # Verify the nonce
    # Verify the transforms
    for tfm in ikev2_packet["IKEv2_SA"]["IKEv2_Transform"].iterpayloads():
        if tfm.transform_type == 1:
            assert tfm.transform_id == 12
        if tfm.transform_type == 2:
            assert tfm.transform_id == 5
        if tfm.transform_type == 3:
            assert tfm.transform_id == 12
        if tfm.transform_type == 4:
            assert tfm.transform_id == 19


def test_forge_ikev2_init_basic2():
    # Use random cryptographic values generated by create_random_ikev2_sa
    ikev2_crypto, ikev2_keys = create_random_ikev2_sa()

    # Extract test values from ikev2_crypto object
    mode = "Initiator"
    spi_i = 0x12345678
    spi_r = 0x87654321
    ciphers = [ikev2_crypto.choix_chiffrement]  # Random encryption algorithm
    key_lengths = [
        ikev2_crypto.key_chiffrement
    ]  # Key length from ikev2_crypto
    prfs = [ikev2_crypto.choix_prf]  # Random PRF
    integrities = [ikev2_crypto.choix_integrite]  # Random integrity algorithm
    groupdescs = [ikev2_crypto.choix_dh]  # DH group
    nonce = b"\x01\x02\x03\x04\x05\x06\x07\x08"  # Random nonce for testing
    cookie = None
    KE = ikev2_crypto.dh.public_key  # DH public key from ikev2_crypto
    notify_extras = [NotifyPayload(type="INITIAL_CONTACT", notify=b"")]
    mid = 1

    # Call the forge_ikev2_init function
    ikev2_packet = forge_ikev2_init(
        mode=mode,
        spi_i=spi_i,
        spi_r=spi_r,
        ciphers=ciphers,
        key_lengths=key_lengths,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        nonce=nonce,
        cookie=cookie,
        KE=KE,
        notify_extras=notify_extras,
        mid=mid,
    )

    # Basic assertions to check the validity of the returned packet
    assert isinstance(
        ikev2_packet, IKEv2
    )  # Check if the return type is correct
    assert ikev2_packet.init_SPI == spi_i  # Verify the initiator SPI
    assert ikev2_packet.resp_SPI == spi_r  # Verify the responder SPI

    assert ikev2_packet["IKEv2_Nonce"].nonce == nonce  # Verify the nonce
    # Verify the transforms based on the generated values
    for tfm in ikev2_packet["IKEv2_SA"]["IKEv2_Transform"].iterpayloads():
        if tfm.transform_type == 1:  # Encryption
            assert tfm.transform_id == ciphers[0]
        if tfm.transform_type == 2:  # PRF
            assert tfm.transform_id == prfs[0]
        if tfm.transform_type == 3:  # Integrity
            assert tfm.transform_id == integrities[0]
        if tfm.transform_type == 4:  # DH Group
            assert tfm.transform_id == groupdescs[0]


def test_forge_ikev2_init_full():
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)
    (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        init_i_pkt,
        init_r_pkt,
    ) = create_random_ikev2_init(
        spi_i=spi_i,
        spi_r=spi_r,
    )

    # Basic Assertions for Initiator Packet
    assert isinstance(
        init_i_pkt, IKEv2
    ), "Initiator packet is not an IKEv2 instance"
    assert init_i_pkt.init_SPI == spi_i, "Incorrect initiator init SPI"
    assert (
        init_i_pkt["IKEv2_Nonce"].nonce is not None
    ), "Initiator nonce missing"
    assert (
        init_i_pkt["IKEv2_KE"].ke == ikev2_crypto_i.dh.public_key
    ), "Incorrect KE"

    # Basic Assertions for Responder Packet
    assert isinstance(
        init_r_pkt, IKEv2
    ), "Responder packet is not an IKEv2 instance"
    assert init_r_pkt.init_SPI == spi_i, "Incorrect responder init SPI"
    assert (
        init_r_pkt["IKEv2_Nonce"].nonce is not None
    ), "Responder nonce missing"
    assert (
        init_r_pkt["IKEv2_KE"].ke == ikev2_crypto_r.dh.public_key
    ), "Incorrect KE"

    # Validate that both initiator and responder have same SPI and DH
    assert init_i_pkt.init_SPI == init_r_pkt.init_SPI, "Mismatch in SPIs"

    # Verify notify message
    assert init_i_pkt["IKEv2_Notify"].type == 16384, "Notify type mismatch"
