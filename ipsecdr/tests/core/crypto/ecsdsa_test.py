import pytest
from ipsecdr.core.crypto.ecsdsa import (
    Point,
    Signature,
    brainpoolP256r1,
    secp256r1,
    inverse_mod,
    is_on_curve,
    point_neg,
    point_add,
    scalar_mult,
    make_keypair,
    sign_message,
    verify_signature,
    NotOnCurve,
)


def test_inverse_mod():
    p = 101
    k = 42
    inv = inverse_mod(k, p)
    assert (k * inv) % p == 1, "Inverse mod calculation failed"

    # Test with k = 0
    with pytest.raises(ZeroDivisionError):
        inverse_mod(0, p)

    # Test with negative k
    k_neg = -42
    inv_neg = inverse_mod(k_neg, p)
    inv_pos = inverse_mod(-k_neg, p)
    assert inv_neg == p - inv_pos, "Inverse mod of negative k failed"
    assert (k_neg * inv_neg) % p == 1, "Inverse mod with negative k incorrect"


def test_is_on_curve():
    # Test that the base point is on the curve
    curve = secp256r1
    assert is_on_curve(curve, curve.g), "Base point is not on the curve"

    # Test that the point at infinity is on the curve
    assert is_on_curve(curve, None), "Point at infinity should be on the curve"

    # Test that an invalid point is not on the curve
    invalid_point = Point((0, 0))
    assert not is_on_curve(
        curve, invalid_point
    ), "Invalid point incorrectly identified as on the curve"


def test_point_neg():
    curve = secp256r1
    point = curve.g
    neg_point = point_neg(curve, point)
    # The y-coordinate should be negated modulo p
    expected_neg_point = Point((point[0], (-point[1]) % curve.p))
    assert neg_point == expected_neg_point, "Point negation failed"
    assert is_on_curve(curve, neg_point), "Negated point is not on the curve"

    # Test that negating the point at infinity returns None
    assert (
        point_neg(curve, None) is None
    ), "Negation of point at infinity should be None"


def test_point_add():
    curve = secp256r1
    point = curve.g

    # Test addition with None (point at infinity)
    sum_point = point_add(curve, point, None)
    assert sum_point == point, "Adding point with None failed"

    sum_point = point_add(curve, None, point)
    assert sum_point == point, "Adding None with point failed"

    # Test adding a point and its negation (should return None)
    neg_point = point_neg(curve, point)
    sum_point = point_add(curve, point, neg_point)
    assert (
        sum_point is None
    ), "Adding a point and its negation did not return None"

    # Test point doubling
    double_point = point_add(curve, point, point)
    assert is_on_curve(
        curve, double_point
    ), "Resulting point is not on the curve after doubling"
    # Additional verification
    # Multiply point by 2 using scalar_mult and compare
    double_point_scalar = scalar_mult(curve, 2, point)
    assert (
        double_point == double_point_scalar
    ), "Point doubling does not match scalar multiplication by 2"

    # Test adding two different points
    other_point = scalar_mult(curve, 3, point)
    sum_point = point_add(curve, point, other_point)
    assert is_on_curve(
        curve, sum_point
    ), "Resulting point is not on the curve after addition"
    # Additional verification
    # Expected result using scalar multiplication
    expected_point = scalar_mult(curve, 1 + 3, point)
    assert sum_point == expected_point, "Point addition result incorrect"


def test_scalar_mult():
    curve = secp256r1
    point = curve.g

    # Test scalar multiplication with k = 0 (should return None)
    result = scalar_mult(curve, 0, point)
    assert result is None, "Scalar multiplication with k=0 should return None"

    # Test scalar multiplication with negative k
    k = -3
    neg_k_point = scalar_mult(curve, k, point)
    pos_k_point = scalar_mult(curve, -k, point_neg(curve, point))
    assert (
        neg_k_point == pos_k_point
    ), "Scalar multiplication with negative k failed"

    # Test scalar multiplication with k = 1 (should return the same point)
    result = scalar_mult(curve, 1, point)
    assert (
        result == point
    ), "Scalar multiplication with k=1 should return the same point"

    # Test scalar multiplication for a large k
    k = 123456789
    result = scalar_mult(curve, k, point)
    assert is_on_curve(
        curve, result
    ), "Resulting point is not on the curve after scalar multiplication"
    # Additional verification
    # Verify that multiplying by n returns None (n is the order of the curve)
    result_n = scalar_mult(curve, curve.n, point)
    assert (
        result_n is None
    ), "Scalar multiplication by curve order n should return None"


def test_make_keypair():
    curve = secp256r1
    private_key, public_key = make_keypair(curve)
    assert 1 <= private_key < curve.n, "Private key out of valid range"
    assert is_on_curve(curve, public_key), "Public key is not on the curve"
    # Additional verification
    # Verify that public_key == private_key * G
    expected_public_key = scalar_mult(curve, private_key, curve.g)
    assert (
        public_key == expected_public_key
    ), "Public key does not match private_key * G"


def test_sign_message():
    curve = secp256r1
    private_key, public_key = make_keypair(curve)
    message = b"Test message for signing"
    signature = sign_message(curve, private_key, message)
    r_bytes, s_bytes = signature
    assert len(r_bytes) == 32, "Signature component r is not 32 bytes"
    assert len(s_bytes) == 32, "Signature component s is not 32 bytes"
    # Additional verification
    # Verify that signature is valid
    assert verify_signature(
        curve, public_key, message, signature
    ), "Signature verification failed"


def test_verify_signature():
    curve = secp256r1
    private_key, public_key = make_keypair(curve)
    message = b"Test message for verification"
    signature = sign_message(curve, private_key, message)

    # Verify that signature is valid
    assert verify_signature(
        curve, public_key, message, signature
    ), "Valid signature did not verify"

    # Alter the message and verify that signature fails
    altered_message = b"Altered message for verification"
    assert not verify_signature(
        curve, public_key, altered_message, signature
    ), "Invalid signature verified"

    # Alter the signature and verify that signature fails
    r_bytes, s_bytes = signature
    altered_signature = Signature((r_bytes[:-1] + b"\x00", s_bytes))
    assert not verify_signature(
        curve, public_key, message, altered_signature
    ), "Altered signature verified"

    # Use an invalid public key
    invalid_public_key = (public_key[0], (public_key[1] + 1) % curve.p)
    with pytest.raises(NotOnCurve):
        verify_signature(
            curve, invalid_public_key, message, signature
        ), "Invalid public key verified signature"

    # Additional verification
    # Test with signature components out of range
    invalid_s_bytes = (curve.n + 1).to_bytes(32, byteorder="big")
    invalid_signature = Signature((r_bytes, invalid_s_bytes))

    assert not verify_signature(
        curve, public_key, message, invalid_signature
    ), "Signature with invalid s verified"

    invalid_r_bytes = b"\x00" * 32
    invalid_signature = Signature((invalid_r_bytes, s_bytes))
    assert not verify_signature(
        curve, public_key, message, invalid_signature
    ), "Signature with invalid r verified"


def test_full_ecdsa_flow():
    # Test the full ECDSA flow on both curves
    message = b"Full ECDSA flow test message"

    for curve in [secp256r1, brainpoolP256r1]:
        private_key, public_key = make_keypair(curve)
        signature = sign_message(curve, private_key, message)
        assert verify_signature(
            curve, public_key, message, signature
        ), f"Full ECDSA flow failed on curve {curve.name}"
        # Additional verification
        # Ensure that the public key matches the private key
        expected_public_key = scalar_mult(curve, private_key, curve.g)
        assert (
            public_key == expected_public_key
        ), f"Public key does not match private key on curve {curve.name}"

        # Test with a different message
        other_message = b"Another message"
        signature_other = sign_message(curve, private_key, other_message)
        assert verify_signature(
            curve, public_key, other_message, signature_other
        ), "Verification failed for other message"

        # Ensure that signatures are different for different messages
        assert (
            signature != signature_other
        ), "Signatures should be different for different messages"


def test_edge_cases():
    # Test edge cases and invalid inputs
    curve = secp256r1

    # Test sign_message with invalid private key (0)
    with pytest.raises(ValueError):
        sign_message(curve, 0, b"Test message")

    # Test verify_signature with invalid signature components
    private_key, public_key = make_keypair(curve)
    message = b"Test message"
    signature = sign_message(curve, private_key, message)

    # Invalid s (0)
    invalid_signature = Signature((signature[0], b"\x00" * 32))
    assert not verify_signature(
        curve, public_key, message, invalid_signature
    ), "Signature with s=0 verified"

    # Invalid r (all zeros)
    invalid_signature = Signature((b"\x00" * 32, signature[1]))
    assert not verify_signature(
        curve, public_key, message, invalid_signature
    ), "Signature with r=0 verified"

    # Invalid public key (not on curve)
    invalid_public_key = (curve.p + 1, curve.p + 1)
    assert not is_on_curve(
        curve, invalid_public_key
    ), "Invalid public key should not be on the curve"
    with pytest.raises(NotOnCurve):
        verify_signature(curve, invalid_public_key, message, signature)


def test_point_addition_associativity():
    # Point addition is not generally associative, but we can test specific cases
    curve = secp256r1
    P = curve.g
    Q = scalar_mult(curve, 2, P)
    R = scalar_mult(curve, 3, P)

    # Compute (P + Q) + R
    sum1 = point_add(curve, point_add(curve, P, Q), R)
    # Compute P + (Q + R)
    sum2 = point_add(curve, P, point_add(curve, Q, R))

    assert sum1 == sum2, "Point addition is not associative in this case"

    # Additional verification
    # Check that the result equals scalar_mult of the sum of scalars
    total_scalar = 1 + 2 + 3
    expected_point = scalar_mult(curve, total_scalar, P)
    assert (
        sum1 == expected_point
    ), "Point addition result does not match scalar multiplication"


def test_scalar_mult_distributivity():
    # Test that scalar multiplication distributes over point addition: k*(P + Q) == k*P + k*Q
    curve = secp256r1
    P = curve.g
    Q = scalar_mult(curve, 2, P)
    k = 5

    left_side = scalar_mult(curve, k, point_add(curve, P, Q))
    right_side = point_add(
        curve, scalar_mult(curve, k, P), scalar_mult(curve, k, Q)
    )

    assert (
        left_side == right_side
    ), "Scalar multiplication does not distribute over point addition"

    # Additional verification
    # Check that both sides are on the curve
    assert is_on_curve(curve, left_side), "Left side point is not on the curve"
    assert is_on_curve(
        curve, right_side
    ), "Right side point is not on the curve"


def test_large_scalar_multiplication():
    # Test scalar multiplication with a large scalar
    curve = secp256r1
    k = curve.n - 1  # Largest scalar less than the order
    P = curve.g

    result = scalar_mult(curve, k, P)
    assert is_on_curve(curve, result), "Resulting point is not on the curve"
    # Additional verification
    # Since k = n - 1, k * P should be the negation of P
    expected_point = point_neg(curve, P)
    assert (
        result == expected_point
    ), "Scalar multiplication with k = n - 1 does not result in -P"


def test_signature_with_different_curves():
    # Test signing and verification with different curves
    message = b"Test message for different curves"

    for curve in [secp256r1, brainpoolP256r1]:
        private_key, public_key = make_keypair(curve)
        signature = sign_message(curve, private_key, message)
        assert verify_signature(
            curve, public_key, message, signature
        ), f"Signature verification failed on curve {curve.name}"

        # Additional verification
        # Attempt to verify signature with wrong curve
        other_curve = brainpoolP256r1 if curve == secp256r1 else secp256r1
        with pytest.raises(NotOnCurve):
            verify_signature(
                other_curve, public_key, message, signature
            ), "Signature verified with wrong curve"

        # Attempt to verify signature with a different public key
        other_private_key, other_public_key = make_keypair(curve)
        assert not verify_signature(
            curve, other_public_key, message, signature
        ), "Signature verified with wrong public key"
