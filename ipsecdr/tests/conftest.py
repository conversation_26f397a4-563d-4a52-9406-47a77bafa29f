import pytest
import secrets
import random
import io
from pathlib import Path
from unittest.mock import patch
from scapy.contrib.ikev2 import IKEv2_Nonce
from ipsecdr.core.IKEv2.init import forge_ikev2_init
from ipsecdr.core.crypto.ikev2_crypto import IKEv2Algorithm
from ipsecdr.core.IKEv2.utils import (
    set_ikev2_crypto,
    set_ikev2_keys,
)
from ipsecdr.engine.server import IPsecServerManager
from ipsecdr.utils.configuration import IPsecDRConfigParser
from ipsecdr.utils.models import (
    GlobalConfig,
    NetworkConfig,
    PKIConfig,
    IKESAConfig,
    AuthConfig,
    ChildSAConfig,
    TSConfig,
    IDConfig,
    IPsecConfig,
    NotifyPayload,
)
from ipsecdr.engine.scenario import TestConfiguration
from tests.support.helpers import (
    ensure_test_certificates_exist,
    load_test_certificates,
)
from tests.infra.ipsecEmulation import IpsecInfraTester
from tests.support.logger import logger

REPOSITORY_ROOT = Path(__file__).resolve().parents[1]


@pytest.fixture
def ikev2_init_setup():
    ensure_test_certificates_exist()  # Implement this function as needed

    # Paths to certificates
    REPOSITORY_ROOT = Path(__file__).resolve().parents[1]
    certs_dir = REPOSITORY_ROOT / "configs" / "tests"
    cert_i_path = certs_dir / "cert_i.pem"
    key_i_path = certs_dir / "key_i.pem"
    cert_r_path = certs_dir / "cert_r.pem"
    key_r_path = certs_dir / "key_r.pem"
    ca_cert_path = certs_dir / "ca.pem"

    # Generate SPIs
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)

    # Define cryptographic parameters
    ciphers = [20]  # ENCR_AES_GCM_16
    key_lengths = [256]
    prfs = [5]  # PRF_HMAC_SHA2_256
    integrities = [0]  # No integrity algorithm (since using AEAD cipher)
    groupdescs = [19]  # SECP256R1

    # Initialize IKEv2Algorithm instances for Initiator and Responder
    ikev2_crypto_i = IKEv2Algorithm(choix_dh=19)
    ikev2_crypto_r = IKEv2Algorithm(choix_dh=19)

    # Generate nonces
    nonce_i = secrets.token_bytes(32)
    nonce_r = secrets.token_bytes(32)

    # Generate Key Exchange data
    KE_i = ikev2_crypto_i.dh.public_key
    KE_r = ikev2_crypto_r.dh.public_key

    # Forge IKE_SA_INIT packets for Initiator and Responder
    init_i_pkt = forge_ikev2_init(
        mode="Initiator",
        spi_i=spi_i,
        spi_r=spi_r,
        ciphers=ciphers,
        key_lengths=key_lengths,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        nonce=nonce_i,
        cookie=None,
        KE=KE_i,
        notify_extras=[NotifyPayload(type="INITIAL_CONTACT", notify=b"")],
        mid=1,
    )

    init_r_pkt = forge_ikev2_init(
        mode="Responder",
        spi_i=spi_i,
        spi_r=spi_r,
        ciphers=ciphers,
        key_lengths=key_lengths,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        nonce=nonce_r,
        cookie=None,
        KE=KE_r,
        notify_extras=[NotifyPayload(type="INITIAL_CONTACT", notify=b"")],
        mid=1,
    )

    # Set up cryptographic algorithms based on received packets
    ikev2_crypto_i = set_ikev2_crypto(
        ikev2_pkt=init_r_pkt,
        ikev2_crypto=ikev2_crypto_i,
    )

    ikev2_crypto_r = set_ikev2_crypto(
        ikev2_pkt=init_i_pkt,
        ikev2_crypto=ikev2_crypto_r,
    )

    # Derive keys
    ikev2_keys_i = set_ikev2_keys(
        ke=init_r_pkt["IKEv2"]["IKEv2_KE"].ke,
        nonce_i=nonce_i,
        nonce_r=nonce_r,
        spi_i=spi_i,
        spi_r=spi_r,
        ikev2_crypto=ikev2_crypto_i,
        pub_cert=str(cert_i_path),
        key_cert=str(key_i_path),
        trust_chain=[str(ca_cert_path)],
    )

    ikev2_keys_r = set_ikev2_keys(
        ke=init_i_pkt["IKEv2"]["IKEv2_KE"].ke,
        nonce_i=nonce_i,
        nonce_r=nonce_r,
        spi_i=spi_i,
        spi_r=spi_r,
        ikev2_crypto=ikev2_crypto_r,
        pub_cert=str(cert_r_path),
        key_cert=str(key_r_path),
        trust_chain=[str(ca_cert_path)],
    )

    plaintext_chunk = IKEv2_Nonce(nonce=b"Test plaintext for encryption")

    assert ikev2_keys_i.sk_ei == ikev2_keys_r.sk_ei
    assert ikev2_keys_i.sk_er == ikev2_keys_r.sk_er
    assert ikev2_keys_i.sk_ai == ikev2_keys_r.sk_ai
    assert ikev2_keys_i.sk_ar == ikev2_keys_r.sk_ar

    return {
        "spi_i": spi_i,
        "spi_r": spi_r,
        "ikev2_crypto_i": ikev2_crypto_i,
        "ikev2_crypto_r": ikev2_crypto_r,
        "ikev2_keys_i": ikev2_keys_i,
        "ikev2_keys_r": ikev2_keys_r,
        "plaintext_chunk": plaintext_chunk,
        "nonce_i": nonce_i,
        "nonce_r": nonce_r,
        "init_i_pkt": init_i_pkt,
        "init_r_pkt": init_r_pkt,
    }


@pytest.fixture
def cert_paths():
    ensure_test_certificates_exist()  # Implement this function as needed

    # Paths to certificates
    REPOSITORY_ROOT = Path(__file__).resolve().parents[1]
    certs_dir = REPOSITORY_ROOT / "configs" / "tests"
    cert_i_path = certs_dir / "cert_i.pem"
    key_i_path = certs_dir / "key_i.pem"
    cert_r_path = certs_dir / "cert_r.pem"
    key_r_path = certs_dir / "key_r.pem"
    ca_cert_path = certs_dir / "ca.pem"

    return {
        "cert_i_path": cert_i_path,
        "key_i_path": key_i_path,
        "cert_r_path": cert_r_path,
        "key_r_path": key_r_path,
        "ca_cert_path": ca_cert_path,
    }


@pytest.fixture(scope="function")
def ipsec_setup():
    server_port = random.randint(10000, 60000)
    client_port = random.randint(10000, 60000)
    certs = load_test_certificates()

    # Create default configurations
    global_config = GlobalConfig()

    # Client network configuration
    network_config_client = NetworkConfig(
        ipsec_src="127.0.0.1",
        ipsec_dst="127.0.0.1",
        port_src=client_port,  # Client's source port
        port_dst=server_port,  # Server's listening port
    )

    # Server network configuration
    network_config_server = NetworkConfig(
        ipsec_src="127.0.0.1",
        ipsec_dst="127.0.0.1",
        port_src=server_port,  # Server's source port
        port_dst=0,  # Server accepts connections on port_src
    )

    # Create PKI configurations
    pki_config_client = PKIConfig(
        ca_root=certs["ca_cert"],
        pub_key=certs["client_cert"],
        prv_key=certs["client_key"],
        trust_chain=[certs["client_cert"]],
    )
    pki_config_server = PKIConfig(
        ca_root=certs["ca_cert"],
        pub_key=certs["server_cert"],
        prv_key=certs["server_key"],
        trust_chain=[certs["server_cert"]],
    )

    # Create IKE_SA configurations
    ike_sa_config = IKESAConfig(
        sa_encr="ENCR_AES_GCM_16",
        sa_encr_size=256,
        sa_prf="PRF_HMAC_SHA2_256",
        sa_integ="AUTH_HMAC_SHA2_256_128",
        sa_groupdesc=["256randECPgr"],
    )

    # Create AUTH configurations
    auth_config = AuthConfig(auth_method="ECDSA_SECP256R1_SHA256")

    # Create CHILD_SA configurations
    child_sa_config = ChildSAConfig(
        encr="ENCR_AES_GCM_16",
        encr_size=256,
        integ="AUTH_HMAC_SHA2_256_128",
        groupdesc=["256randECPgr"],
        esn="NO_ESN",
    )

    # Create TS configurations
    ts_config = TSConfig(
        tsi_ip_range=("0.0.0.0", "***************"),
        tsr_ip_range=("0.0.0.0", "***************"),
        tsi_port_range=("0", "65535"),
        tsr_port_range=("0", "65535"),
    )

    # Create ID configurations
    id_config = IDConfig(
        idi_type="ID_KEY_ID",
        idi_data="ClientID",
        idr_type="ID_KEY_ID",
        idr_data="ServerID",
    )

    # Create IPsec configurations
    ipsec_config = IPsecConfig(
        ike_sa=ike_sa_config,
        auth=auth_config,
        child_sa=child_sa_config,
        ts=ts_config,
        id=id_config,
    )

    # Create TestConfiguration instances for client and server
    client_config = TestConfiguration(
        global_config=global_config,
        network_config=network_config_client,
        pki_config=pki_config_client,
        ipsec_config=ipsec_config,
    )

    server_config = TestConfiguration(
        global_config=global_config,
        network_config=network_config_server,
        pki_config=pki_config_server,
        ipsec_config=ipsec_config,
    )

    # Initialize the server with server_config
    server_manager = IPsecServerManager(server_config)

    yield client_config, server_manager


@pytest.mark.real_world
@pytest.fixture(scope="module")
def ipsec_infrastructure():
    """
    Pytest fixture to set up and tear down the IPsec testing infrastructure.
    """
    conf_path = REPOSITORY_ROOT / "configs" / "test-infra" / "libvirt"
    fs_path = REPOSITORY_ROOT / "configs" / "test-infra" / "filesystems"
    build_configs = REPOSITORY_ROOT / "configs" / "test-infra" / "fs-config"

    # Initialize the IpsecInfraTester
    tester = IpsecInfraTester(
        logger=logger,
        xml_confs_path=conf_path,
        fs_path=fs_path,
        build_configs=build_configs,
        start=True,
        build_fs=False,
    )

    # Yield to allow the test to run
    yield tester

    # Teardown: The fixture code after yield is executed after the test completes
    # tester.teardown()
    del tester


@pytest.mark.real_world
@pytest.fixture(scope="module")
def ipsec_infrastructure_manual():
    """
    Pytest fixture to set up and tear down the IPsec testing infrastructure.
    """
    logger.warning(
        "The setup infrastructure for running this test must be launched manually before running the test."
    )


@pytest.fixture
def ipsec_confs_infrastructure():
    """
    Pytest fixture to set up and tear down the IPsec testing infrastructure.
    """
    mock_config_content_client = f"""
[ARGS]
timeout = 15
verbose = True
logfile = /tmp/ipsec.log
# Chemin du binaire tcpreplay
tcpreplay = /usr/bin/tcpreplay
# Dossier de stockage pour les fichiers pcap
pcap_path = /tmp/pcap

[IF]
# Interface d'émission/réception
interface = br-ext
# IP du chiffreur émission
ipsec_src = 127.0.0.1
# IP du chiffreur distant
ipsec_dst = 127.0.0.1
# IP d'émission inital
ip_src = 127.0.0.1
# IP de destination final
ip_dst = 127.0.0.1
# Port d'émission
port_src = 4501
# Port de destination
port_dst = 4500
ip_dst_forbid = ***********

[NAT_T]
nat_t = True
nat_port_src = 4501
nat_port_dst = 4500

[IKE_SA]
encr = ENCR_AES_GCM_16
encr_size = 256
prf = PRF_HMAC_SHA2_256
integ = AUTH_HMAC_SHA2_256_128
groupdesc = 256randECPgr

[AUTH]
auth_method = ECDSA_SECP256R1_SHA256

[CHILD_SA]
encr = ENCR_AES_GCM_16
encr_size = 256
integ = AUTH_HMAC_SHA2_256_128
groupdesc = 256randECPgr
esn = ESN

[ESP]
crypto = False
replay = False
nat_t = False
when = False

[CA]
ca_root = {REPOSITORY_ROOT}/configs/test-infra/fs-config/debian-client/etc/certs/ca_cert.pem
pub_key = {REPOSITORY_ROOT}/configs/test-infra/fs-config/debian-client/etc/certs/debian-client-cert
prv_key = {REPOSITORY_ROOT}/configs/test-infra/fs-config/debian-client/etc/certs/debian-client-key
trust_chain = %(pub_key)s

[TS]
tsi_ip_range   = 127.0.0.1-127.0.0.1
tsi_port_range = 0-65535
tsr_ip_range   = 127.0.0.1-127.0.0.1
tsr_port_range = 0-65535

[ID]
idi_type = ID_FQDN
idi_data = debian-client.lan
idr_type = ID_FQDN
idr_data = ipsecdr-gw.lan
    """

    mock_config_content_server = f"""
[ARGS]
timeout = 15
verbose = True
logfile = /tmp/ipsec.log
# Chemin du binaire tcpreplay
tcpreplay = /usr/bin/tcpreplay
# Dossier de stockage pour les fichiers pcap
pcap_path = /tmp/pcap

[IF]
# Interface d'émission/réception
interface = br-ext
# IP du chiffreur émission
ipsec_src = 127.0.0.1
# IP du chiffreur distant
ipsec_dst = 127.0.0.1
# IP d'émission inital
ip_src = 127.0.0.1
# IP de destination final
ip_dst = 127.0.0.1
# Port d'émission
port_src = 4500
# Port de destination
port_dst = 0
ip_dst_forbid = ***********

[NAT_T]
nat_t = True
nat_port_src = 4500
nat_port_dst = 0

[IKE_SA]
encr = ENCR_AES_GCM_16
encr_size = 256
prf = PRF_HMAC_SHA2_256
integ = AUTH_HMAC_SHA2_256_128
groupdesc = 256randECPgr

[AUTH]
auth_method = ECDSA_SECP256R1_SHA256

[CHILD_SA]
encr = ENCR_AES_GCM_16
encr_size = 256
integ = AUTH_HMAC_SHA2_256_128
groupdesc = 256randECPgr
esn = ESN

[ESP]
crypto = False
replay = False
nat_t = False
when = False

[CA]
ca_root = {REPOSITORY_ROOT}/configs/test-infra/fs-config/strongswan-gw/etc/certs/ca_cert.pem
pub_key = {REPOSITORY_ROOT}/configs/test-infra/fs-config/strongswan-gw/etc/certs/strongswan-gw-cert
prv_key = {REPOSITORY_ROOT}/configs/test-infra/fs-config/strongswan-gw/etc/certs/strongswan-gw-key
trust_chain = %(pub_key)s

[TS]
tsi_ip_range   = 127.0.0.1-127.0.0.1
tsi_port_range = 0-65535
tsr_ip_range   = 127.0.0.1-127.0.0.1
tsr_port_range = 0-65535

[ID]
idi_type = ID_FQDN
idi_data = debian-client.lan
idr_type = ID_FQDN
idr_data = ipsecdr-gw.lan

[TEST_SP]
ip_dst_interdit = ***********
    """

    test_config_file_client = io.StringIO(mock_config_content_client)
    test_config_file_server = io.StringIO(mock_config_content_server)
    with patch("builtins.open", return_value=test_config_file_client):
        config_parser_clt = IPsecDRConfigParser("dummy_path_clt.ini")
    with patch("builtins.open", return_value=test_config_file_server):
        config_parser_srv = IPsecDRConfigParser("dummy_path_srv.ini")

    client_config = TestConfiguration(
        global_config=config_parser_clt.global_config,
        network_config=config_parser_clt.network,
        pki_config=config_parser_clt.pki,
        ipsec_config=config_parser_clt.ipsec,
        esp_config=config_parser_clt.esp,
    )
    server_config = TestConfiguration(
        global_config=config_parser_srv.global_config,
        network_config=config_parser_srv.network,
        pki_config=config_parser_srv.pki,
        ipsec_config=config_parser_srv.ipsec,
        esp_config=config_parser_srv.esp,
    )

    yield client_config, server_config  # Yield both configurations


@pytest.fixture(scope="module")
def ipsec_srv_infrastructure():
    """
    Pytest fixture to set up and tear down the IPsec testing infrastructure.
    """
    mock_config_content_client = f"""
[ARGS]
timeout = 15
verbose = True
logfile = /tmp/ipsec.log
# Chemin du binaire tcpreplay
tcpreplay = /usr/bin/tcpreplay
# Dossier de stockage pour les fichiers pcap
pcap_path = /tmp/pcap

[IF]
# Interface d'émission/réception
interface = br-ext
# IP du chiffreur émission
ipsec_src = 127.0.0.1
# IP du chiffreur distant
ipsec_dst = 127.0.0.1
# IP d'émission inital
ip_src = 127.0.0.1
# IP de destination final
ip_dst = 127.0.0.1
# Port d'émission
port_src = 4501
# Port de destination
port_dst = 4500
ip_dst_forbid = ***********

[NAT_T]
nat_t = True
nat_port_src = 4501
nat_port_dst = 4500


[IKE_SA]
encr = ENCR_AES_GCM_16
encr_size = 256
prf = PRF_HMAC_SHA2_256
integ = AUTH_HMAC_SHA2_256_128
groupdesc = 256randECPgr


[AUTH]
auth_method = ECDSA_SECP256R1_SHA256


[CHILD_SA]

encr = ENCR_AES_GCM_16
encr_size = 256
integ = AUTH_HMAC_SHA2_256_128
groupdesc = 256randECPgr
esn = ESN

[ESP]
crypto = False
replay = False
nat_t = False
when = False

[CA]
ca_root = {REPOSITORY_ROOT}/configs/test-infra/fs-config/debian-client/etc/certs/ca_cert.pem
pub_key = {REPOSITORY_ROOT}/configs/test-infra/fs-config/debian-client/etc/certs/debian-client-cert
prv_key = {REPOSITORY_ROOT}/configs/test-infra/fs-config/debian-client/etc/certs/debian-client-key
trust_chain = %(pub_key)s

[TS]
tsi_ip_range   = 127.0.0.1-127.0.0.1
tsi_port_range = 0-65535
tsr_ip_range   = 127.0.0.1-127.0.0.1
tsr_port_range = 0-65535

[ID]
idi_type = ID_FQDN
idi_data = debian-client.lan
idr_type = ID_FQDN
idr_data = ipsecdr-gw.lan

    """

    mock_config_content_server = f"""
[ARGS]
timeout = 15
verbose = True
logfile = /tmp/ipsec.log
# Chemin du binaire tcpreplay
tcpreplay = /usr/bin/tcpreplay
# Dossier de stockage pour les fichiers pcap
pcap_path = /tmp/pcap

[IF]
# Interface d'émission/réception
interface = br-ext
# IP du chiffreur émission
ipsec_src = 127.0.0.1
# IP du chiffreur distant
ipsec_dst = 127.0.0.1
# IP d'émission inital
ip_src = 127.0.0.1
# IP de destination final
ip_dst = 127.0.0.1
# Port d'émission
port_src = 4500
# Port de destination
port_dst = 0
ip_dst_forbid = ***********


[NAT_T]
nat_t = True
nat_port_src = 4500
nat_port_dst = 0


[IKE_SA]
encr = ENCR_AES_GCM_16
encr_size = 256
prf = PRF_HMAC_SHA2_256
integ = AUTH_HMAC_SHA2_256_128
groupdesc = 256randECPgr


[AUTH]
auth_method = ECDSA_SECP256R1_SHA256


[CHILD_SA]

encr = ENCR_AES_GCM_16
encr_size = 256
integ = AUTH_HMAC_SHA2_256_128
groupdesc = 256randECPgr
esn = ESN

[ESP]
crypto = False
replay = False
nat_t = False
when = False

[CA]
ca_root = {REPOSITORY_ROOT}/configs/test-infra/fs-config/strongswan-gw/etc/certs/ca_cert.pem
pub_key = {REPOSITORY_ROOT}/configs/test-infra/fs-config/strongswan-gw/etc/certs/strongswan-gw-cert
prv_key = {REPOSITORY_ROOT}/configs/test-infra/fs-config/strongswan-gw/etc/certs/strongswan-gw-key
trust_chain = %(pub_key)s

[TS]
tsi_ip_range   = 127.0.0.1-127.0.0.1
tsi_port_range = 0-65535
tsr_ip_range   = 127.0.0.1-127.0.0.1
tsr_port_range = 0-65535

[ID]
idi_type = ID_FQDN
idi_data = debian-client.lan
idr_type = ID_FQDN
idr_data = ipsecdr-gw.lan

[TEST_SP]
ip_dst_interdit = ***********
    """

    test_config_file_client = io.StringIO(mock_config_content_client)
    test_config_file_server = io.StringIO(mock_config_content_server)
    with patch("builtins.open", return_value=test_config_file_client):
        config_parser_clt = IPsecDRConfigParser("dummy_path_clt.ini")
    with patch("builtins.open", return_value=test_config_file_server):
        config_parser_srv = IPsecDRConfigParser("dummy_path_srv.ini")

    client_config = TestConfiguration(
        global_config=config_parser_clt.global_config,
        network_config=config_parser_clt.network,
        pki_config=config_parser_clt.pki,
        ipsec_config=config_parser_clt.ipsec,
        esp_config=config_parser_clt.esp,
    )
    server_config = TestConfiguration(
        global_config=config_parser_srv.global_config,
        network_config=config_parser_srv.network,
        pki_config=config_parser_srv.pki,
        ipsec_config=config_parser_srv.ipsec,
        esp_config=config_parser_srv.esp,
    )
    server_manager = IPsecServerManager(server_config)

    yield client_config, server_manager
