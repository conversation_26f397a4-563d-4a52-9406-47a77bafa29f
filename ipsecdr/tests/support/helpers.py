import secrets
import tempfile
from pathlib import Path
from datetime import datetime, timedelta
from ipsecdr.core.IKEv2.init import forge_ikev2_init
from ipsecdr.core.IKEv2.auth import forge_ikev2_auth
from ipsecdr.core.crypto.ikev2_crypto import (
    IKEv2Algorithm,
    IKEv2KeyData,
)
from ipsecdr.core.IKEv2.utils import set_ikev2_crypto, set_ikev2_keys
from ipsecdr.utils.models import NotifyPayload
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.backends import default_backend
from cryptography import x509
from cryptography.x509.oid import NameOID, ExtendedKeyUsageOID
from cryptography.hazmat.primitives import hashes, serialization


def ensure_test_certificates_exist():
    """
    Checks for the existence of test certificates in
    REPOSITORY_ROOT/config/tests/.
    If they do not exist, creates them using secp256r1 and sha256 signatures.
    """
    # Determine repository root and certificates directory
    REPOSITORY_ROOT = (
        Path(__file__).resolve().parents[2]
    )  # Adjust the number of parents as needed
    certs_dir = REPOSITORY_ROOT / "configs" / "tests"
    certs_dir.mkdir(parents=True, exist_ok=True)

    # File paths for certificates and keys
    cert_i_path = certs_dir / "cert_i.pem"
    key_i_path = certs_dir / "key_i.pem"
    cert_r_path = certs_dir / "cert_r.pem"
    key_r_path = certs_dir / "key_r.pem"
    ca_cert_path = certs_dir / "ca.pem"
    ca_key_path = certs_dir / "ca_key.pem"  # Needed to sign the certs

    # Check if all certificates exist
    cert_files = [
        cert_i_path,
        key_i_path,
        cert_r_path,
        key_r_path,
        ca_cert_path,
    ]
    if not all(f.exists() for f in cert_files):
        # Create CA certificate and key
        if not ca_cert_path.exists() or not ca_key_path.exists():
            ca_key = ec.generate_private_key(ec.SECP256R1(), default_backend())
            ca_subject = x509.Name(
                [
                    x509.NameAttribute(NameOID.COMMON_NAME, "Test CA"),
                ]
            )
            ca_cert = (
                x509.CertificateBuilder()
                .subject_name(ca_subject)
                .issuer_name(ca_subject)
                .public_key(ca_key.public_key())
                .serial_number(x509.random_serial_number())
                .not_valid_before(datetime.utcnow())
                .not_valid_after(datetime.utcnow() + timedelta(days=365))
                .add_extension(
                    x509.BasicConstraints(ca=True, path_length=None),
                    critical=True,
                )
                .sign(ca_key, hashes.SHA256(), default_backend())
            )

            # Write CA key and certificate to files
            with open(ca_key_path, "wb") as f:
                f.write(
                    ca_key.private_bytes(
                        encoding=serialization.Encoding.PEM,
                        format=serialization.PrivateFormat.TraditionalOpenSSL,
                        encryption_algorithm=serialization.NoEncryption(),
                    )
                )
            with open(ca_cert_path, "wb") as f:
                f.write(ca_cert.public_bytes(serialization.Encoding.PEM))

        else:
            # Load existing CA key and certificate
            with open(ca_key_path, "rb") as f:
                ca_key = serialization.load_pem_private_key(
                    f.read(), password=None, backend=default_backend()
                )
            with open(ca_cert_path, "rb") as f:
                ca_cert = x509.load_pem_x509_certificate(
                    f.read(), default_backend()
                )

        # Create certificates and keys for initiator and responder
        for cert_path, key_path, common_name in [
            (cert_i_path, key_i_path, "Initiator"),
            (cert_r_path, key_r_path, "Responder"),
        ]:
            key = ec.generate_private_key(ec.SECP256R1(), default_backend())
            subject = x509.Name(
                [
                    x509.NameAttribute(NameOID.COMMON_NAME, common_name),
                ]
            )
            cert = (
                x509.CertificateBuilder()
                .subject_name(subject)
                .issuer_name(ca_cert.subject)
                .public_key(key.public_key())
                .serial_number(x509.random_serial_number())
                .not_valid_before(datetime.utcnow())
                .not_valid_after(datetime.utcnow() + timedelta(days=365))
                .add_extension(
                    x509.ExtendedKeyUsage(
                        [
                            ExtendedKeyUsageOID.CLIENT_AUTH,
                            ExtendedKeyUsageOID.SERVER_AUTH,
                        ]
                    ),
                    critical=False,
                )
                .sign(ca_key, hashes.SHA256(), default_backend())
            )

            # Write key and certificate to files
            with open(key_path, "wb") as f:
                f.write(
                    key.private_bytes(
                        encoding=serialization.Encoding.PEM,
                        format=serialization.PrivateFormat.TraditionalOpenSSL,
                        encryption_algorithm=serialization.NoEncryption(),
                    )
                )
            with open(cert_path, "wb") as f:
                f.write(cert.public_bytes(serialization.Encoding.PEM))


def load_test_certificates():
    ensure_test_certificates_exist()
    REPOSITORY_ROOT = Path(__file__).resolve().parents[2]
    certs_dir = REPOSITORY_ROOT / "configs" / "tests"

    # File paths for certificates and keys
    cert_i_path = certs_dir / "cert_i.pem"
    key_i_path = certs_dir / "key_i.pem"
    cert_r_path = certs_dir / "cert_r.pem"
    key_r_path = certs_dir / "key_r.pem"
    ca_cert_path = certs_dir / "ca.pem"

    return {
        "client_cert": str(cert_i_path),
        "client_key": str(key_i_path),
        "server_cert": str(cert_r_path),
        "server_key": str(key_r_path),
        "ca_cert": str(ca_cert_path),
    }


def generate_valid_dr_ec_point(curve_name: str = "secp256r1") -> bytes:
    """
    Generate a valid EC public key point for a given curve.

    :param curve_name: The name of the elliptic curve to use
    (default: 'secp256r1'). Available brainpoolp256r1.
    :return: The public key point as bytes.
    """
    if curve_name == "secp256r1":
        curve = ec.SECP256R1()
    elif curve_name == "brainpoolp256r1":
        curve = ec.BrainpoolP256R1()
    else:
        raise ValueError("Unsupported curve name")

    # Generate private key
    private_key = ec.generate_private_key(curve, default_backend())

    # Get the corresponding public key
    public_key = private_key.public_key()

    # Extract the public numbers (x, y coordinates)
    public_numbers = public_key.public_numbers()

    # Convert the public key point (x, y) to bytes
    x_bytes = public_numbers.x.to_bytes(
        (public_numbers.x.bit_length() + 7) // 8, "big"
    )
    y_bytes = public_numbers.y.to_bytes(
        (public_numbers.y.bit_length() + 7) // 8, "big"
    )

    # Return concatenated (x, y) point
    return x_bytes + y_bytes


def create_random_ikev2_sa():
    # Random values for testing
    groupdescs = [secrets.choice([28, 19])]  # Random Diffie-Hellman group
    ciphers = [secrets.choice([20, 13])]  # Random encryption algorithm
    key_lengths = [secrets.choice([128, 192, 256])]  # Random key length
    prfs = [secrets.choice([5, 7])]  # Random PRF function
    integrities = [secrets.choice([13, 14, 12])]  # Random integrity algorithm
    KEr = (
        generate_valid_dr_ec_point()
        if groupdescs[0] == 19
        else generate_valid_dr_ec_point("brainpoolp256r1")
    )
    # Generate nonce
    noncei = secrets.token_bytes(32)
    noncer = secrets.token_bytes(32)
    # Step 1: Initialize ikev2_crypto object with random DH group
    ikev2_crypto = IKEv2Algorithm(choix_dh=groupdescs[0])

    # Generate public key for Diffie-Hellman
    # KE = ikev2_crypto.dh.public_key

    # Step 2: Set algorithms (encryption, PRF, integrity) with random values
    ikev2_crypto.set_other_algo(
        choix_chiffrement=ciphers[0],
        key_size=key_lengths[0],
        choix_integrite=integrities[0],
        choix_prf=prfs[0],
    )

    # Step 3: Generate a random responder key exchange value (fake KE)
    ikev2_crypto.dh.compute_secret(KEr)  # Random fake KE value

    # Step 4: Generate a random nonce sum for key derivation
    nonces_sum = noncei + noncer

    # Step 5: Create ikev2_keys object and set key seed using PRF
    ikev2_keys = IKEv2KeyData(ikev2_crypto.dh.shared_secret)

    # Generate the SKEYSEED based on the nonce sum and shared secret
    skeyseed = ikev2_crypto.prf.prf(
        key=nonces_sum, data=ikev2_crypto.dh.shared_secret
    )
    ikev2_keys.setkeyseed(skeyseed)

    # Step 6: Set up key materials (encryption, integrity, PRF) for testing
    len_keys_cipher = ikev2_crypto.chiffrement.key_size

    if ikev2_crypto.chiffrement.AES == "GCM":
        len_keys_integrity = 0
    else:
        len_keys_integrity = ikev2_crypto.integrite.key_size
    # recuperation de la taille de la cle pour le prf
    len_keys_prf = ikev2_crypto.prf.key_size
    # Calcul de la taille total des cles en fonctions du nombre necessaire
    # chiffrement 2 cles : sk_ei & sk_er
    # integrite 2 cles : sk_ai & sk_ei
    # prf 3 cles : sk_d & sk_pi & sk_pr
    total_size_key = (
        len_keys_cipher * 2 + len_keys_integrity * 2 + len_keys_prf * 3
    )

    # Create random key material for testing
    keymat = ikev2_crypto.prf.prfplus(
        skeyseed,
        nonces_sum + secrets.token_bytes(8) + secrets.token_bytes(8),
        total_size_key,
    )

    # Store the derived keys in ikev2_keys object
    ikev2_keys.setkey(
        keymat, len_keys_cipher, len_keys_integrity, len_keys_prf
    )

    return ikev2_crypto, ikev2_keys


def create_random_ikev2_init_():
    """
    Generate cryptographic values, keys, and initial IKEv2 packets
    for both initiator and responder.
    Also, simulate key generation and assignment to public_key, private_key,
    and algo_key fields.
    """
    ensure_test_certificates_exist()
    REPOSITORY_ROOT = Path(__file__).resolve().parents[2]
    certs_dir = REPOSITORY_ROOT / "configs" / "tests"
    cert_i_path = certs_dir / "cert_i.pem"
    key_i_path = certs_dir / "key_i.pem"
    cert_r_path = certs_dir / "cert_r.pem"
    key_r_path = certs_dir / "key_r.pem"
    ca_cert_path = certs_dir / "ca.pem"

    # Define cryptographic parameters
    ciphers = [20]  # ENCR_AES_GCM
    key_lengths = [256]
    prfs = [5]  # PRF_HMAC_SHA2_256
    integrities = [0]  # No integrity algorithm
    groupdescs = [19]  # SECP256R1

    # Initialize IKEv2Algorithm instances for Initiator and Responder
    ikev2_crypto_i = IKEv2Algorithm(choix_dh=groupdescs[0])
    ikev2_crypto_r = IKEv2Algorithm(choix_dh=groupdescs[0])

    # Generate nonces
    nonce_i = secrets.token_bytes(32)
    nonce_r = secrets.token_bytes(32)

    # Generate Key Exchange data
    KE_i = ikev2_crypto_i.dh.public_key
    KE_r = ikev2_crypto_r.dh.public_key

    # Generate SPIs
    spi_i = secrets.token_bytes(8)
    spi_r = secrets.token_bytes(8)

    # Forge IKE_SA_INIT packets for Initiator and Responder
    init_i_pkt = forge_ikev2_init(
        mode="Initiator",
        spi_i=spi_i,
        spi_r=spi_r,
        ciphers=ciphers,
        key_lengths=key_lengths,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        nonce=nonce_i,
        cookie=None,
        KE=KE_i,
        notify_extras=[NotifyPayload(type="INITIAL_CONTACT", notify=b"")],
        mid=1,
    )

    init_r_pkt = forge_ikev2_init(
        mode="Responder",
        spi_i=spi_i,
        spi_r=spi_r,
        ciphers=ciphers,
        key_lengths=key_lengths,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        nonce=nonce_r,
        cookie=None,
        KE=KE_r,
        notify_extras=[NotifyPayload(type="INITIAL_CONTACT", notify=b"")],
        mid=1,
    )

    # Set up cryptographic algorithms based on received packets
    ikev2_crypto_i = set_ikev2_crypto(
        ikev2_pkt=init_r_pkt,
        ikev2_crypto=ikev2_crypto_i,
    )

    ikev2_crypto_r = set_ikev2_crypto(
        ikev2_pkt=init_i_pkt,
        ikev2_crypto=ikev2_crypto_r,
    )

    # Derive keys for Initiator
    ikev2_keys_i = set_ikev2_keys(
        ke=init_r_pkt["IKEv2_KE"].ke,
        nonce_i=nonce_i,
        nonce_r=nonce_r,
        spi_i=spi_i,
        spi_r=spi_r,
        ikev2_crypto=ikev2_crypto_i,
        pub_cert=str(cert_i_path),
        key_cert=str(key_i_path),
        trust_chain=[str(ca_cert_path)],
    )

    # Derive keys for Responder
    ikev2_keys_r = set_ikev2_keys(
        ke=init_i_pkt["IKEv2_KE"].ke,
        nonce_i=nonce_i,
        nonce_r=nonce_r,
        spi_i=spi_i,
        spi_r=spi_r,
        ikev2_crypto=ikev2_crypto_r,
        pub_cert=str(cert_r_path),
        key_cert=str(key_r_path),
        trust_chain=[str(ca_cert_path)],
    )

    return (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        init_i_pkt,
        init_r_pkt,
    )


def create_random_ikev2_init(
    spi_i: bytes,
    spi_r: bytes,
):
    """
    Generate cryptographic values, keys, and initial IKEv2 packets
    for both initiator and responder.
    Also, simulate key generation and assignment to public_key,
    private_key, and algo_key fields.
    """
    ensure_test_certificates_exist()
    REPOSITORY_ROOT = Path(__file__).resolve().parents[2]
    certs_dir = REPOSITORY_ROOT / "configs" / "tests"
    cert_i_path = certs_dir / "cert_i.pem"
    key_i_path = certs_dir / "key_i.pem"
    cert_r_path = certs_dir / "cert_r.pem"
    key_r_path = certs_dir / "key_r.pem"
    ca_cert_path = certs_dir / "ca.pem"

    # Define cryptographic parameters
    ciphers = [20]  # ENCR_AES_GCM
    key_lengths = [256]
    prfs = [5]  # PRF_HMAC_SHA2_256
    integrities = [0]  # No integrity algorithm
    groupdescs = [19]  # SECP256R1
    cookie = None
    mid = 1

    # Generate nonces for Initiator and Responder
    nonce_i = secrets.token_bytes(32)
    nonce_r = secrets.token_bytes(32)

    # Initialize IKEv2Algorithm instances for Initiator and Responder
    ikev2_crypto_i = IKEv2Algorithm(choix_dh=groupdescs[0])
    ikev2_crypto_r = IKEv2Algorithm(choix_dh=groupdescs[0])

    # Generate Key Exchange data
    KE_i = ikev2_crypto_i.dh.public_key
    KE_r = ikev2_crypto_r.dh.public_key

    # Forge IKE_SA_INIT packets
    init_i_pkt = forge_ikev2_init(
        mode="Initiator",
        spi_i=spi_i,
        spi_r=spi_r,
        ciphers=ciphers,
        key_lengths=key_lengths,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        nonce=nonce_i,
        cookie=cookie,
        KE=KE_i,
        notify_extras=[NotifyPayload(type="INITIAL_CONTACT", notify=b"")],
        mid=mid,
    )

    init_r_pkt = forge_ikev2_init(
        mode="Responder",
        spi_i=spi_i,
        spi_r=spi_r,
        ciphers=ciphers,
        key_lengths=key_lengths,
        prfs=prfs,
        integrities=integrities,
        groupdescs=groupdescs,
        nonce=nonce_r,
        cookie=cookie,
        KE=KE_r,
        notify_extras=[NotifyPayload(type="INITIAL_CONTACT", notify=b"")],
        mid=mid,
    )

    # Set up cryptographic algorithms
    ikev2_crypto_i = set_ikev2_crypto(
        ikev2_pkt=init_r_pkt,
        ikev2_crypto=ikev2_crypto_i,
    )
    ikev2_crypto_r = set_ikev2_crypto(
        ikev2_pkt=init_i_pkt,
        ikev2_crypto=ikev2_crypto_r,
    )

    # Derive keys for Initiator
    ikev2_keys_i = set_ikev2_keys(
        ke=init_r_pkt["IKEv2_KE"].ke,
        nonce_i=nonce_i,
        nonce_r=nonce_r,
        spi_i=spi_i,
        spi_r=spi_r,
        ikev2_crypto=ikev2_crypto_i,
        pub_cert=str(cert_i_path),
        key_cert=str(key_i_path),
        trust_chain=[str(ca_cert_path)],
    )

    # Derive keys for Responder
    ikev2_keys_r = set_ikev2_keys(
        ke=init_i_pkt["IKEv2_KE"].ke,
        nonce_i=nonce_i,
        nonce_r=nonce_r,
        spi_i=spi_i,
        spi_r=spi_r,
        ikev2_crypto=ikev2_crypto_r,
        pub_cert=str(cert_r_path),
        key_cert=str(key_r_path),
        trust_chain=[str(ca_cert_path)],
    )

    return (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        init_i_pkt,
        init_r_pkt,
    )


def create_ikev2_sa(
    spi_i: bytes,
    spi_r: bytes,
):
    """
    Simulate the IKE_SA_INIT and IKE_AUTH exchanges between an initiator
    and responder.
    This function uses create_random_ikev2_init() and performs
    a fake AUTH exchange using forge_ikev2_auth for both initiator
    and responder.
    """
    # Step 1: Generate cryptographic parameters and initial IKEv2 packets
    (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        init_i_pkt,
        init_r_pkt,
    ) = create_random_ikev2_init(spi_i=spi_i, spi_r=spi_r)

    # Common parameters

    mid = 1
    esn = 1
    notify_options = [NotifyPayload(type="INITIAL_CONTACT", notify=b"")]
    idi_type = 1  # Example ID type
    idi_data = "192.168.1.1"
    idr_type = 1
    idr_data = "192.168.1.2"
    traffic_selector_client = ["172.32.45.8", "172.16.45.254", 0, 65535]
    traffic_selector_server = ["172.32.43.8", "172.16.43.254", 0, 65535]
    auth_method = 9  # Example authentication method secp256r1

    # Step 2: Forge IKE_AUTH message from Initiator to Responder
    ikev2_auth_i_pkt = forge_ikev2_auth(
        mode="Initiator",
        spi_i=spi_i,
        spi_r=spi_r,
        mid=mid + 1,
        ikev2_crypto=ikev2_crypto_i,
        ikev2_keys=ikev2_keys_i,
        esn=esn,
        notify_options=notify_options,
        idi_type=idi_type,
        idi_data=idi_data,
        idr_type=idr_type,
        idr_data=idr_data,
        traffic_selector_client=traffic_selector_client,
        traffic_selector_server=traffic_selector_server,
        init_i_pkt=init_i_pkt,
        init_r_pkt=init_r_pkt,
        auth_method=auth_method,
    )

    # Step 3: Forge IKE_AUTH response from Responder to Initiator
    ikev2_auth_r_pkt = forge_ikev2_auth(
        mode="Response",
        spi_i=spi_i,
        spi_r=spi_r,
        mid=mid + 1,
        ikev2_crypto=ikev2_crypto_r,
        ikev2_keys=ikev2_keys_r,
        esn=esn,
        notify_options=notify_options,
        idi_type=idi_type,
        idi_data=idi_data,
        idr_type=idr_type,
        idr_data=idr_data,
        traffic_selector_client=traffic_selector_client,
        traffic_selector_server=traffic_selector_server,
        init_i_pkt=init_i_pkt,
        init_r_pkt=init_r_pkt,
        auth_method=auth_method,
    )

    # Step 4: Update the keys and cryptographic parameters as needed
    # In a real exchange, keys would be derived from shared secrets and nonces
    # For testing purposes, we assume the keys are already correctly set
    #  in ikev2_keys_i and ikev2_keys_r

    return (
        ikev2_crypto_i,
        ikev2_keys_i,
        ikev2_crypto_r,
        ikev2_keys_r,
        init_i_pkt,
        init_r_pkt,
        ikev2_auth_i_pkt,
        ikev2_auth_r_pkt,
    )


def write_config_file(content: str) -> str:
    """
    Write the provided content to a temporary file and return its path.

    :param content: A string to write
    :return: Created file name
    :rtype: str"""

    with tempfile.NamedTemporaryFile(mode="w+", delete=False) as tmp_file:
        tmp_file.write(content)
        return tmp_file.name
