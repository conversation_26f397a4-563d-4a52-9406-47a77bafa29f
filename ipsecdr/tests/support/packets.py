import binascii
from scapy.contrib.ikev2 import IKEv2


# Exchange with refused auth
init_i = IKEv2(
    binascii.unhexlify(
        "5c7fab946035f76400000000000000002120220800000000000001542200007402000034010100050300000c01000014800e010003000008020000050300000802000007030000080400001c00000008040000130000003c020100060300000c0100000d800e0100030000080300000c03000008020000050300000802000007030000080400001c000000080400001328000048001c00004a5c09d6b0d2be7d7b919ba2892b107e2e2818980de1411bf76438c111eeba224319ca1467e5a255a9d40f017178af9b0220a2264e5b229d6da6be32fd23afb52900002425d6ea7400b7a36196258077d6b1c1d2689367b377e9c85fc5bf6aa2601daa7d2900001c00004004ff7bcd8b6a05f18bcb94620b3ac6453326dd60f02900001c000040058bfd2238497f87c6edb159e2ab758da6517032d0290000080000402e290000100000402f00020003000400050000000800004016"
    )
)
init_r = IKEv2(
    binascii.unhexlify(
        "5c7fab946035f764d2a8ed04135fcf0f2120222000000000000001212200002800000024010100030300000c01000014800e01000300000802000005000000080400001c28000048001c00008c9187ef00cc9eff690e9bd420f768688c23b1faeda89e1081ffdef3023e67362c24847e5b4d309f14d615a836fca8b9d8e277336b15ef2fe70017c4479576e529000024da021722bfa750ebf48d09208393273096a9ddb651100f246a79e920c6aa66102900001c00004004aa5b6e3d184873f4a3f596a7ee67fcf505db18622600001c00004005cfb20e4027625473557b8b642d24bf5e81ed282a29000019040765c9767a2993ee144339585be5fa7a5bb14b27290000080000402e290000100000402f00020003000400050000000800004014"
    )
)
auth_i = IKEv2(
    binascii.unhexlify(
        "5c7fab946035f764d2a8ed04135fcf0f2e202308000000010000037323000357dc08376289b0c2c8d207cbc1f1caf128d084e89ac892f4010f416084f7d1bf2d7a9333c3c0b31cdb1511aa26cfd87e258dfbb626cedabfc83049714d19b357074971103204800081e9dcb1ff364fa92a4e9e24359a83daab181fc42f795c788b76ae0ca4e0b329eeb02c7865c3172cf415caa00d952ee3d9154d5b0822e66bd072988863f2bcf8999ed24a170fd3d75526d8b1400c887d49f8fac6aa7afd3b622010f286453f4b2920fde4751027fe6a52c2d5f4474c445aba183a88a94e7c7550c963c33753ebdfad00c09308b925ffc88fcb256669011410c55eaa2cdd118820874095345233fc2331f11736d4ff3af3b618846407050cc1373310f1130b0059034f4115f3e6449343306bc9b364bbe45b538ef6984768551b3cd094083373470bceda8f1d167a0dabfa21ad61e7fbab80fe2e956d9fc2222b465bf41512d70ec66a5f8b955b6c3f183d78e46e7a84b2fad6a332b88a8b191abc1eeb1e8a90e78e64e72963ac0488ac4c911369fef17c8e4d52e5be261744b94b8f3e66d96c42d947e186b429c17817978ca20debc4d51f49e75cd4f37f38a42c7dcbbd0f322a1e2eaca3fd73adeb10fcdcc2ac689baf606ea2cef8dc3ed80e72463965f396d3d00d24a946af7c3202637b0de0749e4c0e9f84ed93775f368dbfda40a70dce484c2142003f57ff39c340b81154113832c48cbe8f86cdafdb907166e668b76230cbf098a9953dd5ad8ab520181f96ea22118d0c21d6654133ffc545e42537dd48a98b766470a1c3904b00d7f60197ff9cd0865033b1a35af72d7aa302465c5ef10178b0260b84e4dbb4e5997df9fe5d8a174c73070467b1bd6cbeaa5f84a529260fd93b24dbed80baa36af4e25ce9ef118122508bc4e133a9c25d5150d1f1560afb5432675d8b0adffe1c445fb6daf418c3551fca5df9399a4889b6338c584f8a9557dca8e39d15b5182133235a09fe682c65d25d313f230366865d986140e137ce420a261ebb0ec34b01090a3ac528be3b0e52e66d2215da857a4d13b504de7773f4528edc65263b32f71ef820c0a7529678e05dba7d7692634a381ea1daef1ea967057f07c3367a48f1bd571436d88c9af582c09fa116ba745ded69a32b9bef4e13d265d8ef8193118e93122baaf7ae32dd059dbe1015483ac473a84c014d53fa16310933faa1374af76b8d5605a0973788"
    )
)
auth_r = IKEv2(
    binascii.unhexlify(
        "5c7fab946035f764d2a8ed04135fcf0f2e202320000000010000004129000025dc49d629cbdecabfb60d06e420e687c532ba07232cae1cb70844be8ef12281f4e8"
    )
)
