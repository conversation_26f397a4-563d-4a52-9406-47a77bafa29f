import pytest
import asyncio
from pathlib import Path
from ipsecdr.engine.orchestration import Orchestra<PERSON>
from tests.support.logger import logger

REPOSITORY_ROOT = Path(__file__).resolve().parents[1]


@pytest.mark.real_world
@pytest.mark.asyncio
async def test_valid_ipsec_client(ipsec_srv_infrastructure):
    client_config, server_manager = ipsec_srv_infrastructure
    await server_manager.start()
    await asyncio.sleep(1)
    test_scenario_name = "T.00_VALID_IPSEC"
    test_scenario_file = (
        REPOSITORY_ROOT
        / "ipsecdr"
        / "engine"
        / "tests"
        / f"{test_scenario_name}.yml"
    )
    if not test_scenario_file.exists():
        pytest.fail(f"Test scenario file {test_scenario_file} does not exist.")
    test_list = [test_scenario_name]
    orchestrator = Orchestrator(
        config=client_config,
        test_list=test_list,
        max_testers=1,
        max_checkers=1,
        mode="initiator",  # Run in client mode
    )
    await orchestrator.run()

    # Access the test results from the orchestrator's test pool
    test_results = orchestrator.test_pool[test_scenario_name]

    # Validate the results
    test_data = test_results.get(1)  # Test ID is 1
    if test_data is None:
        pytest.fail("Test data not found in orchestrator's test pool.")

    outcome = test_data.outcome
    if outcome is None:
        pytest.fail("Test outcome not available.")

    result = outcome.result

    if result != "Passed":
        reason = outcome.reason
        pytest.fail(f"Test {test_scenario_name} failed: {reason}")
    else:
        print(f"Test {test_scenario_name} passed successfully.")
    await server_manager.stop()


@pytest.mark.real_world
@pytest.mark.asyncio
async def test_valid_ipsec_server(ipsec_confs_infrastructure):
    client_config, server_config = ipsec_confs_infrastructure
    test_scenario_name_srv = "T.00_VALID_IPSEC_SRV"
    test_scenario_name_clt = "T.00_VALID_IPSEC_CLT"
    test_scenario_file_clt = (
        REPOSITORY_ROOT
        / "ipsecdr"
        / "engine"
        / "tests"
        / f"{test_scenario_name_clt}.yml"
    )
    test_scenario_file_srv = (
        REPOSITORY_ROOT
        / "ipsecdr"
        / "engine"
        / "tests"
        / f"{test_scenario_name_srv}.yml"
    )
    if not test_scenario_file_clt.exists():
        pytest.fail(
            f"Test scenario file {test_scenario_file_clt} does not exist."
        )
    if not test_scenario_file_srv.exists():
        pytest.fail(
            f"Test scenario file {test_scenario_file_srv} does not exist."
        )
    test_list_clt = [test_scenario_name_clt]
    test_list_srv = [test_scenario_name_srv]

    # Create orchestrators for both server and client
    orchestrator_server = Orchestrator(
        config=server_config,
        test_list=test_list_srv,
        max_testers=1,
        max_checkers=1,
        mode="responder",  # Run in server mode
    )
    orchestrator_client = Orchestrator(
        config=client_config,
        test_list=test_list_clt,
        max_testers=1,
        max_checkers=1,
        mode="initiator",  # Run in client mode
    )
    logger.debug("RUN")
    # Run both orchestrators concurrently
    await asyncio.gather(
        orchestrator_server.run(),
        orchestrator_client.run(),
    )

    logger.debug("END RUN")
    # Combine test results from both orchestrators
    test_results_server = orchestrator_server.test_pool[test_scenario_name_srv]
    test_results_client = orchestrator_client.test_pool[test_scenario_name_clt]

    # Check the server test results
    test_data_server = test_results_server.get(1)
    if test_data_server is None:
        pytest.fail("Test data not found in server orchestrator's test pool.")

    outcome_server = test_data_server.outcome
    if outcome_server is None:
        pytest.fail("Test outcome not available from server.")

    result_server = outcome_server.result
    if result_server != "Passed":
        reason = outcome_server.reason
        pytest.fail(f"Server test {test_scenario_name_srv} failed: {reason}")

    # Check the client test results
    test_data_client = test_results_client.get(1)
    if test_data_client is None:
        pytest.fail("Test data not found in client orchestrator's test pool.")

    outcome_client = test_data_client.outcome
    if outcome_client is None:
        pytest.fail("Test outcome not available from client.")

    result_client = outcome_client.result
    if result_client != "Passed":
        reason = outcome_client.reason
        pytest.fail(f"Client test {test_scenario_name_clt} failed: {reason}")


@pytest.mark.real_world
@pytest.mark.asyncio
async def test_valid_ipsec_server_one_test(ipsec_confs_infrastructure):
    client_config, server_config = ipsec_confs_infrastructure
    test_scenario_name = "T.00_VALID_IPSEC"
    test_scenario_file = (
        REPOSITORY_ROOT
        / "ipsecdr"
        / "engine"
        / "tests"
        / f"{test_scenario_name}.yml"
    )
    if not test_scenario_file.exists():
        pytest.fail(f"Test scenario file {test_scenario_file} does not exist.")
    test_list = [test_scenario_name]

    # Create orchestrators for both server and client
    orchestrator_server = Orchestrator(
        config=server_config,
        test_list=test_list,
        max_testers=1,
        max_checkers=1,
        mode="responder",  # Run in server mode
    )
    orchestrator_client = Orchestrator(
        config=client_config,
        test_list=test_list,
        max_testers=1,
        max_checkers=1,
        mode="initiator",  # Run in client mode
    )
    # Run both orchestrators concurrently
    await asyncio.gather(
        orchestrator_server.run(),
        orchestrator_client.run(),
    )

    test_results_server = orchestrator_server.test_pool[test_scenario_name]
    test_results_client = orchestrator_client.test_pool[test_scenario_name]

    test_data_server = test_results_server.get(1)
    if test_data_server is None:
        pytest.fail("Test data not found in server orchestrator's test pool.")

    outcome_server = test_data_server.outcome
    if outcome_server is None:
        pytest.fail("Test outcome not available from server.")

    result_server = outcome_server.result
    if result_server != "Passed":
        reason = outcome_server.reason
        pytest.fail(f"Server test {test_scenario_name} failed: {reason}")

    # Check the client test results
    test_data_client = test_results_client.get(1)
    if test_data_client is None:
        pytest.fail("Test data not found in client orchestrator's test pool.")

    outcome_client = test_data_client.outcome
    if outcome_client is None:
        pytest.fail("Test outcome not available from client.")

    result_client = outcome_client.result
    if result_client != "Passed":
        reason = outcome_client.reason
        pytest.fail(f"Client test {test_scenario_name} failed: {reason}")

    print(f"Test {test_scenario_name} passed successfully.")


"""
@pytest.mark.real_world
@pytest.mark.asyncio
async def test_invalid_ipsec_configuration(ipsec_confs_infrastructure):
    ""\"
    Test the system's behavior with an invalid IPsec configuration.
    ""\"
    client_config, server_config = ipsec_confs_infrastructure

    # Introduce an invalid parameter in the client configuration
    client_config.ipsec.ike_sa.encr = "INVALID_ENCRYPTION_ALGO"

    test_scenario_name = "T.00_VALID_IPSEC"
    test_list = [test_scenario_name]

    # Create orchestrators for both server and client
    orchestrator_server = Orchestrator(
        config=server_config,
        test_list=test_list,
        max_testers=1,
        max_checkers=1,
        mode="server",
    )
    orchestrator_client = Orchestrator(
        config=client_config,
        test_list=test_list,
        max_testers=1,
        max_checkers=1,
        mode="client",
    )

    # Run both orchestrators concurrently
    await asyncio.gather(
        orchestrator_server.run(),
        orchestrator_client.run(),
    )

    # Access the test results
    test_results_client = orchestrator_client.test_pool
    test_data_client = test_results_client.get(1)
    if test_data_client is None:
        pytest.fail("Test data not found in client orchestrator's test pool.")

    outcome_client = test_data_client.get("outcome")
    if outcome_client is None:
        pytest.fail("Test outcome not available from client.")

    result_client = outcome_client.get("result")
    if result_client != "Failed":
        pytest.fail("Test should have failed due to invalid configuration.")
    else:
        print("Invalid configuration test passed (failure as expected).")
"""
