import os
from shutil import copy
from xml.etree import ElementTree
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.primitives import serialization
from .libvirtHandler import libvirtHandler
from .rootfsBuilder import RootFsBuilder
from .utils import (
    generate_ca,
    generate_certificate,
)


class IpsecInfraTester(libvirtHandler):
    """
    IpsecInfraTester is a class to test vpn infrastructures
    """

    def print(self, text: str = "", style: str = "default") -> None:
        self.logger.info(f"[{style}]{text}[/]")

    def printE(self, text: str = "") -> None:
        self.logger.error(f"[bold red]{text}[/]")

    def printW(self, text: str = "") -> None:
        self.logger.warning(f"[bold yellow]{text}[/]")

    def printI(self, text: str = "") -> None:
        self.logger.info(f"[green]{text}[/]")

    def check_paths(self, paths: list):
        errors = []
        for path in paths:
            if not os.path.exists(path):
                self.printE(f"Cannot find file_confs directory {path}")
                errors.append(path)
        if errors:
            raise FileNotFoundError

    def load_xml_file(self, xml_file_path: str) -> dict:
        with open(xml_file_path, "r") as f:
            xml_content = f.read()
        if name := ElementTree.fromstring(xml_content).find("name").text:
            return {name: xml_content}
        else:
            print("Cannot find name of xml file wont continue")
            return {}

    def make_libvirt_configs(self, xml_confs_path) -> None:
        if not os.path.exists(xml_confs_path):
            self.printE("XML configs path not found!")
            raise FileNotFoundError
        for file in os.listdir(xml_confs_path):
            file_path = xml_confs_path / file
            tree = ElementTree.parse(file_path)
            if name := tree.find("name").text:
                continue
            else:
                self.printE("Cannot find name in configuration")
                return
            root = tree.getroot()
            match root.tag:
                case "domain":
                    if (
                        source_fs := root.find("devices")
                        .find("filesystem")
                        .find("source")
                    ):
                        continue
                    else:
                        self.printE("Cannot find devices tag in domain config")
                        return
                    try:
                        source_fs.set("dir", self.filesystems_path / name)
                    except Exception as e:
                        self.printE(
                            f"Error while trying to update root filesystem: \
                        {repr(e)}"
                        )
                        return
                    tree.write(file_path)
                case "network":
                    continue
                case _:
                    self.printE(
                        f"The following config is not supported {file_path}."
                    )
                    continue

    def load_libvirt_configs(self, path) -> (dict, dict):
        networks = {}
        machines = {}
        for file in os.listdir(path):
            if "network" in file:
                networks.update(self.load_xml_file(path / file))
            elif "container" in file:
                machines.update(self.load_xml_file(path / file))
            elif "vm" in file:
                machines.update(self.load_xml_file(path / file))
            else:
                self.printE(
                    "Won't load anything other than \
network or container or vm configs"
                )
        return networks, machines

    def make_pki(
        self,
    ) -> bool:
        # self signed CA
        # key-pair with certs for each node aka clients / gw
        ca_key, ca_cert = generate_ca(ec_type=ec.SECP256R1)
        for name, _ in self.machines_xml.items():
            key, cert = generate_certificate(
                ec.SECP256R1, ca_key, ca_cert, name, [f"{name}.lan"]
            )
            dom_dir = self.build_configs / name
            try:
                os.makedirs(f"{dom_dir}/etc/certs/")
            except FileExistsError:
                self.printW(f"Directory exist {dom_dir}/etc/certs /")

            with open(f"{dom_dir}/etc/certs/ca_key.pem", "wb") as f:
                f.write(
                    ca_key.private_bytes(
                        encoding=serialization.Encoding.PEM,
                        format=serialization.PrivateFormat.TraditionalOpenSSL,
                        encryption_algorithm=serialization.NoEncryption(),
                    )
                )
            with open(f"{dom_dir}/etc/certs/ca_cert.pem", "wb") as f:
                f.write(ca_cert.public_bytes(serialization.Encoding.PEM))
            with open(f"{dom_dir}/etc/certs/{name}-cert", "wb") as f:
                f.write(cert.public_bytes(serialization.Encoding.PEM))
            with open(f"{dom_dir}/etc/certs/{name}-key", "wb") as f:
                f.write(
                    key.private_bytes(
                        encoding=serialization.Encoding.PEM,
                        format=serialization.PrivateFormat.TraditionalOpenSSL,
                        encryption_algorithm=serialization.NoEncryption(),
                    )
                )
        copy(
            self.build_configs
            / "debian-client"
            / "etc"
            / "certs"
            / "debian-client-cert",
            self.build_configs
            / "strongswan-gw"
            / "etc"
            / "certs"
            / "debian-client-cert",
        )
        return True

    def start(self):
        self.start_network()
        self.start_machine()

    def stop(self):
        self.__del__()

    def build_rootfs_dict(
        self,
    ):
        self.rootfs_dict = {}
        for name, _ in self.machines_xml.items():
            match name:
                case "http-server":
                    packages = {"install": ["apache2"]}
                    post_script = {}
                case "strongswan-gw":
                    packages = {
                        "install": [
                            "iptables",
                            "strongswan",
                            "charon-systemd",
                            "strongswan-swanctl",
                            "strongswan-charon",
                            "strongswan-libcharon",
                            "libstrongswan-standard-plugins",
                            "libstrongswan-extra-plugins",
                        ]
                    }
                    post_script = {
                        "post-files": [
                            "cp /etc/certs/ca_cert.pem /etc/swanctl/x509ca/",
                            "cp /etc/certs/ca_key.pem /etc/swanctl/private/",
                            f"cp /etc/certs/{name}-cert /etc/swanctl/x509/",
                            "cp /etc/certs/debian-client-cert /etc/swanctl/x509/",
                            f"cp /etc/certs/{name}-key /etc/swanctl/private/",
                            "ip r a ***********/24 via ******** dev eth0",
                        ],
                    }
                case "router":
                    packages = {"install": ["iptables", "dnsmasq"]}
                    post_script = {
                        "post-files": "chmod +x /sbin/inet-forward.sh && systemctl enable net-setup",
                        "post-packages": [
                            'echo "net.ipv4.ip_forward = 1" > /etc/sysctl.conf',
                        ],
                    }
                case "debian-client":
                    packages = {
                        "install": [
                            "iptables",
                            "strongswan",
                            "charon-systemd",
                            "strongswan-swanctl",
                            "strongswan-charon",
                            "strongswan-libcharon",
                            "libstrongswan-standard-plugins",
                            "libstrongswan-extra-plugins",
                        ]
                    }
                    post_script = {
                        "post-files": [
                            "cp /etc/certs/ca_cert.pem /etc/swanctl/x509ca/",
                            "cp /etc/certs/ca_key.pem /etc/swanctl/private/",
                            f"cp /etc/certs/{name}-cert /etc/swanctl/x509/",
                            f"cp /etc/certs/{name}-key /etc/swanctl/private/",
                            "ip r a ********/24 via ************* dev eth0",
                        ],
                    }
                case "client-ipsecdr":
                    packages = {
                        "install": [
                            "iptables",
                            "python3",
                            "python3-venv",
                            "python3-pip",
                            "python3-dev",
                            "build-essential",
                            "libssl-dev",
                            "libffi-dev",
                            "libbz2-dev",
                            "libreadline-dev",
                            "libsqlite3-dev",
                            "libz-dev",
                            "curl",
                            "git",
                        ]
                    }
                    post_script = {
                        "post-files": [
                            "cp /etc/certs/ca_cert.pem /etc/swanctl/x509ca/",
                            "cp /etc/certs/ca_key.pem /etc/swanctl/private/",
                            f"cp /etc/certs/{name}-cert /etc/swanctl/x509/",
                            f"cp /etc/certs/{name}-key /etc/swanctl/private/",
                            "ip r a ********/24 via ************* dev eth0",
                        ],
                    }
                case "server-ipsecdr":
                    packages = {
                        "install": [
                            "iptables",
                            "python3",
                            "python3-venv",
                            "python3-pip",
                            "python3-dev",
                            "build-essential",
                            "libssl-dev",
                            "libffi-dev",
                            "libbz2-dev",
                            "libreadline-dev",
                            "libsqlite3-dev",
                            "libz-dev",
                            "curl",
                            "git",
                        ]
                    }
                    post_script = {
                        "post-files": [
                            "cp /etc/certs/ca_cert.pem /etc/swanctl/x509ca/",
                            "cp /etc/certs/ca_key.pem /etc/swanctl/private/",
                            f"cp /etc/certs/{name}-cert /etc/swanctl/x509/",
                            "cp /etc/certs/debian-client-cert /etc/swanctl/x509/",
                            f"cp /etc/certs/{name}-key /etc/swanctl/private/",
                            "ip r a ***********/24 via ******** dev eth0",
                        ],
                    }
            self.rootfs_dict[name] = {
                "path": os.path.join(self.fs_path, name),
                "packages": packages,
                "overlay": os.path.join(self.build_configs, name),
                "post_script": post_script,
            }

    def __init__(
        self,
        logger,
        xml_confs_path: str,
        fs_path: str,
        build_configs: str,
        start: bool = False,
        build_fs: bool = False,
        ipsecdr: bool = False,
    ):
        self.logger = logger
        self.check_paths(paths=[xml_confs_path, fs_path, build_configs])
        self.build_configs = build_configs
        self.fs_path = fs_path

        # Libvirt
        self.xml_confs_path = xml_confs_path
        self.make_libvirt_configs(xml_confs_path=self.xml_confs_path)
        self.networks_xml, self.machines_xml = self.load_libvirt_configs(
            xml_confs_path
        )

        if ipsecdr:
            self.machines_xml.pop("debian-client")
            self.machines_xml.pop("strongswan-gw")

        super().__init__(
            logger=logger,
            hypervisor="lxc:///system",
            networks_xml=self.networks_xml,
            machines_xml=self.machines_xml,
            start=False,
        )

        self.build_rootfs_dict()
        self.make_pki()
        self.rootfs_builder = RootFsBuilder(
            logger=logger,
            template=os.path.join(
                self.build_configs,
                "base.yml",
            ),
            confs_dir=self.build_configs,
            rootfs_dict=self.rootfs_dict,
            build=False,
        )
        if build_fs:
            self.rootfs_builder.build()
        if start:
            self.start()
