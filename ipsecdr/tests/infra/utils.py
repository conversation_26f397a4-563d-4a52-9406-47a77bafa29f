from datetime import datetime, timedelta
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.primitives import hashes
from cryptography.x509.oid import NameOID
from cryptography import x509
import os
import subprocess as sp
from xml.etree import ElementTree


def chroot_exit(rootfs_dir: str) -> None:
    try:
        sp.run(["umount", "-R", f"{rootfs_dir}/proc"])
        sp.run(["umount", "-R", f"{rootfs_dir}/sys"])
        sp.run(["umount", "-R", f"{rootfs_dir}/dev"])
        sp.run(["umount", "-R", f"{rootfs_dir}/run"])
        print("Exited chroot environment properly.")
    except sp.CalledProcessError as e:
        print(f"Error exiting chroot environment: {e}")


def chroot_exec(
    rootfs_dir: str, commands: list, verbose: bool = False
) -> None:
    try:
        sp.run(
            ["mount", "-t", "proc", "/proc", f"{rootfs_dir}/proc"],
        )
        sp.run(["mount", "--rbind", "/dev", f"{rootfs_dir}/sys"])
        sp.run(["mount", "--rbind", "/dev", f"{rootfs_dir}/dev/"])
        sp.run(["mount", "--make-rslave", f"{rootfs_dir}/sys/"])
        sp.run(["mount", "--make-rslave", f"{rootfs_dir}/dev/"])
        sp.run(["mount", "--bind", "/run", f"{rootfs_dir}/run/"])
        sp.run(["mount", "--make-slave", f"{rootfs_dir}/run/"])
        sp.run(["rm", "-f", f"{rootfs_dir}/etc/resolv.conf"])
        sp.run(["cp", "/etc/resolv.conf", f"{rootfs_dir}/etc/"])
        chroot_cmd = ["chroot", f"{rootfs_dir}", "/bin/bash", "-c"]
        for cmd in commands:
            command = chroot_cmd + cmd
            print(command)
            if verbose:
                sp.run(command)
            else:
                sp.run(
                    command,
                    stdout=sp.DEVNULL,
                    stderr=sp.STDOUT,
                )
        chroot_exit(rootfs_dir)
        print("Custom commands executed successfully.")
    except sp.CalledProcessError as e:
        print(f"Error executing custom commands in chroot: {e}")


def enter_chroot(rootfs_dir: str) -> None:
    try:
        sp.run(
            ["mount", "-t", "proc", "/proc", f"{rootfs_dir}/proc"],
        )
        sp.run(["mount", "--rbind", "/dev", f"{rootfs_dir}/sys"])
        sp.run(["mount", "--rbind", "/dev", f"{rootfs_dir}/dev/"])
        sp.run(["mount", "--make-rslave", f"{rootfs_dir}/sys/"])
        sp.run(["mount", "--make-rslave", f"{rootfs_dir}/dev/"])
        chroot_cmd = ["chroot", f"{rootfs_dir}", "/bin/bash"]
        sp.run(chroot_cmd)
        chroot_exit(rootfs_dir)
        print("Custom commands executed successfully.")
    except sp.CalledProcessError as e:
        print(f"Error executing custom commands in chroot: {e}")


def load_xml_file(xml_file_path: str) -> dict:
    with open(xml_file_path, "r") as f:
        xml_content = f.read()
    if name := ElementTree.fromstring(xml_content).find("name").text:
        return {name: xml_content}
    else:
        print("Cannot find name of xml file wont continue")
        return {}


def load_configs(path) -> (dict, dict):
    networks = {}
    containers = {}
    for file in os.listdir(path):
        if "network" in file:
            networks.update(load_xml_file(path + "/" + file))
        elif "container" in file:
            containers.update(load_xml_file(path + "/" + file))
        else:
            print("Wont load anything other than network or container configs")
    return networks, containers


def generate_ca(ec_type):
    if not (ec_type == ec.BrainpoolP256R1 or ec_type == ec.SECP256R1):
        print("Only secp256 or bp256 used")
        return
    ca_key = ec.generate_private_key(ec_type(), default_backend())

    ca_subject = x509.Name(
        [
            x509.NameAttribute(NameOID.COUNTRY_NAME, "ZZ"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "DOKO"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "SOKO"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "KOK"),
            x509.NameAttribute(NameOID.COMMON_NAME, "ipsec-ca.lan"),
        ]
    )

    ca_cert = (
        x509.CertificateBuilder()
        .subject_name(ca_subject)
        .issuer_name(ca_subject)
        .public_key(ca_key.public_key())
        .serial_number(x509.random_serial_number())
        .not_valid_before(datetime.utcnow())
        .not_valid_after(datetime.utcnow() + timedelta(days=3650))
        .add_extension(
            x509.BasicConstraints(ca=True, path_length=None),
            critical=True,
        )
        .sign(ca_key, hashes.SHA256(), default_backend())
    )
    return ca_key, ca_cert


def generate_certificate(ec_type, ca_key, ca_cert, common_name, san_list=None):
    if not (ec_type == ec.BrainpoolP256R1 or ec_type == ec.SECP256R1):
        print("Only ecp256 or bp256 used")
        return
    key = ec.generate_private_key(ec_type(), default_backend())

    csr = (
        x509.CertificateSigningRequestBuilder()
        .subject_name(
            x509.Name(
                [
                    x509.NameAttribute(NameOID.COUNTRY_NAME, "ZZ"),
                    x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "DOKO"),
                    x509.NameAttribute(NameOID.LOCALITY_NAME, "SOKO"),
                    x509.NameAttribute(NameOID.ORGANIZATION_NAME, "KOKO"),
                    x509.NameAttribute(NameOID.COMMON_NAME, common_name),
                ]
            )
        )
        .sign(key, hashes.SHA256(), default_backend())
    )

    cert_builder = (
        x509.CertificateBuilder()
        .subject_name(csr.subject)
        .issuer_name(ca_cert.subject)
        .public_key(csr.public_key())
        .serial_number(x509.random_serial_number())
        .not_valid_before(datetime.utcnow())
        .not_valid_after(datetime.utcnow() + timedelta(days=365))
        .add_extension(
            x509.BasicConstraints(ca=False, path_length=None),
            critical=True,
        )
    )
    if san_list:
        san_extension = x509.SubjectAlternativeName(
            [x509.DNSName(san) for san in san_list]
        )
        cert_builder = cert_builder.add_extension(
            san_extension, critical=False
        )
    cert = cert_builder.sign(ca_key, hashes.SHA256(), default_backend())
    return key, cert
