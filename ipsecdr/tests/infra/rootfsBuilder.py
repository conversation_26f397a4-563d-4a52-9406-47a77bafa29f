import os
import multiprocessing
import yaml
import subprocess as sp


def load_yaml(file_path):
    with open(file_path, "r") as file:
        return yaml.safe_load(file)


def save_yaml(data, file_path):
    with open(file_path, "w") as file:
        yaml.safe_dump(data, file)


def add_package(yaml_data, package_name, action="install"):
    package_set = next(
        (
            pkg_set
            for pkg_set in yaml_data["packages"]["sets"]
            if pkg_set["action"] == action
        ),
        None,
    )
    if package_set:
        package_set["packages"].append(package_name)
    else:
        yaml_data["packages"]["sets"].append(
            {"action": action, "packages": [package_name]}
        )


def change_image_name(yaml_data, new_name):
    """Change the image: name field in the YAML data."""
    if "image" in yaml_data and "name" in yaml_data["image"]:
        yaml_data["image"]["name"] = new_name
    else:
        print("Invalid YAML format or missing 'image: name' field")
        return
    print(f"Changed image name to {new_name}")


def add_copy_generator(
    yaml_data, source_path, destination_path, mode=None, uid=None, gid=None
):
    """Add a copy generator to the YAML data."""
    copy_generator = {
        "path": destination_path,
        "generator": "copy",
        "source": source_path,
    }

    if mode:
        copy_generator["mode"] = mode
    if uid:
        copy_generator["uid"] = uid
    if gid:
        copy_generator["gid"] = gid

    if "files" in yaml_data:
        yaml_data["files"].append(copy_generator)
    else:
        yaml_data["files"] = [copy_generator]


def add_copy_generators_from_overlay(
    yaml_data, overlay_dir, container_root="/"
):
    """Recursively add copy generators for all files in the overlay directory."""
    for root, _, files in os.walk(overlay_dir):
        for file in files:
            source_path = os.path.join(root, file)
            relative_path = os.path.relpath(source_path, overlay_dir)
            destination_path = os.path.join(container_root, relative_path)
            add_copy_generator(yaml_data, source_path, destination_path)


def remove_package(yaml_data, package_name):
    package_set = next(
        (
            pkg_set
            for pkg_set in yaml_data["packages"]["sets"]
            if pkg_set["action"] == "remove"
        ),
        None,
    )
    if package_set:
        package_set["packages"].append(package_name)
    else:
        yaml_data["packages"]["sets"].append(
            {"action": "remove", "packages": [package_name]}
        )


def add_post_script_action(yaml_data, script, trigger="post-packages"):
    post_action = next(
        (
            action
            for action in yaml_data["actions"]
            if action["trigger"] == trigger
        ),
        None,
    )
    if post_action:
        post_action["action"] += f"\n{script}"
    else:
        yaml_data["actions"].append({"trigger": trigger, "action": script})


def run_distrobuilder(name_yml, dir):
    """Run distrobuilder build-dir using the specified YAML file and directory."""
    command = [
        "distrobuilder",
        "build-dir",
        "--with-post-files",
        name_yml,
        dir,
    ]
    result = sp.run(command, stdout=sp.PIPE, stderr=sp.PIPE)
    if result.returncode == 0:
        return True
    else:
        print(f"Failed to build rootfs for {name_yml} in {dir}")
        print(result.stderr.decode())


class RootFs:
    def __init__(self, conf=None, path=None):
        self.conf = conf
        self.path = path


class RootFsBuilder:
    def validate_rootfs_dict(self, rootfs_dict):
        """Validate the rootfs dictionary configuration."""
        for rootfs_name, config in rootfs_dict.items():
            if "path" not in config or not config["path"]:
                raise ValueError(
                    f"Error: 'path' is a mandatory field and must be defined for rootfs '{rootfs_name}'"
                )
            if "packages" not in config:
                config["packages"] = {}
            if "overlay" not in config:
                config["overlay"] = ""
            if "post_script" not in config:
                config["post_script"] = {}
            elif not isinstance(config["post_script"], dict):
                raise ValueError(
                    f"Error: 'post_script' must be a dictionary for rootfs '{rootfs_name}'"
                )

    def build(self):
        """Builds all rootfs in the rootfs_list."""
        with multiprocessing.Pool() as pool:
            pool.starmap(
                self.build_rootfs,
                [
                    (name, data["path"])
                    for name, data in self.rootfs_dict.items()
                ],
            )

    def build_rootfs(self, rootfs_name, output_dir):
        """Build the rootfs using distrobuilder."""
        if run_distrobuilder(
            os.path.join(self.confs_dir, f"{rootfs_name}.yml"), output_dir
        ):
            self.printI(
                f"Successfully built rootfs for {rootfs_name}\
                     in {output_dir}"
            )
        else:
            self.printE(
                f"Failed to build rootfs for {rootfs_name}\
                     in {output_dir}"
            )

    def handle_package(self, rootfs_name, packages: dict):
        """Handle package installation/removal for a rootfs."""
        for action, package_list in packages.items():
            for package in package_list:
                if action == "install":
                    add_package(self.template_yml, package, action="install")
                elif action == "remove":
                    remove_package(self.template_yml, package)

    def update_conf(self, rootfs_name):
        """Update the configuration for the rootfs."""
        rootfs_config = self.rootfs_dict[rootfs_name]

        if "packages" in rootfs_config:
            self.handle_package(rootfs_name, rootfs_config["packages"])

        if "overlay" in rootfs_config and rootfs_config["overlay"]:
            self.apply_overlay(rootfs_config["overlay"])

        if "post_script" in rootfs_config and rootfs_config["post_script"]:
            for trigger, scripts in rootfs_config["post_script"].items():
                if isinstance(scripts, list):
                    for script in scripts:
                        add_post_script_action(
                            self.template_yml,
                            f"#!/bin/sh\n{script}",
                            trigger=trigger,
                        )
                else:
                    add_post_script_action(
                        self.template_yml,
                        f"#!/bin/sh\n{scripts}",
                        trigger=trigger,
                    )

        hostname_script = f'#!/bin/sh\necho "{rootfs_name}" > /etc/hostname'
        add_post_script_action(
            self.template_yml, hostname_script, trigger="post-files"
        )
        save_yaml(
            self.template_yml,
            os.path.join(self.confs_dir, f"{rootfs_name}.yml"),
        )

    def apply_overlay(self, overlay_dir):
        """Apply overlay directory to the rootfs configuration."""
        add_copy_generators_from_overlay(self.template_yml, overlay_dir)

    def reload_template(self, template=None):
        if template is None:
            template = self.template
        if os.path.exists(template):
            self.template = template
            self.template_yml = load_yaml(self.template)
        else:
            raise FileNotFoundError

    def __init__(
        self,
        logger,
        template: str,
        confs_dir: str,
        rootfs_dict: dict,
        build: bool = False,
    ):
        self.logger = logger
        self.reload_template(template=template)
        if os.path.exists(confs_dir):
            self.confs_dir = confs_dir
        else:
            raise FileNotFoundError
        self.rootfs_dict = rootfs_dict
        self.validate_rootfs_dict(self.rootfs_dict)
        self.printI("Rebuilding distrobuilder configurations")
        for rootfs_name in self.rootfs_dict:
            self.update_conf(rootfs_name)
            self.reload_template()
        if build:
            self.printI(
                f"Starting build of {len(self.rootfs_dict)}\
                     filesystems could take a few minutes!"
            )
            self.build()

    def print(self, text: str = "", style: str = "default") -> None:
        self.logger.info(f"[{style}]{text}[/]")

    def printE(self, text: str = "") -> None:
        self.logger.error(f"[blink bold red]{text}[/]")

    def printW(self, text: str = "") -> None:
        self.logger.warning(f"[bold yellow]{text}[/]")

    def printI(self, text: str = "") -> None:
        self.logger.info(f"[green]{text}[/]")


if __name__ == "__main__":
    rootfs_dict = {
        "rootfs1": {
            "path": "test/rootfs1",
            "packages": {"install": ["curl", "vim"], "remove": ["nano"]},
            "overlay": "/home/<USER>/Desktop/Works/taf/ipsec_dr/test-infra/src/vpnEmulation/c/r1/",
            "post_script": {
                "post-files": 'echo "Post-script for rootfs1" > /etc/motd'
            },
        },
    }

    # Initialize and build rootfs
    rootfs_builder = RootFsBuilder(
        template="t/base.yml",
        confs_dir="t",
        rootfs_dict=rootfs_dict,
        build=True,
    )
