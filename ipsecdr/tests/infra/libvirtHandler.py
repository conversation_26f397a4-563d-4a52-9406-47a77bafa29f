import libvirt
from pathlib import Path
import xml.etree.ElementTree as ET

import rich.repr


@rich.repr.auto
class libvirtHandler:
    """
    libvirt handler to build labs
    Quckly setup test infrastructures
    """

    # Utils

    def print(self, text: str = "", style: str = "default") -> None:
        self.logger.info(f"[{style}]{text}[/]")

    def printE(self, text: str = "") -> None:
        self.logger.error(f"[blink bold red]{text}[/]")

    def printW(self, text: str = "") -> None:
        self.logger.warning(f"[bold yellow]{text}[/]")

    def printI(self, text: str = "") -> None:
        self.logger.info(f"[green]{text}[/]")

    # Libvirt related

    def _libvirt_lookup(self, type: str, name):
        try:
            if type == "network":
                return self.conn.networkLookupByName(name)
            elif type == "domain":
                return self.conn.lookupByName(name)
            else:
                self.printE(
                    f"Error type {type} is neither network or domain!",
                )
                raise ValueError
        except libvirt.libvirtError as e:
            self.printE(repr(e))
            self.printI("We continue execution")
            pass

    def _libvirt_define(self, type: str, name: str, xml: str):
        try:
            if type == "network":
                self.nets[name] = self.conn.networkDefineXML(xml)
            elif type == "domain":
                self.machines[name] = self.conn.defineXML(xml)
            else:
                self.printE(
                    f"Error type {type} is neither network or domain!",
                )
                raise ValueError
            self.printI(f"{type} {name} defined.")
            return
        except libvirt.libvirtError as e:
            self.printE(
                f"Unknown error while defining machine entity {name}",
            )
            self.printE(repr(e))
            raise e

    def _libvirt_create(self, type: str, name: str):
        try:
            if type == "network":
                return self.nets[name].create()
            elif type == "domain":
                return self.machines[name].create()
            else:
                self.printE(
                    f"Error type {type} is neither network or domain!",
                )
                raise ValueError
        except libvirt.libvirtError as e:
            self.printE(repr(e))
            raise e

    def _libvirt_destroy(self, instance):
        try:
            if instance.isActive():
                return instance.destroy()
            else:
                self.printW("Instance not active")
                return
        except libvirt.libvirtError as e:
            self.print(repr(e))
            raise e

    def _libvirt_undefine(self, instance):
        try:
            self._libvirt_destroy(instance)
            return instance.undefine()
        except libvirt.libvirtError as e:
            self.printE(repr(e))
            self.printW("Retrying one last time!")
            if self._libvirt_destroy(instance):
                return instance.undefine()
            raise e

    def create_networks(self, networks_xml: dict, start: bool) -> None:
        for name, xml in networks_xml.items():
            net = self._libvirt_lookup(type="network", name=name)
            if net:
                self.printI(
                    f"Network {name} undefined: "
                    + str(self._libvirt_undefine(net)),
                )
            self._libvirt_define(type="network", name=name, xml=xml)
            if start:
                self.nets[name].setAutostart(1)
                self._libvirt_create(type="network", name=name)

    def create_machines(self, machines_xml: dict, start: bool) -> None:
        REPOSITORY_ROOT = Path(__file__).resolve().parents[2]
        for name, xml in machines_xml.items():
            tree = ET.ElementTree(ET.fromstring(xml))
            root = tree.getroot()
            for filesystem in root.findall(".//devices/filesystem"):
                source = filesystem.find("source")
                if source is not None and "dir" in source.attrib:
                    source.attrib["dir"] = source.attrib["dir"].replace(
                        "PATH", str(REPOSITORY_ROOT)
                    )

            updated_xml = ET.tostring(root, encoding="unicode")
            dom = self._libvirt_lookup(type="domain", name=name)
            if dom:
                self.printI(
                    f"Machine {name} undefined: "
                    + str(self._libvirt_undefine(dom)),
                )
            self._libvirt_define(type="domain", name=name, xml=updated_xml)
            if start:
                self._libvirt_create(type="domain", name=name)

    def delete_networks(self) -> int:
        try:
            for name, network in self.nets.items():
                net = self._libvirt_lookup(type="network", name=name)
                if net:
                    status = self._libvirt_undefine(net)
                    self.printI(
                        f"Network {name} undefined: " + str(status),
                    )
                    return status
            return True
        except Exception as e:
            self.logger.warning(e)
            return False

    def delete_machines(self) -> bool:
        try:
            for name, dom in self.machines.items():
                dom = self._libvirt_lookup(type="domain", name=name)
                if dom:
                    status = self._libvirt_undefine(dom)
                    self.printI(
                        f"Machine {name} undefined: " + str(status),
                    )
            return True
        except Exception as e:
            self.logger.warning(e)
            return False

    def start_network(self, name: str = "") -> None:
        if name and name in self.nets.keys():
            if self.nets[name].isActive():
                self.printI(f"Network {name} already active")
                return
            self._libvirt_create(type="network", name=name)
        for name, net in self.nets.items():
            if net.isActive():
                self.printI(f"Network {name} already active")
                return
            self._libvirt_create(type="network", name=name)

    def start_machine(self, name: str = "") -> None:
        if name and name in self.machines.keys():
            if self.nets[name].isActive():
                self.printI(f"Machine {name} already active")
                return
            self._libvirt_create(type="domain", name=name)
        for name, net in self.machines.items():
            if net.isActive():
                self.printI(f"Machine {name} already active")
                return
            self._libvirt_create(type="domain", name=name)

    def __init__(
        self,
        logger,
        hypervisor: str,
        networks_xml: dict,
        machines_xml: dict,
        start: bool = False,
    ):
        self.logger = logger
        self.conn = libvirt.open(hypervisor)

        self.nets = {}
        self.machines = {}

        self.networks_conf = networks_xml
        self.machines_conf = machines_xml
        self.create_networks(self.networks_conf, start=start)
        self.create_machines(self.machines_conf, start=start)

    def __del__(self) -> bool:
        try:
            if not self.delete_machines():
                self.printE("Error in deletion of domains")
            self.delete_networks()
            return True
        except Exception as e:
            self.printE(f"Error in deletion {repr(e)}")
            return False
