from pathlib import Path
from tests.infra.ipsecEmulation import IpsecInfraTester
from tests.support.logger import logger


def main():
    REPOSITORY_ROOT = Path(__file__).resolve().parents[1]
    conf_path = REPOSITORY_ROOT / "configs" / "test-infra" / "libvirt"
    fs_path = REPOSITORY_ROOT / "configs" / "test-infra" / "filesystems"
    build_configs = REPOSITORY_ROOT / "configs" / "test-infra" / "fs-config"
    # tester not used so _
    _ = IpsecInfraTester(
        logger=logger,
        xml_confs_path=conf_path,
        fs_path=fs_path,
        build_configs=build_configs,
        start=True,
        build_fs=True,
    )
    input("PAUSE")


if __name__ == "__main__":
    main()
