import sys
import pytest
from unittest import mock
from unittest.mock import patch, MagicMock
from tests.support.logger import logger

# Import the main function from the entrypoint script
from ipsecdr.ipsecdr import main


def test_entrypoint_with_valid_args(monkeypatch):
    """
    Test that the entrypoint correctly parses arguments and launches the Orchestrator.
    """
    # Simulate command-line arguments
    test_args = [
        "ipsecdr.py",
        "--config",
        "test_config.ini",
        "--client",
        "--tests",
        "T.01_VALID_IPSEC",
        "T.02_ALGO_DR",
        "--max-testers",
        "2",
        "--max-checkers",
        "2",
        "--verbose",
    ]

    # Use monkeypatch to set sys.argv
    monkeypatch.setattr(sys, "argv", test_args)

    # Mock the IPsecDRConfigParser and Orchestrator classes
    with (
        patch("ipsecdr.ipsecdr.IPsecDRConfigParser") as MockConfigParser,
        patch("ipsecdr.ipsecdr.TestConfiguration") as MockTestConfig,
        patch("ipsecdr.ipsecdr.Orchestrator") as MockOrchestrator,
    ):

        # Create a mock configuration and orchestrator instance
        mock_config = MagicMock()
        MockConfigParser.return_value = mock_config

        mock_orchestrator_instance = MagicMock()
        MockOrchestrator.return_value = mock_orchestrator_instance

        mock_test_config = MagicMock()
        MockTestConfig.return_value = mock_test_config
        main()

        MockConfigParser.assert_called_once_with("test_config.ini")
        MockTestConfig.assert_called_once()
        # Assertions to check that the Orchestrator was instantiated with correct parameters
        MockOrchestrator.assert_called_once_with(
            config=mock_test_config,
            test_list=["T.01_VALID_IPSEC", "T.02_ALGO_DR"],
            max_testers=2,
            max_checkers=2,
            mode="client",
        )

        # Assert that the orchestrator's run method was called
        mock_orchestrator_instance.run.assert_called_once()


def test_entrypoint_missing_tests(monkeypatch, capsys, caplog):
    """
    Test that the entrypoint exits when no tests are specified.
    """
    test_args = ["ipsecdr.py", "--config", "test_config.ini", "--client"]
    monkeypatch.setattr(sys, "argv", test_args)

    # Mock sys.exit to prevent the test from exiting
    with patch("sys.exit") as mock_exit:
        main()
        # Assert that sys.exit was called with code 1
        mock_exit.assert_called_once_with(1)
    logger.warning(f"{caplog.text}")
    assert ("No tests specified") in caplog.text


def test_entrypoint_with_test_list_file(monkeypatch):
    """
    Test that the entrypoint correctly reads the test list from a file.
    """
    test_args = [
        "ipsecdr.py",
        "--config",
        "test_config.ini",
        "--server",
        "--test-list",
        "test_list.txt",
    ]

    # Mock the content of the test list file
    test_list_content = "T.01_VALID_IPSEC\nT.03_SA_TRANSFORM\n"
    monkeypatch.setattr(sys, "argv", test_args)
    with (
        patch(
            "builtins.open", mock.mock_open(read_data=test_list_content)
        ) as mock_file,
        patch("ipsecdr.ipsecdr.IPsecDRConfigParser") as MockConfigParser,
        patch("ipsecdr.ipsecdr.TestConfiguration") as MockTestConfig,
        patch("ipsecdr.ipsecdr.Orchestrator") as MockOrchestrator,
    ):
        # Create mock configuration and orchestrator instances
        mock_config = MagicMock()
        MockConfigParser.return_value = mock_config

        mock_orchestrator_instance = MagicMock()
        MockOrchestrator.return_value = mock_orchestrator_instance

        mock_test_config = MagicMock()
        MockTestConfig.return_value = mock_test_config

        main()

        mock_file.assert_called_once_with("test_list.txt", "r")

        MockOrchestrator.assert_called_once_with(
            config=mock_test_config,
            test_list=["T.01_VALID_IPSEC", "T.03_SA_TRANSFORM"],
            max_testers=1,
            max_checkers=1,
            mode="server",
        )
        mock_orchestrator_instance.run.assert_called_once()


def test_entrypoint_invalid_args(monkeypatch, capsys):
    """
    Test that the entrypoint handles invalid arguments gracefully.
    """
    test_args = [
        "ipsecdr.py",
        "--config",
        "test_config.ini",
        "--client",
        "--unknown-arg",
    ]
    monkeypatch.setattr(sys, "argv", test_args)
    with pytest.raises(SystemExit) as excinfo:
        main()

    captured = capsys.readouterr()
    assert "error: unrecognized arguments: --unknown-arg" in captured.err
    # Assert that the exit code is 2 (standard for argparse errors)
    assert excinfo.value.code == 2


def test_entrypoint_missing_config(monkeypatch, capsys):
    """
    Test that the entrypoint exits when the config file is missing.
    """
    test_args = ["ipsecdr.py", "--client", "--tests", "T.01_VALID_IPSEC"]

    monkeypatch.setattr(sys, "argv", test_args)
    with pytest.raises(SystemExit) as excinfo:
        main()

    captured = capsys.readouterr()
    assert (
        "error: the following arguments are required: -c/--config"
        in captured.err
    )
    assert excinfo.value.code == 2
