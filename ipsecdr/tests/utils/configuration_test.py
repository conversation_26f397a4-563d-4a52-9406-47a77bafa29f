import pytest
import os
from ipsecdr.utils.configuration import (
    IPsecDRConfigParser,
)
from tests.support.helpers import write_config_file
from tests.support.logger import logger


def test_load_config_file():
    config_content = """
[ARGS]
timeout = 30
verbose = True
logfile = /var/log/ipsecdr.log
pcap_path = /var/pcap

[IF]
interface = eth0
ipsec_src = ***********
ipsec_dst = ***********
ip_src = ***********
ip_dst = ***********
port_src = 500
port_dst = 500

[CA]
ca_root = /etc/ipsec.d/cacerts/ca.pem
pub_key = /etc/ipsec.d/certs/server-cert.pem
prv_key = /etc/ipsec.d/private/server-key.pem
trust_chain = /etc/ipsec.d/cacerts/ca.pem

[AUTH]
auth_method = ECDSA_SECP256R1_SHA256
"""
    config_file = write_config_file(config_content)
    # Mock os.path.isfile to always return True for testing
    original_isfile = os.path.isfile
    os.path.isfile = lambda path: True

    try:
        parser = IPsecDRConfigParser(config_file)
        assert parser.global_config.timeout == 30
        assert parser.global_config.verbose is True
        assert parser.global_config.logfile == "/var/log/ipsecdr.log"
        assert parser.network.interface == "eth0"
        assert str(parser.network.ipsec_src) == "***********"
        assert parser.pki.ca_root == "/etc/ipsec.d/cacerts/ca.pem"
        assert parser.ipsec.auth.auth_method == 9  # Converted to ID
    finally:
        os.remove(config_file)
        os.path.isfile = original_isfile


def test_overlay_config():
    base_config_content = """
[ARGS]
timeout = 30
verbose = False

[IF]
interface = eth0
"""

    overlay_config_content = """
[ARGS]
verbose = True
logfile = /var/log/ipsecdr_overlay.log

[IF]
ip_src = ********
"""

    base_config_file = write_config_file(base_config_content)
    overlay_config_file = write_config_file(overlay_config_content)
    # Mock os.path.isfile to always return True for testing
    original_isfile = os.path.isfile
    os.path.isfile = lambda path: True

    try:
        parser = IPsecDRConfigParser(base_config_file)
        parser.overlay_config([overlay_config_file])
        logger.warning(
            f"{parser.global_config.verbose}  - {type(parser.global_config.verbose)}"
        )
        assert parser.global_config.timeout == 30
        assert parser.global_config.verbose is True
        assert parser.global_config.logfile == "/var/log/ipsecdr_overlay.log"
        assert parser.network.interface == "eth0"
        assert str(parser.network.ip_src) == "********"
    finally:
        os.remove(base_config_file)
        os.remove(overlay_config_file)
        os.path.isfile = original_isfile


def test_cli_args_override():
    config_content = """
[ARGS]
timeout = 30
verbose = False

[IF]
interface = eth0
"""

    config_file = write_config_file(config_content)
    # Mock os.path.isfile to always return True for testing
    original_isfile = os.path.isfile
    os.path.isfile = lambda path: True

    cli_args = {
        "ARGS.verbose": True,
        "ARGS.timeout": 60,
        "IF.interface": "eth1",
        "IF.port_src": 4500,
        "CA.ca_root": "/etc/ipsec.d/cacerts/cli_ca.pem",
    }

    try:
        parser = IPsecDRConfigParser(config_file, cli_args=cli_args)
        assert parser.global_config.timeout == 60
        assert parser.global_config.verbose is True
        assert parser.network.interface == "eth1"
        assert parser.network.port_src == 4500
        assert parser.pki.ca_root == "/etc/ipsec.d/cacerts/cli_ca.pem"
    finally:
        os.remove(config_file)
        os.path.isfile = original_isfile


def test_invalid_config():
    invalid_config_content = """
[ARGS]
timeout = -10

[IF]
port_src = 70000
"""

    config_file = write_config_file(invalid_config_content)
    # Mock os.path.isfile to always return True for testing
    original_isfile = os.path.isfile
    os.path.isfile = lambda path: True

    with pytest.raises(ValueError) as excinfo:
        _ = IPsecDRConfigParser(config_file)  # parser not used so _
    error_message = str(excinfo.value)
    assert ("Timeout must be positive") in error_message or (
        "Port must be between 0 and 65535"
    ) in error_message

    os.remove(config_file)
    os.path.isfile = original_isfile


def test_default_values():
    config_content = """
[ARGS]
pcap_path = /custom/pcap
"""

    config_file = write_config_file(config_content)
    # Mock os.path.isfile to always return True for testing
    original_isfile = os.path.isfile
    os.path.isfile = lambda path: True

    try:
        parser = IPsecDRConfigParser(config_file)
        # Check that unspecified values are set to defaults
        assert parser.global_config.timeout == 15  # Default value
        assert parser.global_config.pcap_path == "/custom/pcap"
        assert parser.global_config.verbose is False  # Default value
    finally:
        os.remove(config_file)
        os.path.isfile = original_isfile


# TODO add test_missing_required_values
