import io
import pytest
from pathlib import Path
from unittest.mock import patch
from ipsecdr.engine.orchestration import Orchestrator
from ipsecdr.engine.scenario import TestConfiguration
from ipsecdr.utils.configuration import IPsecDRConfigParser

REPOSITORY_ROOT = Path(__file__).resolve().parents[1]


@pytest.mark.real_world
def test_valid_ipsec_client(ipsec_infrastructure):
    """
    Test that simulates a normal IPsec tunnel as a client using the scenario T.00_VALID_IPSEC.
    """

    # Mock configuration content for IPsecDR
    mock_config_content = f"""
[ARGS]
timeout = 15
verbose = True
logfile = /tmp/ipsec.log
# Chemin du binaire tcpreplay
tcpreplay = /usr/bin/tcpreplay
# Dossier de stockage pour les fichiers pcap
pcap_path = /tmp/pcap

[IF]
# Interface d'émission/réception
interface = br-ext
# IP du chiffreur émission
ipsec_src = ***********
# IP du chiffreur distant
ipsec_dst = ********
# IP d'émission inital
ip_src = ***********
# IP de destination final
ip_dst = *********
# Port d'émission
port_src = 4500
# Port de destination
port_dst = 4500

[NAT_T]
nat_t = True
nat_port_src = 4500
nat_port_dst = 4500


[IKE_SA]
encr = ENCR_AES_GCM_16
encr_size = 256
prf = PRF_HMAC_SHA2_256
integ = AUTH_HMAC_SHA2_256_128
groupdesc = 256randECPgr


[AUTH]
auth_method = ECDSA_SECP256R1_SHA256


[CHILD_SA]

encr = ENCR_AES_GCM_16
encr_size = 256
integ = AUTH_HMAC_SHA2_256_128
groupdesc = 256randECPgr
esn = ESN

[CA]
ca_root = {REPOSITORY_ROOT}/configs/test-infra/fs-config/debian-client/etc/certs/ca_cert.pem
pub_key = {REPOSITORY_ROOT}/configs/test-infra/fs-config/debian-client/etc/certs/debian-client-cert
prv_key = {REPOSITORY_ROOT}/configs/test-infra/fs-config/debian-client/etc/certs/debian-client-key
trust_chain = %(pub_key)s

[TS]
tsi_ip_range   = ***********-************
tsi_port_range = 0-65535
tsr_ip_range   = 10.0.0.0-**********
tsr_port_range = 0-65535

[ID]
idi_type = ID_FQDN
idi_data = debian-client.lan
idr_type = ID_FQDN
idr_data = strongswan-gw.lan

[TEST_SP]
ip_dst_interdit = ***********
    """

    test_config_file = io.StringIO(mock_config_content)

    with patch("builtins.open", return_value=test_config_file):
        # Initialize and parse configuration with IPsecDRConfigParser
        config_parser = IPsecDRConfigParser("dummy_path.ini")

    config = TestConfiguration(
        global_config=config_parser.global_config,
        network_config=config_parser.network,
        pki_config=config_parser.pki,
        ipsec_config=config_parser.ipsec,
    )
    test_scenario_name = "T.00_VALID_IPSEC"
    test_scenario_file = (
        REPOSITORY_ROOT
        / "ipsecdr"
        / "engine"
        / "tests"
        / f"{test_scenario_name}.yml"
    )

    if not test_scenario_file.exists():
        pytest.fail(f"Test scenario file {test_scenario_file} does not exist.")

    # Build the test list with the scenario name
    test_list = [test_scenario_name]

    # Initialize the Orchestrator
    orchestrator = Orchestrator(
        config=config,
        test_list=test_list,
        max_testers=1,
        max_checkers=1,
        mode="client",  # Run in client mode
    )

    # Run the Orchestrator
    orchestrator.run()

    # Access the test results from the orchestrator's test pool
    test_results = orchestrator.test_pool

    # Validate the results
    test_data = test_results.get(1)  # Test ID is 1
    if test_data is None:
        pytest.fail("Test data not found in orchestrator's test pool.")

    outcome = test_data.get("outcome")
    if outcome is None:
        pytest.fail("Test outcome not available.")

    result = outcome.get("result")
    if result != "Passed":
        reason = outcome.get("reason", "No reason provided.")
        pytest.fail(f"Test {test_scenario_name} failed: {reason}")
    else:
        print(f"Test {test_scenario_name} passed successfully.")
