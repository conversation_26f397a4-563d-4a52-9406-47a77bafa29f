# IPsec Evaluator

A modern, professional IPsec compliance testing tool designed for ANSSI IPsecDR corpus assessment and verification.

## Overview

IPsec Evaluator is a complete rewrite of the original ipsecdr tool, featuring:

- **Modern Architecture**: Built with Python 3.12+, async/await patterns, and type hints
- **Enhanced Hook System**: Advanced callback capabilities for packet-level and exchange-level monitoring
- **Container-Based Infrastructure**: Uses Incus containers instead of libvirt for better isolation and management
- **Infrastructure as Code**: OpenTofu deployment for reproducible test environments
- **Professional Testing Framework**: Comprehensive orchestrator/tester/checker pattern with metadata collection

## Key Features

### Advanced Hook System
- **Packet Number Callbacks**: Trigger actions on specific packet numbers
- **Exchange Step Callbacks**: Hook into INIT, AUTH, and CHILD_SA exchanges
- **Universal Packet Callbacks**: Monitor every packet in the communication flow
- **Metadata Collection**: Comprehensive data gathering for analysis and reporting

### Modern Infrastructure
- **Incus Containers**: Lightweight, secure container-based testing environment
- **OpenTofu Deployment**: Infrastructure as Code for consistent deployments
- **StrongSwan Integration**: Automated deployment and configuration of IPsec endpoints

### Professional Architecture
- **Orchestrator**: Manages test execution and infrastructure deployment
- **Testers**: Deploy and configure initiator/responder pairs with custom hooks
- **Checkers**: Analyze packets and metadata to assess ANSSI compliance
- **Reporters**: Generate comprehensive compliance reports

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Orchestrator  │────│     Tester      │────│     Checker     │
│                 │    │                 │    │                 │
│ • Test Planning │    │ • Deploy Infra  │    │ • Packet Analysis│
│ • Resource Mgmt │    │ • Configure     │    │ • Compliance    │
│ • Report Gen    │    │ • Execute Hooks │    │ • Metadata Eval │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Infrastructure │
                    │                 │
                    │ • Incus Mgmt    │
                    │ • OpenTofu      │
                    │ • StrongSwan    │
                    └─────────────────┘
```

## Installation

### Prerequisites
- Python 3.12+
- Poetry
- Incus
- OpenTofu

### Setup
```bash
cd ipsec-evaluator
poetry install
poetry install --with dev,infra  # Include development and infrastructure tools
```

### Development Setup
```bash
poetry install --with dev
poetry run pre-commit install
```

## Usage

### Basic Test Execution
```bash
# Run a specific test
poetry run ipsec-evaluator test --scenario T.00_VALID_IPSEC

# Run multiple tests
poetry run ipsec-evaluator test --scenarios T.01_ALGO_DR T.02_COMPLIANCE

# Run with custom configuration
poetry run ipsec-evaluator test --config custom-config.yaml --scenario T.00_VALID_IPSEC
```

### Infrastructure Management
```bash
# Deploy test infrastructure
poetry run ipsec-evaluator infra deploy

# Clean up infrastructure
poetry run ipsec-evaluator infra cleanup

# Status check
poetry run ipsec-evaluator infra status
```

## Configuration

Test scenarios are defined in YAML files that specify:
- Initiator and responder configurations
- Hook definitions and callbacks
- Compliance requirements
- Expected behaviors

Example test scenario:
```yaml
name: T.00_VALID_IPSEC
description: "Basic IPsec compliance validation"
test_scenarios:
  initiator:
    - valid_client:
        mode: strict
        hooks:
          packet_callbacks:
            - packet: 1
              exchange: INIT
              callback: validate_init_proposal
          exchange_callbacks:
            - exchange: AUTH
              callback: verify_authentication
        packets:
          - number: 1
            exchange: INIT
          - number: 2
            exchange: AUTH
  responder:
    - valid_server:
        mode: strict
        hooks:
          universal_callback: log_all_packets
```

## Development

### Project Structure
```
src/ipsec_evaluator/
├── core/                 # Core IPsec implementation
│   ├── crypto/          # Cryptographic operations
│   ├── ikev2/           # IKEv2 protocol implementation
│   └── esp/             # ESP implementation
├── engine/              # Test execution engine
│   ├── orchestrator.py  # Test orchestration
│   ├── tester.py        # Test execution
│   ├── checker.py       # Compliance checking
│   └── hooks/           # Hook system
├── infrastructure/      # Infrastructure management
│   ├── incus/           # Incus container management
│   ├── opentofu/        # Infrastructure as Code
│   └── strongswan/      # StrongSwan configuration
├── models/              # Data models and schemas
├── utils/               # Utilities and helpers
└── cli/                 # Command-line interface
```

### Testing
```bash
# Run all tests
poetry run pytest

# Run specific test categories
poetry run pytest -m unit
poetry run pytest -m integration
poetry run pytest -m e2e

# Run with coverage
poetry run pytest --cov=src --cov-report=html
```

### Code Quality
```bash
# Format code
poetry run black src tests
poetry run isort src tests

# Lint code
poetry run flake8 src tests
poetry run mypy src

# Run pre-commit hooks
poetry run pre-commit run --all-files
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Ensure code quality checks pass
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
