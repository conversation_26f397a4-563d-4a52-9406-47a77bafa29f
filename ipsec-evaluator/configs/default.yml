global:
  timeout: 30
  verbose: false
  log_level: INFO
  log_file: null
  max_concurrent_tests: 5

network:
  interface: eth0
  initiator_ip: "************"
  responder_ip: "************"
  ike_port: 500
  nat_t_port: 4500
  nat_traversal: true
  test_network: "*************/24"

crypto:
  # IKE SA algorithms
  ike_encryption: ["AES_GCM_16"]
  ike_integrity: ["HMAC_SHA2_256"]
  ike_prf: ["PRF_HMAC_SHA2_256"]
  ike_dh_groups: [19]  # ECP-256
  
  # Child SA algorithms
  esp_encryption: ["AES_GCM_16"]
  esp_integrity: ["HMAC_SHA2_256"]
  
  # Key sizes
  encryption_key_sizes: [256]
  
  # Certificate configuration
  ca_cert_path: "configs/certs/ca.pem"
  cert_path: "configs/certs/cert.pem"
  key_path: "configs/certs/key.pem"

infrastructure:
  # Incus configuration
  incus_remote: "local"
  incus_project: "default"
  
  # Container configuration
  base_image: "ubuntu:22.04"
  container_prefix: "ipsec-eval"
  
  # Network configuration
  bridge_name: "ipsec-br0"
  network_cidr: "**********/24"
  
  # OpenTofu configuration
  tofu_workspace: "ipsec-evaluator"
  tofu_state_path: null
  
  # StrongSwan configuration
  strongswan_version: "5.9.8"
  strongswan_config_template: null

# Test-specific configuration
test_data_dir: "./test_data"
results_dir: "./results"
