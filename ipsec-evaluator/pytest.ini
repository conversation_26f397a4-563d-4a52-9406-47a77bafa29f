[tool:pytest]
# Pytest configuration for ipsec-evaluator
minversion = 6.0
addopts =
    -ra
    --strict-markers
    --strict-config
    -p no:warnings

testpaths = tests

# Test markers
markers =
    unit: Unit tests
    integration: Integration tests
    compliance: Compliance validation tests
    anssi: ANSSI compliance tests
    crypto: Cryptographic tests
    performance: Performance tests
    real_world: Real-world infrastructure tests
    slow: Slow running tests

# Asyncio configuration
asyncio_mode = auto

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Test discovery
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Coverage configuration
[coverage:run]
source = src/ipsec_evaluator
omit =
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */virtualenv/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod
