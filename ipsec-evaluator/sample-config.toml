[global_config]
max_concurrent_tests = 2
test_timeout = 300
results_dir = "./anssi_results"
verbose = true
log_level = "DEBUG"

[network]
initiator_ip = "************"
responder_ip = "************"
ike_port = 500
nat_traversal = false
interface = "eth0"

[crypto]
ike_encryption = [ "AES_GCM_16", "AES_CTR",]
ike_integrity = [ "HMAC_SHA2_256", "HMAC_SHA2_384",]
ike_prf = [ "PRF_HMAC_SHA2_256", "PRF_HMAC_SHA2_384",]
ike_dh_groups = [ 19, 28, 20, 29,]
esp_encryption = [ "AES_GCM_16",]
esp_integrity = [ "HMAC_SHA2_256",]
encryption_key_sizes = [ 256, 384,]

[pki]
ca_cert_path = "/etc/ipsec.d/cacerts/ca.pem"
initiator_cert_path = "/etc/ipsec.d/certs/initiator.pem"
initiator_key_path = "/etc/ipsec.d/private/initiator.pem"
responder_cert_path = "/etc/ipsec.d/certs/responder.pem"
responder_key_path = "/etc/ipsec.d/private/responder.pem"
verify_certificates = true
certificate_validation = true
