"""
Enhanced test configuration and fixtures for ipsec-evaluator.

This module provides comprehensive test fixtures and configuration based on
the ipsecdr test structure, adapted for the enhanced ipsec-evaluator architecture.
"""

import pytest
import secrets
import random
import asyncio
from pathlib import Path
from typing import Dict, List, Any
from unittest.mock import patch, MagicMock

from ipsecator.models.config import (
    TestConfiguration,
    GlobalConfig,
    NetworkConfig,
    CryptoConfig,
    PKIConfig
)
from ipsecator.models.scenario import TestScenario
from ipsecator.models.base import TestMode, ComplianceLevel
from ipsecator.core.ikev2 import IKEv2Protocol, IKEv2Role, IKEv2HookManager
from ipsecator.core.esp import ESPProtocol, ESPMode
from ipsecator.core.crypto.dh import DHManager
from ipsecator.engine.orchestrator import Orchestrator
from ipsecator.engine.tester import Tester
from ipsecator.engine.checker import Checker, ComplianceStandard
from tests.support.helpers import (
    ensure_test_certificates_exist,
    load_test_certificates,
    create_test_scenario,
    generate_test_data
)

# Repository root for test files
REPOSITORY_ROOT = Path(__file__).resolve().parents[1]


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_certificates():
    """Fixture to provide test certificate paths."""
    ensure_test_certificates_exist()
    
    certs_dir = REPOSITORY_ROOT / "tests" / "support" / "certs"
    return {
        "ca_cert": certs_dir / "ca.pem",
        "initiator_cert": certs_dir / "initiator.pem",
        "initiator_key": certs_dir / "initiator_key.pem",
        "responder_cert": certs_dir / "responder.pem",
        "responder_key": certs_dir / "responder_key.pem"
    }


@pytest.fixture
def basic_crypto_config():
    """Fixture providing basic ANSSI-compliant crypto configuration."""
    return CryptoConfig(
        ike_encryption=["AES_GCM_16"],
        ike_integrity=["HMAC_SHA2_256"],
        ike_prf=["PRF_HMAC_SHA2_256"],
        ike_dh_groups=[19, 28],  # secp256r1 and brainpoolP256r1
        esp_encryption=["AES_GCM_16"],
        esp_integrity=["HMAC_SHA2_256"],
        encryption_key_sizes=[256]
    )


@pytest.fixture
def enhanced_crypto_config():
    """Fixture providing enhanced crypto configuration with multiple options."""
    return CryptoConfig(
        ike_encryption=["AES_GCM_16", "AES_CTR", "CHACHA20_POLY1305"],
        ike_integrity=["HMAC_SHA2_256", "HMAC_SHA2_384", "HMAC_SHA2_512"],
        ike_prf=["PRF_HMAC_SHA2_256", "PRF_HMAC_SHA2_384"],
        ike_dh_groups=[19, 20, 21, 28, 29, 30],  # Multiple secp and brainpool
        esp_encryption=["AES_GCM_16", "AES_CTR"],
        esp_integrity=["HMAC_SHA2_256", "HMAC_SHA2_384"],
        encryption_key_sizes=[256, 384]
    )


@pytest.fixture
def network_config():
    """Fixture providing network configuration for tests."""
    return NetworkConfig(
        initiator_ip="************",
        responder_ip="************",
        ike_port=500,
        nat_traversal=False,
        interface="eth0"
    )


@pytest.fixture
def pki_config(test_certificates):
    """Fixture providing PKI configuration."""
    return PKIConfig(
        ca_cert_path=str(test_certificates["ca_cert"]),
        initiator_cert_path=str(test_certificates["initiator_cert"]),
        initiator_key_path=str(test_certificates["initiator_key"]),
        responder_cert_path=str(test_certificates["responder_cert"]),
        responder_key_path=str(test_certificates["responder_key"])
    )


@pytest.fixture
def global_config():
    """Fixture providing global test configuration."""
    return GlobalConfig(
        max_concurrent_tests=2,
        test_timeout=300,
        results_dir=str(REPOSITORY_ROOT / "test_results"),
        verbose=True,
        log_level="DEBUG"
    )


@pytest.fixture
def test_configuration(global_config, network_config, basic_crypto_config, pki_config):
    """Fixture providing complete test configuration."""
    return TestConfiguration(
        global_config=global_config,
        network=network_config,
        crypto=basic_crypto_config,
        pki=pki_config
    )


@pytest.fixture
def enhanced_test_configuration(global_config, network_config, enhanced_crypto_config, pki_config):
    """Fixture providing enhanced test configuration with multiple crypto options."""
    return TestConfiguration(
        global_config=global_config,
        network=network_config,
        crypto=enhanced_crypto_config,
        pki=pki_config
    )


@pytest.fixture
def basic_test_scenario():
    """Fixture providing a basic test scenario."""
    return TestScenario(
        name="basic_ipsec_test",
        description="Basic IPsec compliance test",
        version="1.0",
        tags=["basic", "compliance"],
        metadata={
            "author": "ipsec-evaluator",
            "category": "basic_compliance"
        }
    )


@pytest.fixture
def anssi_test_scenario():
    """Fixture providing an ANSSI compliance test scenario."""
    return TestScenario(
        name="anssi_compliance_test",
        description="ANSSI compliance validation test",
        version="1.0",
        tags=["anssi", "compliance", "french"],
        metadata={
            "author": "ipsec-evaluator",
            "category": "anssi_compliance",
            "standard": "ANSSI"
        }
    )


@pytest.fixture
def dh_manager():
    """Fixture providing enhanced DH manager."""
    return DHManager()


@pytest.fixture
def hook_manager():
    """Fixture providing IKEv2 hook manager."""
    return IKEv2HookManager()


@pytest.fixture
def ikev2_protocol_initiator(test_configuration, hook_manager):
    """Fixture providing IKEv2 protocol in initiator mode."""
    return IKEv2Protocol(
        role=IKEv2Role.INITIATOR,
        local_ip=test_configuration.network.initiator_ip,
        remote_ip=test_configuration.network.responder_ip,
        local_port=test_configuration.network.ike_port,
        remote_port=test_configuration.network.ike_port,
        hook_manager=hook_manager
    )


@pytest.fixture
def ikev2_protocol_responder(test_configuration, hook_manager):
    """Fixture providing IKEv2 protocol in responder mode."""
    return IKEv2Protocol(
        role=IKEv2Role.RESPONDER,
        local_ip=test_configuration.network.responder_ip,
        remote_ip=test_configuration.network.initiator_ip,
        local_port=test_configuration.network.ike_port,
        remote_port=test_configuration.network.ike_port,
        hook_manager=hook_manager
    )


@pytest.fixture
def esp_protocol():
    """Fixture providing ESP protocol."""
    return ESPProtocol(
        mode=ESPMode.TUNNEL,
        enable_anti_replay=True
    )


@pytest.fixture
def enhanced_tester_initiator(test_configuration, basic_test_scenario, hook_manager):
    """Fixture providing enhanced tester in initiator mode."""
    return Tester(
        test_id=f"test-{secrets.token_hex(4)}",
        config=test_configuration,
        scenario=basic_test_scenario,
        mode=TestMode.INITIATOR,
        hook_manager=hook_manager
    )


@pytest.fixture
def enhanced_tester_responder(test_configuration, basic_test_scenario, hook_manager):
    """Fixture providing enhanced tester in responder mode."""
    return Tester(
        test_id=f"test-{secrets.token_hex(4)}",
        config=test_configuration,
        scenario=basic_test_scenario,
        mode=TestMode.RESPONDER,
        hook_manager=hook_manager
    )


@pytest.fixture
def enhanced_checker(test_configuration, basic_test_scenario):
    """Fixture providing enhanced checker with ANSSI compliance."""
    return Checker(
        test_id=f"check-{secrets.token_hex(4)}",
        config=test_configuration,
        scenario=basic_test_scenario,
        standards=[ComplianceStandard.ANSSI]
    )


@pytest.fixture
def enhanced_orchestrator(test_configuration):
    """Fixture providing enhanced orchestrator."""
    return Orchestrator(config=test_configuration)


@pytest.fixture
def random_test_data():
    """Fixture providing random test data for testing."""
    return generate_test_data()


# Pytest markers for different test categories
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "compliance: Compliance validation tests")
    config.addinivalue_line("markers", "anssi: ANSSI compliance tests")
    config.addinivalue_line("markers", "crypto: Cryptographic tests")
    config.addinivalue_line("markers", "performance: Performance tests")
    config.addinivalue_line("markers", "real_world: Real-world infrastructure tests")
    config.addinivalue_line("markers", "slow: Slow running tests")


# Test collection configuration
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test location."""
    for item in items:
        # Add markers based on test file location
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "compliance" in str(item.fspath):
            item.add_marker(pytest.mark.compliance)
        
        # Add markers based on test name patterns
        if "anssi" in item.name.lower():
            item.add_marker(pytest.mark.anssi)
        if "crypto" in item.name.lower():
            item.add_marker(pytest.mark.crypto)
        if "performance" in item.name.lower():
            item.add_marker(pytest.mark.performance)
