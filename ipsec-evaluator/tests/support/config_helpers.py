"""
Configuration test helpers for ipsec-evaluator.

This module provides utilities for creating test configuration files
and managing test configuration data.
"""

import os
import tempfile
import toml
import yaml
import configparser
from pathlib import Path
from typing import Dict, Any, Union, List
from contextlib import contextmanager


def write_config_file(content: str, suffix: str = ".ini") -> str:
    """
    Write configuration content to a temporary file.
    
    Args:
        content: Configuration content as string
        suffix: File suffix (.ini, .toml, .yaml, .yml)
        
    Returns:
        Path to the temporary configuration file
    """
    with tempfile.NamedTemporaryFile(mode='w', suffix=suffix, delete=False) as f:
        f.write(content)
        return f.name


def write_toml_config(config_data: Dict[str, Any]) -> str:
    """
    Write configuration data to a temporary TOML file.
    
    Args:
        config_data: Configuration data as dictionary
        
    Returns:
        Path to the temporary TOML file
    """
    with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
        toml.dump(config_data, f)
        return f.name


def write_yaml_config(config_data: Dict[str, Any]) -> str:
    """
    Write configuration data to a temporary YAML file.
    
    Args:
        config_data: Configuration data as dictionary
        
    Returns:
        Path to the temporary YAML file
    """
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        yaml.dump(config_data, f, default_flow_style=False)
        return f.name


def write_ini_config(config_data: Dict[str, Dict[str, Any]]) -> str:
    """
    Write configuration data to a temporary INI file.
    
    Args:
        config_data: Configuration data as nested dictionary
        
    Returns:
        Path to the temporary INI file
    """
    config = configparser.ConfigParser()
    
    for section_name, section_data in config_data.items():
        config.add_section(section_name)
        for key, value in section_data.items():
            config.set(section_name, key, str(value))
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.ini', delete=False) as f:
        config.write(f)
        return f.name


@contextmanager
def temporary_config_files(*config_files: str):
    """
    Context manager for temporary configuration files.
    
    Args:
        *config_files: Paths to configuration files
        
    Yields:
        List of configuration file paths
    """
    try:
        yield list(config_files)
    finally:
        for config_file in config_files:
            try:
                os.unlink(config_file)
            except (OSError, FileNotFoundError):
                pass


def create_basic_toml_config() -> Dict[str, Any]:
    """Create a basic TOML configuration for testing."""
    return {
        'global_config': {
            'timeout': 30,
            'verbose': True,
            'log_level': 'DEBUG',
            'max_concurrent_tests': 2
        },
        'network': {
            'interface': 'eth0',
            'ipsec_src': '************',
            'ipsec_dst': '************',
            'ip_src': '************',
            'ip_dst': '************',
            'port_src': 500,
            'port_dst': 500,
            'nat_traversal': False,
            'test_network': '***********/24'
        },
        'crypto': {
            'encryption_algorithms': ['AES-GCM-256'],
            'integrity_algorithms': ['HMAC-SHA2-256'],
            'dh_groups': [19],
            'prf_algorithms': ['HMAC-SHA2-256']
        }
    }


def create_basic_yaml_config() -> Dict[str, Any]:
    """Create a basic YAML configuration for testing."""
    return create_basic_toml_config()  # Same structure


def create_basic_ini_config() -> Dict[str, Dict[str, Any]]:
    """Create a basic INI configuration for testing (ipsecdr compatibility)."""
    return {
        'ARGS': {
            'timeout': '30',
            'verbose': 'True',
            'logfile': '/var/log/ipsec-evaluator.log',
            'pcap_path': '/var/pcap'
        },
        'IF': {
            'interface': 'eth0',
            'ipsec_src': '************',
            'ipsec_dst': '************',
            'ip_src': '************',
            'ip_dst': '************',
            'port_src': '500',
            'port_dst': '500'
        },
        'CA': {
            'ca_cert_path': '/etc/ipsec.d/cacerts/ca.pem',
            'cert_path': '/etc/ipsec.d/certs/client.pem',
            'key_path': '/etc/ipsec.d/private/client.pem'
        },
        'IKE_SA': {
            'encr': 'AES_GCM_16',
            'integ': 'HMAC_SHA2_256',
            'prf': 'PRF_HMAC_SHA2_256',
            'groupdesc': '19'
        },
        'CHILD_SA': {
            'encr': 'AES_GCM_16',
            'integ': 'HMAC_SHA2_256'
        }
    }


def create_invalid_config() -> Dict[str, Any]:
    """Create an invalid configuration for testing error handling."""
    return {
        'global_config': {
            'timeout': -10,  # Invalid: negative timeout
            'verbose': 'invalid_boolean',  # Invalid: not a boolean
            'log_level': 'INVALID_LEVEL',  # Invalid: unknown log level
            'max_concurrent_tests': 0  # Invalid: must be >= 1
        },
        'network': {
            'port_src': 70000,  # Invalid: port out of range
            'port_dst': -1,  # Invalid: negative port
            'ipsec_src': 'invalid_ip',  # Invalid: not an IP address
            'nat_traversal': 'not_a_boolean'  # Invalid: not a boolean
        },
        'crypto': {
            'dh_groups': [999],  # Invalid: unsupported DH group
            'encryption_algorithms': [],  # Invalid: empty list
        }
    }


def create_overlay_config() -> Dict[str, Any]:
    """Create an overlay configuration for testing."""
    return {
        'global_config': {
            'verbose': False,  # Override from base
            'log_level': 'WARNING',  # Override from base
            'max_concurrent_tests': 5  # Override from base
        },
        'network': {
            'interface': 'eth1',  # Override from base
            'nat_traversal': True  # Override from base
        },
        'crypto': {
            'encryption_algorithms': ['AES-GCM-256', 'AES-CTR'],  # Override from base
            'dh_groups': [19, 20]  # Override from base
        }
    }


def create_cli_override_args() -> Dict[str, Any]:
    """Create CLI override arguments for testing."""
    return {
        'global.timeout': '60',
        'global.verbose': 'true',
        'network.interface': 'eth2',
        'network.port_src': '4500',
        'crypto.dh_groups': '19,20,21'
    }


def create_env_variables() -> Dict[str, str]:
    """Create environment variables for testing."""
    return {
        'IPSEC_EVALUATOR_GLOBAL_TIMEOUT': '45',
        'IPSEC_EVALUATOR_GLOBAL_VERBOSE': 'false',
        'IPSEC_EVALUATOR_NETWORK_INTERFACE': 'lo',
        'IPSEC_EVALUATOR_NETWORK_NAT_TRAVERSAL': 'True',
        'IPSEC_EVALUATOR_CRYPTO_DH_GROUPS': '19,28'
    }


def create_test_pki_config() -> Dict[str, str]:
    """Create test PKI configuration with mock certificate paths."""
    test_dir = Path(__file__).parent / "certs"
    return {
        'ca_cert_path': str(test_dir / "ca.pem"),
        'cert_path': str(test_dir / "client.pem"),
        'key_path': str(test_dir / "client_key.pem"),
        'verify_certificates': 'true',
        'certificate_validation': 'true'
    }


def create_performance_config() -> Dict[str, Any]:
    """Create a performance testing configuration."""
    return {
        'global_config': {
            'max_concurrent_tests': 8,
            'timeout': 600,
            'verbose': False,
            'log_level': 'WARNING'
        },
        'network': {
            'interface': 'eth0',
            'ipsec_src': '************',
            'ipsec_dst': '************',
            'nat_traversal': False
        },
        'crypto': {
            'encryption_algorithms': ['AES-GCM-256', 'AES-CTR', 'CHACHA20-POLY1305'],
            'integrity_algorithms': ['HMAC-SHA2-256', 'HMAC-SHA2-384'],
            'dh_groups': [19, 20, 21, 28, 29, 30],
            'prf_algorithms': ['HMAC-SHA2-256', 'HMAC-SHA2-384']
        }
    }
