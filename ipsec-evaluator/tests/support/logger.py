"""
Test logging support for ipsec-evaluator.

This module provides logging utilities specifically for testing,
including test-specific loggers and log capture functionality.
"""

import logging
import sys
from pathlib import Path
from typing import Optional

# Create test logger
logger = logging.getLogger("ipsecator.tests")
logger.setLevel(logging.DEBUG)

# Create console handler if not already present
if not logger.handlers:
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.DEBUG)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s [%(levelname)8s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    console_handler.setFormatter(formatter)
    
    logger.addHandler(console_handler)


def get_test_logger(name: Optional[str] = None) -> logging.Logger:
    """Get a test-specific logger."""
    if name:
        return logging.getLogger(f"ipsecator.tests.{name}")
    return logger


def setup_test_logging(log_file: Optional[Path] = None, level: str = "DEBUG"):
    """Setup logging for tests with optional file output."""
    log_level = getattr(logging, level.upper())
    
    # Configure root test logger
    logger.setLevel(log_level)
    
    # Add file handler if specified
    if log_file:
        log_file.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(log_level)
        
        formatter = logging.Formatter(
            '%(asctime)s [%(levelname)8s] %(name)s: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)


class LogCapture:
    """Context manager for capturing log messages during tests."""
    
    def __init__(self, logger_name: str = "ipsecator", level: str = "DEBUG"):
        self.logger_name = logger_name
        self.level = getattr(logging, level.upper())
        self.records = []
        self.handler = None
        self.original_level = None
    
    def __enter__(self):
        # Create a custom handler that captures records
        self.handler = logging.Handler()
        self.handler.emit = lambda record: self.records.append(record)
        
        # Get the logger and add our handler
        target_logger = logging.getLogger(self.logger_name)
        self.original_level = target_logger.level
        target_logger.setLevel(self.level)
        target_logger.addHandler(self.handler)
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Remove our handler and restore original level
        target_logger = logging.getLogger(self.logger_name)
        target_logger.removeHandler(self.handler)
        if self.original_level is not None:
            target_logger.setLevel(self.original_level)
    
    def get_messages(self, level: Optional[str] = None) -> list:
        """Get captured log messages, optionally filtered by level."""
        if level:
            target_level = getattr(logging, level.upper())
            return [record.getMessage() for record in self.records if record.levelno >= target_level]
        return [record.getMessage() for record in self.records]
    
    def assert_logged(self, message: str, level: Optional[str] = None):
        """Assert that a specific message was logged."""
        messages = self.get_messages(level)
        assert any(message in msg for msg in messages), \
            f"Message '{message}' not found in logs: {messages}"
    
    def assert_not_logged(self, message: str, level: Optional[str] = None):
        """Assert that a specific message was NOT logged."""
        messages = self.get_messages(level)
        assert not any(message in msg for msg in messages), \
            f"Message '{message}' unexpectedly found in logs: {messages}"
