"""
Test support helpers for ipsec-evaluator.

This module provides utility functions for test setup, certificate management,
and test data generation based on the ipsecdr test patterns.
"""

import secrets
import random
from pathlib import Path
from typing import Dict, List, Any, Tuple
from datetime import datetime
from cryptography import x509
from cryptography.x509.oid import NameOID
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, ec
from cryptography.hazmat.backends import default_backend

from ipsecator.models.scenario import TestScenario
from ipsecator.models.base import TestMode
from ipsecator.engine.tester import TestData
from ipsecator.core.crypto.dh import DHManager


def write_config_file(content: str, suffix: str = ".ini") -> str:
    """
    Write configuration content to a temporary file.

    Args:
        content: Configuration content as string
        suffix: File suffix (.ini, .toml, .yaml, .yml)

    Returns:
        Path to the temporary configuration file
    """
    import tempfile
    with tempfile.NamedTemporaryFile(mode='w', suffix=suffix, delete=False) as f:
        f.write(content)
        return f.name


def ensure_test_certificates_exist():
    """
    Ensure test certificates exist, creating them if necessary.

    This function creates a complete PKI infrastructure for testing,
    including CA certificate and initiator/responder certificates.
    """
    certs_dir = Path(__file__).parent / "certs"
    certs_dir.mkdir(exist_ok=True)

    ca_cert_path = certs_dir / "ca.pem"
    ca_key_path = certs_dir / "ca_key.pem"

    # Create CA certificate if it doesn't exist
    if not ca_cert_path.exists():
        ca_key, ca_cert = create_ca_certificate()

        # Save CA certificate and key
        with open(ca_cert_path, "wb") as f:
            f.write(ca_cert.public_bytes(serialization.Encoding.PEM))

        with open(ca_key_path, "wb") as f:
            f.write(ca_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
    else:
        # Load existing CA
        with open(ca_cert_path, "rb") as f:
            ca_cert = x509.load_pem_x509_certificate(f.read(), default_backend())

        with open(ca_key_path, "rb") as f:
            ca_key = serialization.load_pem_private_key(f.read(), None, default_backend())

    # Create initiator and responder certificates
    for entity in ["initiator", "responder"]:
        cert_path = certs_dir / f"{entity}.pem"
        key_path = certs_dir / f"{entity}_key.pem"

        if not cert_path.exists():
            entity_key, entity_cert = create_entity_certificate(
                entity, ca_key, ca_cert
            )

            # Save entity certificate and key
            with open(cert_path, "wb") as f:
                f.write(entity_cert.public_bytes(serialization.Encoding.PEM))

            with open(key_path, "wb") as f:
                f.write(entity_key.private_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption()
                ))


def create_ca_certificate() -> Tuple[Any, Any]:
    """Create a CA certificate for testing."""
    # Generate CA private key
    ca_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
        backend=default_backend()
    )

    # Create CA certificate
    subject = issuer = x509.Name([
        x509.NameAttribute(NameOID.COUNTRY_NAME, "FR"),
        x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Test"),
        x509.NameAttribute(NameOID.LOCALITY_NAME, "Test"),
        x509.NameAttribute(NameOID.ORGANIZATION_NAME, "IPsec Evaluator Test CA"),
        x509.NameAttribute(NameOID.COMMON_NAME, "Test CA"),
    ])

    ca_cert = x509.CertificateBuilder().subject_name(
        subject
    ).issuer_name(
        issuer
    ).public_key(
        ca_key.public_key()
    ).serial_number(
        x509.random_serial_number()
    ).not_valid_before(
        datetime.utcnow()
    ).not_valid_after(
        datetime.utcnow().replace(year=datetime.utcnow().year + 10)
    ).add_extension(
        x509.SubjectAlternativeName([
            x509.DNSName("testca.local"),
        ]),
        critical=False,
    ).add_extension(
        x509.BasicConstraints(ca=True, path_length=None),
        critical=True,
    ).add_extension(
        x509.KeyUsage(
            digital_signature=True,
            content_commitment=False,
            key_encipherment=False,
            data_encipherment=False,
            key_agreement=False,
            key_cert_sign=True,
            crl_sign=True,
            encipher_only=False,
            decipher_only=False,
        ),
        critical=True,
    ).sign(ca_key, hashes.SHA256(), default_backend())

    return ca_key, ca_cert


def create_entity_certificate(entity_name: str, ca_key: Any, ca_cert: Any) -> Tuple[Any, Any]:
    """Create an entity certificate signed by the CA."""
    # Generate entity private key using ECDSA for modern crypto
    entity_key = ec.generate_private_key(ec.SECP256R1(), default_backend())

    # Create entity certificate
    subject = x509.Name([
        x509.NameAttribute(NameOID.COUNTRY_NAME, "FR"),
        x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Test"),
        x509.NameAttribute(NameOID.LOCALITY_NAME, "Test"),
        x509.NameAttribute(NameOID.ORGANIZATION_NAME, f"IPsec Evaluator {entity_name.title()}"),
        x509.NameAttribute(NameOID.COMMON_NAME, f"{entity_name}.test.local"),
    ])

    entity_cert = x509.CertificateBuilder().subject_name(
        subject
    ).issuer_name(
        ca_cert.subject
    ).public_key(
        entity_key.public_key()
    ).serial_number(
        x509.random_serial_number()
    ).not_valid_before(
        datetime.utcnow()
    ).not_valid_after(
        datetime.utcnow().replace(year=datetime.utcnow().year + 5)
    ).add_extension(
        x509.SubjectAlternativeName([
            x509.DNSName(f"{entity_name}.test.local"),
            x509.IPAddress("************" if entity_name == "initiator" else "************"),
        ]),
        critical=False,
    ).add_extension(
        x509.BasicConstraints(ca=False, path_length=None),
        critical=True,
    ).add_extension(
        x509.KeyUsage(
            digital_signature=True,
            content_commitment=False,
            key_encipherment=True,
            data_encipherment=False,
            key_agreement=True,
            key_cert_sign=False,
            crl_sign=False,
            encipher_only=False,
            decipher_only=False,
        ),
        critical=True,
    ).add_extension(
        x509.ExtendedKeyUsage([
            x509.oid.ExtendedKeyUsageOID.CLIENT_AUTH,
            x509.oid.ExtendedKeyUsageOID.SERVER_AUTH,
        ]),
        critical=True,
    ).sign(ca_key, hashes.SHA256(), default_backend())

    return entity_key, entity_cert


def load_test_certificates() -> Dict[str, str]:
    """Load test certificates and return their paths."""
    certs_dir = Path(__file__).parent / "certs"

    return {
        "ca_cert": str(certs_dir / "ca.pem"),
        "initiator_cert": str(certs_dir / "initiator.pem"),
        "initiator_key": str(certs_dir / "initiator_key.pem"),
        "responder_cert": str(certs_dir / "responder.pem"),
        "responder_key": str(certs_dir / "responder_key.pem")
    }


def create_test_scenario(
    name: str,
    description: str = None,
    tags: List[str] = None,
    **kwargs
) -> TestScenario:
    """Create a test scenario with default values."""
    return TestScenario(
        name=name,
        description=description or f"Test scenario: {name}",
        version="1.0",
        tags=tags or ["test"],
        metadata=kwargs
    )


def generate_test_data(
    test_id: str = None,
    scenario_name: str = "test_scenario",
    mode: TestMode = TestMode.INITIATOR,
    num_exchanges: int = 3,
    num_esp_packets: int = 5
) -> TestData:
    """Generate realistic test data for testing purposes."""
    test_data = TestData(
        test_id=test_id or f"test-{secrets.token_hex(4)}",
        scenario_name=scenario_name,
        mode=mode,
        start_time=datetime.now()
    )

    # Generate IKEv2 exchanges
    exchange_types = ["IKE_SA_INIT", "IKE_AUTH", "CREATE_CHILD_SA"]
    for i, exchange_type in enumerate(exchange_types[:num_exchanges]):
        timing = random.uniform(0.05, 0.3)  # Random timing between 50ms and 300ms

        exchange_data = {
            "exchange_type": exchange_type,
            "role": mode.value,
            "timestamp": datetime.now().isoformat(),
            "algorithms": {
                "encryption": ["AES_GCM_16"],
                "integrity": ["HMAC_SHA2_256"],
                "prf": ["PRF_HMAC_SHA2_256"],
                "dh_groups": [19, 28]  # secp256r1 and brainpoolP256r1
            }
        }

        test_data.ikev2_exchanges.append(exchange_data)
        test_data.exchange_timings[exchange_type] = timing

    # Generate ESP packets
    for i in range(num_esp_packets):
        packet_data = {
            "packet_number": i + 1,
            "timestamp": datetime.now().isoformat(),
            "direction": "outbound" if mode == TestMode.INITIATOR else "inbound",
            "size": random.randint(1200, 1500),
            "encryption_algorithm": "AES_GCM_16",
            "processing_time": random.uniform(0.01, 0.05)
        }
        test_data.esp_packets.append(packet_data)

    # Add metadata
    test_data.metadata.update({
        "generated": True,
        "generator": "test_helpers",
        "crypto_algorithms": {
            "encryption": "AES_GCM_16",
            "integrity": "HMAC_SHA2_256",
            "dh_group": 19
        }
    })

    test_data.end_time = datetime.now()
    return test_data


def generate_random_spi() -> bytes:
    """Generate a random SPI (Security Parameter Index)."""
    return secrets.token_bytes(8)


def generate_random_nonce(length: int = 32) -> bytes:
    """Generate a random nonce."""
    return secrets.token_bytes(length)


def generate_dh_keypair(group_id: int) -> Tuple[bytes, bytes]:
    """Generate a DH keypair for testing."""
    dh_manager = DHManager()
    return dh_manager.generate_keypair(group_id)


def create_mock_ikev2_packet(exchange_type: str = "IKE_SA_INIT") -> Dict[str, Any]:
    """Create a mock IKEv2 packet for testing."""
    return {
        "exchange_type": exchange_type,
        "message_id": random.randint(0, 1000),
        "spi_i": generate_random_spi(),
        "spi_r": generate_random_spi(),
        "flags": {"initiator": True, "version": 0x20, "response": False},
        "payloads": [],
        "timestamp": datetime.now().isoformat()
    }


def create_mock_esp_packet(spi: int = None) -> Dict[str, Any]:
    """Create a mock ESP packet for testing."""
    return {
        "spi": spi or random.randint(1000, 9999),
        "sequence_number": random.randint(1, 1000),
        "payload_length": random.randint(1200, 1500),
        "encryption_algorithm": "AES_GCM_16",
        "timestamp": datetime.now().isoformat()
    }


def assert_anssi_compliance(test_data: TestData) -> bool:
    """Assert that test data meets ANSSI compliance requirements."""
    # Check for required exchanges
    exchange_types = [ex.get("exchange_type") for ex in test_data.ikev2_exchanges]
    assert "IKE_SA_INIT" in exchange_types, "Missing IKE_SA_INIT exchange"
    assert "IKE_AUTH" in exchange_types, "Missing IKE_AUTH exchange"

    # Check algorithms
    for exchange in test_data.ikev2_exchanges:
        if "algorithms" in exchange:
            algorithms = exchange["algorithms"]

            # Check encryption algorithms
            if "encryption" in algorithms:
                for alg in algorithms["encryption"]:
                    assert alg in ["AES_GCM_16", "AES_CTR", "CHACHA20_POLY1305"], \
                        f"Non-ANSSI approved encryption: {alg}"

            # Check DH groups
            if "dh_groups" in algorithms:
                for group in algorithms["dh_groups"]:
                    assert group in [19, 20, 21, 27, 28, 29, 30], \
                        f"Non-ANSSI approved DH group: {group}"

    return True
