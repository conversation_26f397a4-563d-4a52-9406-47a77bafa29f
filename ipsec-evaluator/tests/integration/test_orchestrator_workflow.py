"""
Integration tests for the complete orchestrator/tester/checker workflow.

Tests the full end-to-end testing process including scenario execution,
compliance validation, and reporting, based on ipsecdr patterns.
"""

import pytest
import asyncio
from pathlib import Path

from ipsec_evaluator.engine.orchestrator import EnhancedOrchestrator
from ipsec_evaluator.engine.tester import <PERSON>han<PERSON><PERSON><PERSON>, TestData
from ipsec_evaluator.engine.checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ComplianceStandard
from ipsec_evaluator.models.base import TestMode, ComplianceLevel
from ipsec_evaluator.models.results import ComplianceResult
from tests.support.helpers import create_test_scenario


class TestOrchestratorWorkflow:
    """Integration tests for the complete testing workflow."""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_single_scenario_execution(self, enhanced_orchestrator, basic_test_scenario):
        """Test execution of a single test scenario."""
        orchestrator = enhanced_orchestrator
        
        # Note: This would normally load scenarios from files
        # For testing, we'll simulate the workflow components
        
        # Create tester
        tester = EnhancedTester(
            test_id="integration-test-001",
            config=orchestrator.config,
            scenario=basic_test_scenario,
            mode=TestMode.INITIATOR
        )
        
        # Execute test
        test_data = await tester.execute()
        
        # Verify test execution
        assert isinstance(test_data, TestData)
        assert test_data.end_time is not None
        assert len(test_data.ikev2_exchanges) > 0
        assert len(test_data.esp_packets) > 0
        
        # Create checker
        checker = EnhancedChecker(
            test_id=test_data.test_id,
            config=orchestrator.config,
            scenario=basic_test_scenario,
            standards=[ComplianceStandard.ANSSI]
        )
        
        # Check compliance
        compliance_result = await checker.check_compliance(test_data)
        
        # Verify compliance checking
        assert isinstance(compliance_result, ComplianceResult)
        assert compliance_result.level is not None
        assert compliance_result.score >= 0.0
        assert compliance_result.score <= 100.0
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_initiator_responder_workflow(self, test_configuration, basic_test_scenario):
        """Test complete workflow for both initiator and responder modes."""
        results = {}
        
        # Test both modes
        for mode in [TestMode.INITIATOR, TestMode.RESPONDER]:
            # Create tester
            tester = EnhancedTester(
                test_id=f"workflow-test-{mode.value}",
                config=test_configuration,
                scenario=basic_test_scenario,
                mode=mode
            )
            
            # Execute test
            test_data = await tester.execute()
            
            # Create checker
            checker = EnhancedChecker(
                test_id=test_data.test_id,
                config=test_configuration,
                scenario=basic_test_scenario
            )
            
            # Check compliance
            compliance_result = await checker.check_compliance(test_data)
            
            # Store results
            results[mode.value] = {
                "test_data": test_data,
                "compliance": compliance_result
            }
        
        # Verify both modes completed successfully
        assert len(results) == 2
        
        # Compare initiator vs responder results
        initiator_result = results["initiator"]
        responder_result = results["responder"]
        
        # Both should have similar structure
        assert len(initiator_result["test_data"].ikev2_exchanges) == len(responder_result["test_data"].ikev2_exchanges)
        assert len(initiator_result["test_data"].esp_packets) == len(responder_result["test_data"].esp_packets)
        
        # ESP packet directions should be different
        init_directions = [p["direction"] for p in initiator_result["test_data"].esp_packets]
        resp_directions = [p["direction"] for p in responder_result["test_data"].esp_packets]
        
        assert all(d == "outbound" for d in init_directions)
        assert all(d == "inbound" for d in resp_directions)
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_anssi_compliance_workflow(self, test_configuration, anssi_test_scenario):
        """Test complete ANSSI compliance validation workflow."""
        # Create tester with ANSSI-compliant configuration
        tester = EnhancedTester(
            test_id="anssi-compliance-test",
            config=test_configuration,
            scenario=anssi_test_scenario,
            mode=TestMode.INITIATOR
        )
        
        # Execute test
        test_data = await tester.execute()
        
        # Verify test used ANSSI-compliant algorithms
        for exchange in test_data.ikev2_exchanges:
            if "algorithms" in exchange:
                algorithms = exchange["algorithms"]
                
                # Check encryption algorithms
                if "encryption" in algorithms:
                    for alg in algorithms["encryption"]:
                        assert alg in ["AES_GCM_16", "AES_CTR", "CHACHA20_POLY1305"]
                
                # Check DH groups
                if "dh_groups" in algorithms:
                    for group in algorithms["dh_groups"]:
                        assert group in [19, 20, 21, 27, 28, 29, 30]
        
        # Create ANSSI checker
        checker = EnhancedChecker(
            test_id=test_data.test_id,
            config=test_configuration,
            scenario=anssi_test_scenario,
            standards=[ComplianceStandard.ANSSI]
        )
        
        # Check ANSSI compliance
        compliance_result = await checker.check_compliance(test_data)
        
        # Should be ANSSI compliant
        assert compliance_result.level in [ComplianceLevel.COMPLIANT, ComplianceLevel.PARTIAL]
        assert compliance_result.score >= 80.0
        
        # Should have minimal critical issues
        critical_issues = [issue for issue in compliance_result.issues if "critical" in issue.lower()]
        assert len(critical_issues) == 0
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_concurrent_scenario_execution(self, test_configuration):
        """Test concurrent execution of multiple scenarios."""
        # Create multiple test scenarios
        scenarios = [
            create_test_scenario(f"concurrent_test_{i}", tags=["concurrent", "integration"])
            for i in range(3)
        ]
        
        # Create testers for each scenario
        testers = []
        for i, scenario in enumerate(scenarios):
            tester = EnhancedTester(
                test_id=f"concurrent-test-{i}",
                config=test_configuration,
                scenario=scenario,
                mode=TestMode.INITIATOR
            )
            testers.append(tester)
        
        # Execute all tests concurrently
        tasks = [tester.execute() for tester in testers]
        test_results = await asyncio.gather(*tasks)
        
        # Verify all tests completed
        assert len(test_results) == 3
        for result in test_results:
            assert isinstance(result, TestData)
            assert result.end_time is not None
            assert len(result.ikev2_exchanges) > 0
            assert len(result.esp_packets) > 0
        
        # Create checkers and validate compliance concurrently
        checkers = []
        for i, (scenario, test_data) in enumerate(zip(scenarios, test_results)):
            checker = EnhancedChecker(
                test_id=test_data.test_id,
                config=test_configuration,
                scenario=scenario
            )
            checkers.append(checker)
        
        # Check compliance concurrently
        compliance_tasks = [
            checker.check_compliance(test_data)
            for checker, test_data in zip(checkers, test_results)
        ]
        compliance_results = await asyncio.gather(*compliance_tasks)
        
        # Verify all compliance checks completed
        assert len(compliance_results) == 3
        for result in compliance_results:
            assert isinstance(result, ComplianceResult)
            assert result.level is not None
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_error_recovery_workflow(self, test_configuration, basic_test_scenario):
        """Test error recovery in the workflow."""
        # Create tester
        tester = EnhancedTester(
            test_id="error-recovery-test",
            config=test_configuration,
            scenario=basic_test_scenario,
            mode=TestMode.INITIATOR
        )
        
        # Execute test (should complete normally)
        test_data = await tester.execute()
        
        # Simulate error in test data
        test_data.errors.append("Simulated test error")
        
        # Create checker
        checker = EnhancedChecker(
            test_id=test_data.test_id,
            config=test_configuration,
            scenario=basic_test_scenario
        )
        
        # Check compliance (should handle errors gracefully)
        compliance_result = await checker.check_compliance(test_data)
        
        # Should still produce a result despite errors
        assert isinstance(compliance_result, ComplianceResult)
        assert compliance_result.level is not None
        
        # May have reduced score due to errors
        assert compliance_result.score >= 0.0
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_performance_workflow(self, test_configuration, basic_test_scenario):
        """Test workflow performance with timing analysis."""
        import time
        
        # Measure total workflow time
        start_time = time.time()
        
        # Create and execute tester
        tester = EnhancedTester(
            test_id="performance-test",
            config=test_configuration,
            scenario=basic_test_scenario,
            mode=TestMode.INITIATOR
        )
        
        test_data = await tester.execute()
        
        # Create and execute checker
        checker = EnhancedChecker(
            test_id=test_data.test_id,
            config=test_configuration,
            scenario=basic_test_scenario
        )
        
        compliance_result = await checker.check_compliance(test_data)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Workflow should complete within reasonable time
        assert total_time < 2.0  # Less than 2 seconds for basic test
        
        # Verify performance data was collected
        assert test_data.start_time is not None
        assert test_data.end_time is not None
        assert len(test_data.exchange_timings) > 0
        
        # Check individual exchange timings
        for exchange_type, timing in test_data.exchange_timings.items():
            assert timing > 0
            assert timing < 1.0  # Each exchange should be fast
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_metadata_collection_workflow(self, test_configuration, basic_test_scenario):
        """Test comprehensive metadata collection throughout workflow."""
        # Create tester
        tester = EnhancedTester(
            test_id="metadata-test",
            config=test_configuration,
            scenario=basic_test_scenario,
            mode=TestMode.INITIATOR
        )
        
        # Execute test
        test_data = await tester.execute()
        
        # Verify comprehensive metadata collection
        metadata = test_data.metadata
        
        # Protocol metadata
        assert "ikev2_role" in metadata
        assert "esp_mode" in metadata
        assert "local_ip" in metadata
        assert "remote_ip" in metadata
        
        # Statistics metadata
        assert "ikev2_statistics" in metadata
        assert "esp_statistics" in metadata
        
        # Exchange metadata
        assert len(test_data.ikev2_exchanges) > 0
        for exchange in test_data.ikev2_exchanges:
            assert "exchange_type" in exchange
            assert "timestamp" in exchange
            assert "role" in exchange
        
        # ESP packet metadata
        assert len(test_data.esp_packets) > 0
        for packet in test_data.esp_packets:
            assert "packet_number" in packet
            assert "timestamp" in packet
            assert "direction" in packet
            assert "size" in packet
            assert "processing_time" in packet
        
        # Create checker and verify metadata usage
        checker = EnhancedChecker(
            test_id=test_data.test_id,
            config=test_configuration,
            scenario=basic_test_scenario
        )
        
        compliance_result = await checker.check_compliance(test_data)
        
        # Checker should generate summary with metadata
        summary = checker.get_compliance_summary(compliance_result)
        assert "test_id" in summary
        assert "scenario" in summary
        assert summary["test_id"] == test_data.test_id
        assert summary["scenario"] == basic_test_scenario.name
    
    @pytest.mark.integration
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_large_scale_workflow(self, test_configuration):
        """Test workflow with larger scale scenarios."""
        # Create multiple scenarios with different characteristics
        scenarios = [
            create_test_scenario("large_scale_basic", tags=["large", "basic"]),
            create_test_scenario("large_scale_anssi", tags=["large", "anssi"]),
            create_test_scenario("large_scale_performance", tags=["large", "performance"])
        ]
        
        all_results = []
        
        # Execute each scenario
        for scenario in scenarios:
            # Test both initiator and responder modes
            for mode in [TestMode.INITIATOR, TestMode.RESPONDER]:
                # Create tester
                tester = EnhancedTester(
                    test_id=f"large-scale-{scenario.name}-{mode.value}",
                    config=test_configuration,
                    scenario=scenario,
                    mode=mode
                )
                
                # Execute test
                test_data = await tester.execute()
                
                # Create checker
                checker = EnhancedChecker(
                    test_id=test_data.test_id,
                    config=test_configuration,
                    scenario=scenario
                )
                
                # Check compliance
                compliance_result = await checker.check_compliance(test_data)
                
                all_results.append({
                    "scenario": scenario.name,
                    "mode": mode.value,
                    "test_data": test_data,
                    "compliance": compliance_result
                })
        
        # Verify all tests completed
        assert len(all_results) == 6  # 3 scenarios × 2 modes
        
        # Analyze results
        compliant_count = sum(
            1 for result in all_results
            if result["compliance"].level == ComplianceLevel.COMPLIANT
        )
        
        partial_count = sum(
            1 for result in all_results
            if result["compliance"].level == ComplianceLevel.PARTIAL
        )
        
        # Most should be at least partially compliant
        assert (compliant_count + partial_count) >= len(all_results) * 0.8
