"""
Unit tests for IKEv2 protocol implementation.

Tests the enhanced IKEv2 protocol with hook system integration,
based on ipsecdr patterns but enhanced for the new architecture.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch

from ipsec_evaluator.core.ikev2 import (
    IKEv2Protocol,
    IKEv2<PERSON><PERSON>,
    IKEv2State,
    IKEv2<PERSON><PERSON><PERSON><PERSON><PERSON>,
    HookType,
    HookPriority,
    ExchangeType
)


class TestIKEv2Protocol:
    """Test suite for the IKEv2 Protocol."""
    
    def test_initialization_initiator(self, ikev2_protocol_initiator):
        """Test IKEv2 protocol initialization as initiator."""
        protocol = ikev2_protocol_initiator
        
        assert protocol.role == IKEv2Role.INITIATOR
        assert protocol.local_ip == "************"
        assert protocol.remote_ip == "************"
        assert protocol.local_port == 500
        assert protocol.remote_port == 500
        assert protocol.state_machine.current_state == IKEv2State.INITIAL
        assert isinstance(protocol.hook_manager, IKEv2HookManager)
    
    def test_initialization_responder(self, ikev2_protocol_responder):
        """Test IKEv2 protocol initialization as responder."""
        protocol = ikev2_protocol_responder
        
        assert protocol.role == IKEv2Role.RESPONDER
        assert protocol.local_ip == "************"
        assert protocol.remote_ip == "************"
        assert protocol.state_machine.current_state == IKEv2State.INITIAL
    
    def test_state_machine_integration(self, ikev2_protocol_initiator):
        """Test state machine integration."""
        protocol = ikev2_protocol_initiator
        state_machine = protocol.state_machine
        
        # Initial state should be INITIAL
        assert state_machine.current_state == IKEv2State.INITIAL
        
        # Test state transitions
        assert state_machine.can_transition_to(IKEv2State.SA_INIT_SENT)
        state_machine.transition_to(IKEv2State.SA_INIT_SENT)
        assert state_machine.current_state == IKEv2State.SA_INIT_SENT
        
        # Test invalid transitions
        assert not state_machine.can_transition_to(IKEv2State.ESTABLISHED)
    
    def test_hook_manager_integration(self, ikev2_protocol_initiator):
        """Test hook manager integration."""
        protocol = ikev2_protocol_initiator
        hook_manager = protocol.hook_manager
        
        # Test hook registration
        test_hook_called = False
        
        def test_hook(context):
            nonlocal test_hook_called
            test_hook_called = True
            return {"test": "success"}
        
        hook_manager.register_hook(
            HookType.PRE_PACKET,
            test_hook,
            HookPriority.NORMAL,
            "test_hook"
        )
        
        # Verify hook is registered
        hooks = hook_manager.get_hooks(HookType.PRE_PACKET)
        assert len(hooks) == 1
        assert hooks[0]["name"] == "test_hook"
    
    @pytest.mark.asyncio
    async def test_start_exchange(self, ikev2_protocol_initiator):
        """Test starting an IKE exchange."""
        protocol = ikev2_protocol_initiator
        
        # Start IKE_SA_INIT exchange
        exchange = await protocol.start_exchange(ExchangeType.IKE_SA_INIT)
        
        assert exchange is not None
        assert exchange.exchange_type == ExchangeType.IKE_SA_INIT
        assert exchange.role == IKEv2Role.INITIATOR
        assert exchange.state == "initiated"
    
    @pytest.mark.asyncio
    async def test_exchange_with_hooks(self, ikev2_protocol_initiator):
        """Test exchange execution with hook callbacks."""
        protocol = ikev2_protocol_initiator
        hook_manager = protocol.hook_manager
        
        # Track hook executions
        hook_executions = []
        
        def pre_exchange_hook(context):
            hook_executions.append("pre_exchange")
            return {"status": "pre_executed"}
        
        def post_exchange_hook(context):
            hook_executions.append("post_exchange")
            return {"status": "post_executed"}
        
        # Register hooks
        hook_manager.register_hook(
            HookType.PRE_EXCHANGE,
            pre_exchange_hook,
            HookPriority.HIGH,
            "pre_exchange_test"
        )
        
        hook_manager.register_hook(
            HookType.POST_EXCHANGE,
            post_exchange_hook,
            HookPriority.HIGH,
            "post_exchange_test"
        )
        
        # Start exchange
        exchange = await protocol.start_exchange(ExchangeType.IKE_SA_INIT)
        
        # Verify hooks were executed
        assert "pre_exchange" in hook_executions
        # Note: post_exchange would be called when exchange completes
    
    def test_statistics_collection(self, ikev2_protocol_initiator):
        """Test statistics collection."""
        protocol = ikev2_protocol_initiator
        
        stats = protocol.get_statistics()
        
        # Check basic statistics structure
        assert "state" in stats
        assert "role" in stats
        assert "exchanges_initiated" in stats
        assert "exchanges_completed" in stats
        assert "packets_sent" in stats
        assert "packets_received" in stats
        assert "hook_executions" in stats
        
        # Initial values
        assert stats["state"] == IKEv2State.INITIAL.value
        assert stats["role"] == IKEv2Role.INITIATOR.value
        assert stats["exchanges_initiated"] == 0
        assert stats["exchanges_completed"] == 0
        assert stats["packets_sent"] == 0
        assert stats["packets_received"] == 0
    
    def test_security_association_management(self, ikev2_protocol_initiator):
        """Test security association management."""
        protocol = ikev2_protocol_initiator
        
        # Initially no SAs
        assert len(protocol.security_associations) == 0
        
        # Create a test SA
        sa_data = {
            "spi_i": b"\x01\x02\x03\x04\x05\x06\x07\x08",
            "spi_r": b"\x08\x07\x06\x05\x04\x03\x02\x01",
            "encryption_algorithm": "AES_GCM_16",
            "integrity_algorithm": "HMAC_SHA2_256",
            "dh_group": 19
        }
        
        sa = protocol.create_security_association(sa_data)
        
        assert sa is not None
        assert len(protocol.security_associations) == 1
        assert sa.spi_i == sa_data["spi_i"]
        assert sa.spi_r == sa_data["spi_r"]
    
    def test_error_handling(self, ikev2_protocol_initiator):
        """Test error handling in protocol operations."""
        protocol = ikev2_protocol_initiator
        
        # Test invalid state transitions
        with pytest.raises(ValueError):
            protocol.state_machine.transition_to(IKEv2State.ESTABLISHED)
        
        # Test invalid exchange types
        with pytest.raises(ValueError):
            asyncio.run(protocol.start_exchange("INVALID_EXCHANGE"))
    
    def test_configuration_validation(self, test_configuration):
        """Test protocol configuration validation."""
        # Test valid configuration
        protocol = IKEv2Protocol(
            role=IKEv2Role.INITIATOR,
            local_ip="************",
            remote_ip="************"
        )
        assert protocol is not None
        
        # Test invalid IP addresses
        with pytest.raises(ValueError):
            IKEv2Protocol(
                role=IKEv2Role.INITIATOR,
                local_ip="invalid_ip",
                remote_ip="************"
            )
    
    @pytest.mark.asyncio
    async def test_concurrent_exchanges(self, ikev2_protocol_initiator):
        """Test handling of concurrent exchanges."""
        protocol = ikev2_protocol_initiator
        
        # Start multiple exchanges concurrently
        tasks = [
            protocol.start_exchange(ExchangeType.IKE_SA_INIT),
            protocol.start_exchange(ExchangeType.IKE_AUTH),
        ]
        
        # Note: In real implementation, this might be restricted
        # For now, we test that the protocol can handle the requests
        exchanges = await asyncio.gather(*tasks, return_exceptions=True)
        
        # At least one should succeed
        successful_exchanges = [e for e in exchanges if not isinstance(e, Exception)]
        assert len(successful_exchanges) >= 1
    
    def test_packet_metadata_collection(self, ikev2_protocol_initiator):
        """Test packet metadata collection."""
        protocol = ikev2_protocol_initiator
        
        # Simulate packet processing
        packet_data = {
            "exchange_type": "IKE_SA_INIT",
            "message_id": 1,
            "length": 1024,
            "timestamp": "2024-01-01T12:00:00Z"
        }
        
        protocol.record_packet_metadata(packet_data)
        
        # Check that metadata was recorded
        stats = protocol.get_statistics()
        assert "packet_metadata" in stats
        assert len(stats["packet_metadata"]) == 1
        assert stats["packet_metadata"][0]["exchange_type"] == "IKE_SA_INIT"
    
    def test_performance_tracking(self, ikev2_protocol_initiator):
        """Test performance tracking capabilities."""
        protocol = ikev2_protocol_initiator
        
        # Simulate exchange timing
        exchange_id = "test_exchange_001"
        protocol.start_timing(exchange_id)
        
        # Simulate some processing time
        import time
        time.sleep(0.01)  # 10ms
        
        duration = protocol.end_timing(exchange_id)
        
        assert duration > 0
        assert duration < 1.0  # Should be less than 1 second
        
        # Check timing is recorded in statistics
        stats = protocol.get_statistics()
        assert "exchange_timings" in stats
        assert exchange_id in stats["exchange_timings"]
    
    def test_cleanup_and_teardown(self, ikev2_protocol_initiator):
        """Test protocol cleanup and teardown."""
        protocol = ikev2_protocol_initiator
        
        # Create some state
        sa_data = {
            "spi_i": b"\x01\x02\x03\x04\x05\x06\x07\x08",
            "spi_r": b"\x08\x07\x06\x05\x04\x03\x02\x01",
            "encryption_algorithm": "AES_GCM_16"
        }
        protocol.create_security_association(sa_data)
        
        # Perform cleanup
        protocol.cleanup()
        
        # Verify cleanup
        assert len(protocol.security_associations) == 0
        assert protocol.state_machine.current_state == IKEv2State.INITIAL
        
        stats = protocol.get_statistics()
        assert stats["exchanges_initiated"] == 0
        assert stats["exchanges_completed"] == 0
