"""
Unit tests for ESP protocol implementation.

Tests the enhanced ESP protocol with anti-replay protection and statistics,
based on ipsecdr patterns but enhanced for the new architecture.
"""

import pytest
import secrets
from unittest.mock import Mock, patch

from ipsec_evaluator.core.esp import (
    ESPProtocol,
    ESPMode,
    ESPSecurityAssociation,
    ESPStatistics,
    ESPPacketMetadata
)


class TestESPProtocol:
    """Test suite for the ESP Protocol."""
    
    def test_initialization_tunnel_mode(self, esp_protocol):
        """Test ESP protocol initialization in tunnel mode."""
        protocol = esp_protocol
        
        assert protocol.mode == ESPMode.TUNNEL
        assert protocol.enable_anti_replay is True
        assert isinstance(protocol.statistics, ESPStatistics)
        assert len(protocol.inbound_sas) == 0
        assert len(protocol.outbound_sas) == 0
    
    def test_initialization_transport_mode(self):
        """Test ESP protocol initialization in transport mode."""
        protocol = ESPProtocol(
            mode=ESPMode.TRANSPORT,
            enable_anti_replay=False
        )
        
        assert protocol.mode == ESPMode.TRANSPORT
        assert protocol.enable_anti_replay is False
    
    def test_security_association_creation(self, esp_protocol):
        """Test ESP security association creation."""
        protocol = esp_protocol
        
        # Create inbound SA
        inbound_sa_data = {
            "spi": 0x12345678,
            "encryption_algorithm": "AES_GCM_16",
            "encryption_key": secrets.token_bytes(32),
            "integrity_algorithm": "HMAC_SHA2_256",
            "integrity_key": secrets.token_bytes(32)
        }
        
        inbound_sa = protocol.create_inbound_sa(inbound_sa_data)
        
        assert inbound_sa is not None
        assert inbound_sa.spi == 0x12345678
        assert inbound_sa.encryption_algorithm == "AES_GCM_16"
        assert len(protocol.inbound_sas) == 1
        
        # Create outbound SA
        outbound_sa_data = {
            "spi": 0x87654321,
            "encryption_algorithm": "AES_GCM_16",
            "encryption_key": secrets.token_bytes(32),
            "integrity_algorithm": "HMAC_SHA2_256",
            "integrity_key": secrets.token_bytes(32)
        }
        
        outbound_sa = protocol.create_outbound_sa(outbound_sa_data)
        
        assert outbound_sa is not None
        assert outbound_sa.spi == 0x87654321
        assert len(protocol.outbound_sas) == 1
    
    def test_packet_encryption(self, esp_protocol):
        """Test ESP packet encryption."""
        protocol = esp_protocol
        
        # Create outbound SA
        sa_data = {
            "spi": 0x12345678,
            "encryption_algorithm": "AES_GCM_16",
            "encryption_key": secrets.token_bytes(32),
            "integrity_algorithm": "HMAC_SHA2_256",
            "integrity_key": secrets.token_bytes(32)
        }
        
        sa = protocol.create_outbound_sa(sa_data)
        
        # Test packet data
        plaintext = b"Hello, ESP World!" * 10  # Make it larger
        
        # Encrypt packet
        encrypted_packet = protocol.encrypt_packet(sa.spi, plaintext)
        
        assert encrypted_packet is not None
        assert len(encrypted_packet) > len(plaintext)  # Should be larger due to headers/padding
        assert encrypted_packet != plaintext  # Should be different
        
        # Check statistics
        stats = protocol.get_statistics()
        assert stats.packets_encrypted == 1
        assert stats.bytes_encrypted > 0
    
    def test_packet_decryption(self, esp_protocol):
        """Test ESP packet decryption."""
        protocol = esp_protocol
        
        # Create matching inbound and outbound SAs
        sa_data = {
            "spi": 0x12345678,
            "encryption_algorithm": "AES_GCM_16",
            "encryption_key": secrets.token_bytes(32),
            "integrity_algorithm": "HMAC_SHA2_256",
            "integrity_key": secrets.token_bytes(32)
        }
        
        outbound_sa = protocol.create_outbound_sa(sa_data)
        inbound_sa = protocol.create_inbound_sa(sa_data)
        
        # Test packet data
        original_plaintext = b"Hello, ESP World!" * 10
        
        # Encrypt then decrypt
        encrypted_packet = protocol.encrypt_packet(outbound_sa.spi, original_plaintext)
        decrypted_plaintext = protocol.decrypt_packet(inbound_sa.spi, encrypted_packet)
        
        assert decrypted_plaintext == original_plaintext
        
        # Check statistics
        stats = protocol.get_statistics()
        assert stats.packets_encrypted == 1
        assert stats.packets_decrypted == 1
        assert stats.bytes_encrypted > 0
        assert stats.bytes_decrypted > 0
    
    def test_anti_replay_protection(self, esp_protocol):
        """Test anti-replay protection mechanism."""
        protocol = esp_protocol
        
        # Create inbound SA
        sa_data = {
            "spi": 0x12345678,
            "encryption_algorithm": "AES_GCM_16",
            "encryption_key": secrets.token_bytes(32),
            "integrity_algorithm": "HMAC_SHA2_256",
            "integrity_key": secrets.token_bytes(32)
        }
        
        sa = protocol.create_inbound_sa(sa_data)
        
        # Test sequence number validation
        assert protocol.check_replay_protection(sa.spi, 1) is True  # First packet
        assert protocol.check_replay_protection(sa.spi, 2) is True  # Second packet
        assert protocol.check_replay_protection(sa.spi, 1) is False  # Replay attack
        assert protocol.check_replay_protection(sa.spi, 3) is True  # Valid next packet
        
        # Test window behavior
        for seq in range(4, 100):
            assert protocol.check_replay_protection(sa.spi, seq) is True
        
        # Old sequence numbers should be rejected
        assert protocol.check_replay_protection(sa.spi, 50) is False
    
    def test_statistics_tracking(self, esp_protocol):
        """Test ESP statistics tracking."""
        protocol = esp_protocol
        
        # Initial statistics
        stats = protocol.get_statistics()
        assert stats.packets_encrypted == 0
        assert stats.packets_decrypted == 0
        assert stats.bytes_encrypted == 0
        assert stats.bytes_decrypted == 0
        assert stats.replay_failures == 0
        assert stats.authentication_failures == 0
        
        # Create SA and process packets
        sa_data = {
            "spi": 0x12345678,
            "encryption_algorithm": "AES_GCM_16",
            "encryption_key": secrets.token_bytes(32),
            "integrity_algorithm": "HMAC_SHA2_256",
            "integrity_key": secrets.token_bytes(32)
        }
        
        outbound_sa = protocol.create_outbound_sa(sa_data)
        inbound_sa = protocol.create_inbound_sa(sa_data)
        
        # Process multiple packets
        for i in range(5):
            plaintext = f"Test packet {i}".encode() * 10
            encrypted = protocol.encrypt_packet(outbound_sa.spi, plaintext)
            decrypted = protocol.decrypt_packet(inbound_sa.spi, encrypted)
        
        # Check updated statistics
        stats = protocol.get_statistics()
        assert stats.packets_encrypted == 5
        assert stats.packets_decrypted == 5
        assert stats.bytes_encrypted > 0
        assert stats.bytes_decrypted > 0
        
        # Test replay failure
        protocol.check_replay_protection(inbound_sa.spi, 1)  # Should fail
        stats = protocol.get_statistics()
        assert stats.replay_failures == 1
    
    def test_packet_metadata_collection(self, esp_protocol):
        """Test ESP packet metadata collection."""
        protocol = esp_protocol
        
        # Create SA
        sa_data = {
            "spi": 0x12345678,
            "encryption_algorithm": "AES_GCM_16",
            "encryption_key": secrets.token_bytes(32),
            "integrity_algorithm": "HMAC_SHA2_256",
            "integrity_key": secrets.token_bytes(32)
        }
        
        sa = protocol.create_outbound_sa(sa_data)
        
        # Encrypt packet with metadata collection
        plaintext = b"Test packet for metadata"
        encrypted_packet = protocol.encrypt_packet(sa.spi, plaintext)
        
        # Check that metadata was collected
        metadata_list = protocol.get_packet_metadata()
        assert len(metadata_list) == 1
        
        metadata = metadata_list[0]
        assert isinstance(metadata, ESPPacketMetadata)
        assert metadata.spi == sa.spi
        assert metadata.operation == "encrypt"
        assert metadata.packet_size > 0
        assert metadata.processing_time > 0
    
    def test_sa_lookup_and_management(self, esp_protocol):
        """Test SA lookup and management operations."""
        protocol = esp_protocol
        
        # Create multiple SAs
        for i in range(3):
            sa_data = {
                "spi": 0x12345678 + i,
                "encryption_algorithm": "AES_GCM_16",
                "encryption_key": secrets.token_bytes(32),
                "integrity_algorithm": "HMAC_SHA2_256",
                "integrity_key": secrets.token_bytes(32)
            }
            protocol.create_inbound_sa(sa_data)
            protocol.create_outbound_sa(sa_data)
        
        # Test SA lookup
        assert len(protocol.inbound_sas) == 3
        assert len(protocol.outbound_sas) == 3
        
        # Test finding specific SA
        sa = protocol.find_inbound_sa(0x12345678)
        assert sa is not None
        assert sa.spi == 0x12345678
        
        # Test non-existent SA
        sa = protocol.find_inbound_sa(0x99999999)
        assert sa is None
        
        # Test SA removal
        protocol.remove_inbound_sa(0x12345678)
        assert len(protocol.inbound_sas) == 2
        assert protocol.find_inbound_sa(0x12345678) is None
    
    def test_error_handling(self, esp_protocol):
        """Test ESP protocol error handling."""
        protocol = esp_protocol
        
        # Test encryption without SA
        with pytest.raises(ValueError, match="SA not found"):
            protocol.encrypt_packet(0x99999999, b"test data")
        
        # Test decryption without SA
        with pytest.raises(ValueError, match="SA not found"):
            protocol.decrypt_packet(0x99999999, b"encrypted data")
        
        # Test invalid packet data
        sa_data = {
            "spi": 0x12345678,
            "encryption_algorithm": "AES_GCM_16",
            "encryption_key": secrets.token_bytes(32),
            "integrity_algorithm": "HMAC_SHA2_256",
            "integrity_key": secrets.token_bytes(32)
        }
        
        sa = protocol.create_inbound_sa(sa_data)
        
        # Test decryption with invalid data
        with pytest.raises(ValueError, match="Invalid packet data"):
            protocol.decrypt_packet(sa.spi, b"invalid_encrypted_data")
    
    def test_performance_optimization(self, esp_protocol):
        """Test ESP protocol performance optimizations."""
        protocol = esp_protocol
        
        # Create SA
        sa_data = {
            "spi": 0x12345678,
            "encryption_algorithm": "AES_GCM_16",
            "encryption_key": secrets.token_bytes(32),
            "integrity_algorithm": "HMAC_SHA2_256",
            "integrity_key": secrets.token_bytes(32)
        }
        
        outbound_sa = protocol.create_outbound_sa(sa_data)
        inbound_sa = protocol.create_inbound_sa(sa_data)
        
        # Test bulk packet processing
        import time
        start_time = time.time()
        
        for i in range(100):
            plaintext = f"Performance test packet {i}".encode()
            encrypted = protocol.encrypt_packet(outbound_sa.spi, plaintext)
            decrypted = protocol.decrypt_packet(inbound_sa.spi, encrypted)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Should process 100 packets reasonably quickly (less than 1 second)
        assert processing_time < 1.0
        
        # Check statistics
        stats = protocol.get_statistics()
        assert stats.packets_encrypted == 100
        assert stats.packets_decrypted == 100
        
        # Calculate average processing time
        avg_time = processing_time / 100
        assert avg_time < 0.01  # Less than 10ms per packet
    
    def test_cleanup_and_reset(self, esp_protocol):
        """Test ESP protocol cleanup and reset."""
        protocol = esp_protocol
        
        # Create some state
        sa_data = {
            "spi": 0x12345678,
            "encryption_algorithm": "AES_GCM_16",
            "encryption_key": secrets.token_bytes(32),
            "integrity_algorithm": "HMAC_SHA2_256",
            "integrity_key": secrets.token_bytes(32)
        }
        
        protocol.create_inbound_sa(sa_data)
        protocol.create_outbound_sa(sa_data)
        
        # Process some packets
        plaintext = b"Test data"
        encrypted = protocol.encrypt_packet(sa_data["spi"], plaintext)
        
        # Verify state exists
        assert len(protocol.inbound_sas) == 1
        assert len(protocol.outbound_sas) == 1
        stats = protocol.get_statistics()
        assert stats.packets_encrypted == 1
        
        # Perform cleanup
        protocol.cleanup()
        
        # Verify cleanup
        assert len(protocol.inbound_sas) == 0
        assert len(protocol.outbound_sas) == 0
        stats = protocol.get_statistics()
        assert stats.packets_encrypted == 0
        assert stats.packets_decrypted == 0
