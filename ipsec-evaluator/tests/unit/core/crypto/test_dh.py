"""
Unit tests for <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> key exchange implementation.

This module provides comprehensive tests for the DHManager class with focus on:
- MODP finite field groups
- ECP elliptic curve groups (secp and brainpool)
- Modern curves (Curve25519, Curve448)
- Key pair generation
- Shared secret computation
- ANSSI compliance validation
- Group information and metadata
- Error handling and edge cases
"""

import pytest
from unittest.mock import Mock, patch

from ipsecator.core.crypto.dh import (
    DHManager,
    DHGroupType,
    CurveType,
    DHGroupInfo,
)


class TestDHManager:
    """Test suite for DHManager class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.dh_manager = DHManager()

    def test_initialization(self):
        """Test DHManager initialization."""
        assert len(self.dh_manager._supported_groups) > 0

        # Check that all expected group types are present
        group_types = set()
        curve_types = set()

        for info in self.dh_manager._supported_groups.values():
            group_types.add(info.group_type)
            if info.curve_type:
                curve_types.add(info.curve_type)

        assert DHGroupType.MODP in group_types
        assert DHGroupType.ECP in group_types
        assert DHGroupType.CURVE25519 in group_types
        assert DHGroupType.CURVE448 in group_types

        assert CurveType.SECP in curve_types
        assert CurveType.BRAINPOOL in curve_types
        assert CurveType.MONTGOMERY in curve_types
        assert CurveType.EDWARDS in curve_types

    def test_get_supported_groups(self):
        """Test getting list of supported DH groups."""
        groups = self.dh_manager.get_supported_groups()
        assert isinstance(groups, list)
        assert len(groups) > 0

        # Check specific groups are present
        assert 14 in groups  # MODP-2048
        assert 19 in groups  # secp256r1
        assert 28 in groups  # brainpoolP256r1
        assert 31 in groups  # Curve25519

    def test_get_group_info(self):
        """Test getting group information."""
        # Test secp256r1 (group 19)
        info = self.dh_manager.get_group_info(19)
        assert info is not None
        assert info.group_id == 19
        assert info.name == "secp256r1"
        assert info.group_type == DHGroupType.ECP
        assert info.curve_type == CurveType.SECP
        assert info.key_size == 256
        assert info.anssi_approved is True

        # Test brainpoolP256r1 (group 28)
        info = self.dh_manager.get_group_info(28)
        assert info is not None
        assert info.group_id == 28
        assert info.name == "brainpoolP256r1"
        assert info.group_type == DHGroupType.ECP
        assert info.curve_type == CurveType.BRAINPOOL
        assert info.anssi_approved is True

        # Test Curve25519 (group 31)
        info = self.dh_manager.get_group_info(31)
        assert info is not None
        assert info.group_id == 31
        assert info.name == "Curve25519"
        assert info.group_type == DHGroupType.CURVE25519
        assert info.curve_type == CurveType.MONTGOMERY

        # Test non-existent group
        info = self.dh_manager.get_group_info(999)
        assert info is None

    def test_get_anssi_approved_groups(self):
        """Test getting ANSSI-approved groups."""
        anssi_groups = self.dh_manager.get_anssi_approved_groups()
        assert isinstance(anssi_groups, list)
        assert len(anssi_groups) > 0

        # Check specific ANSSI-approved groups
        assert 19 in anssi_groups  # secp256r1
        assert 20 in anssi_groups  # secp384r1
        assert 21 in anssi_groups  # secp521r1
        assert 28 in anssi_groups  # brainpoolP256r1
        assert 29 in anssi_groups  # brainpoolP384r1
        assert 30 in anssi_groups  # brainpoolP512r1

    def test_get_groups_by_type(self):
        """Test getting groups by type."""
        # Test MODP groups
        modp_groups = self.dh_manager.get_groups_by_type(DHGroupType.MODP)
        assert isinstance(modp_groups, list)
        assert 14 in modp_groups  # MODP-2048
        assert 15 in modp_groups  # MODP-3072
        assert 16 in modp_groups  # MODP-4096

        # Test ECP groups
        ecp_groups = self.dh_manager.get_groups_by_type(DHGroupType.ECP)
        assert isinstance(ecp_groups, list)
        assert 19 in ecp_groups  # secp256r1
        assert 28 in ecp_groups  # brainpoolP256r1

        # Test Curve25519
        curve25519_groups = self.dh_manager.get_groups_by_type(DHGroupType.CURVE25519)
        assert 31 in curve25519_groups

        # Test Curve448
        curve448_groups = self.dh_manager.get_groups_by_type(DHGroupType.CURVE448)
        assert 32 in curve448_groups

    def test_get_groups_by_curve_type(self):
        """Test getting groups by curve type."""
        # Test secp curves
        secp_groups = self.dh_manager.get_groups_by_curve_type(CurveType.SECP)
        assert isinstance(secp_groups, list)
        assert 19 in secp_groups  # secp256r1
        assert 20 in secp_groups  # secp384r1
        assert 21 in secp_groups  # secp521r1

        # Test brainpool curves
        brainpool_groups = self.dh_manager.get_groups_by_curve_type(CurveType.BRAINPOOL)
        assert isinstance(brainpool_groups, list)
        assert 28 in brainpool_groups  # brainpoolP256r1
        assert 29 in brainpool_groups  # brainpoolP384r1
        assert 30 in brainpool_groups  # brainpoolP512r1

        # Test Montgomery curves
        montgomery_groups = self.dh_manager.get_groups_by_curve_type(CurveType.MONTGOMERY)
        assert 31 in montgomery_groups  # Curve25519

        # Test Edwards curves
        edwards_groups = self.dh_manager.get_groups_by_curve_type(CurveType.EDWARDS)
        assert 32 in edwards_groups  # Curve448

    def test_validate_group(self):
        """Test group validation."""
        # Valid groups
        assert self.dh_manager.validate_group(19) is True  # secp256r1
        assert self.dh_manager.validate_group(28) is True  # brainpoolP256r1
        assert self.dh_manager.validate_group(31) is True  # Curve25519

        # Invalid groups
        assert self.dh_manager.validate_group(999) is False
        assert self.dh_manager.validate_group(-1) is False

    def test_is_anssi_approved(self):
        """Test ANSSI approval checking."""
        # ANSSI-approved groups
        assert self.dh_manager.is_anssi_approved(19) is True   # secp256r1
        assert self.dh_manager.is_anssi_approved(28) is True   # brainpoolP256r1

        # Non-ANSSI-approved groups
        assert self.dh_manager.is_anssi_approved(25) is False  # secp192r1
        assert self.dh_manager.is_anssi_approved(31) is False  # Curve25519

        # Non-existent group
        assert self.dh_manager.is_anssi_approved(999) is False

    def test_generate_ecp_keypair(self):
        """Test ECP key pair generation."""
        # Test secp256r1
        private_key, public_key = self.dh_manager.generate_keypair(19)
        assert isinstance(private_key, bytes)
        assert isinstance(public_key, bytes)
        assert len(private_key) > 0
        assert len(public_key) > 0

        # Keys should be different each time
        private_key2, public_key2 = self.dh_manager.generate_keypair(19)
        assert private_key != private_key2
        assert public_key != public_key2

        # Test brainpoolP256r1
        private_key_bp, public_key_bp = self.dh_manager.generate_keypair(28)
        assert isinstance(private_key_bp, bytes)
        assert isinstance(public_key_bp, bytes)
        assert len(private_key_bp) > 0
        assert len(public_key_bp) > 0

    def test_generate_curve25519_keypair(self):
        """Test Curve25519 key pair generation."""
        private_key, public_key = self.dh_manager.generate_keypair(31)
        assert isinstance(private_key, bytes)
        assert isinstance(public_key, bytes)
        assert len(private_key) == 32  # Curve25519 private key size
        assert len(public_key) == 32   # Curve25519 public key size

        # Keys should be different each time
        private_key2, public_key2 = self.dh_manager.generate_keypair(31)
        assert private_key != private_key2
        assert public_key != public_key2

    def test_generate_curve448_keypair(self):
        """Test Curve448 key pair generation."""
        private_key, public_key = self.dh_manager.generate_keypair(32)
        assert isinstance(private_key, bytes)
        assert isinstance(public_key, bytes)
        assert len(private_key) == 56  # Curve448 private key size
        assert len(public_key) == 56   # Curve448 public key size

    def test_generate_modp_keypair(self):
        """Test MODP key pair generation."""
        # Test MODP-2048 (group 14)
        private_key, public_key = self.dh_manager.generate_keypair(14)
        assert isinstance(private_key, bytes)
        assert isinstance(public_key, bytes)
        assert len(private_key) > 0
        assert len(public_key) > 0

    def test_compute_ecp_shared_secret(self):
        """Test ECP shared secret computation."""
        # Generate two key pairs for secp256r1
        private_key1, public_key1 = self.dh_manager.generate_keypair(19)
        private_key2, public_key2 = self.dh_manager.generate_keypair(19)

        # Compute shared secrets
        shared_secret1 = self.dh_manager.compute_shared_secret(19, private_key1, public_key2)
        shared_secret2 = self.dh_manager.compute_shared_secret(19, private_key2, public_key1)

        # Shared secrets should be identical
        assert shared_secret1 == shared_secret2
        assert len(shared_secret1) > 0

    def test_compute_curve25519_shared_secret(self):
        """Test Curve25519 shared secret computation."""
        # Generate two key pairs
        private_key1, public_key1 = self.dh_manager.generate_keypair(31)
        private_key2, public_key2 = self.dh_manager.generate_keypair(31)

        # Compute shared secrets
        shared_secret1 = self.dh_manager.compute_shared_secret(31, private_key1, public_key2)
        shared_secret2 = self.dh_manager.compute_shared_secret(31, private_key2, public_key1)

        # Shared secrets should be identical
        assert shared_secret1 == shared_secret2
        assert len(shared_secret1) == 32  # Curve25519 shared secret size

    def test_compute_curve448_shared_secret(self):
        """Test Curve448 shared secret computation."""
        # Generate two key pairs
        private_key1, public_key1 = self.dh_manager.generate_keypair(32)
        private_key2, public_key2 = self.dh_manager.generate_keypair(32)

        # Compute shared secrets
        shared_secret1 = self.dh_manager.compute_shared_secret(32, private_key1, public_key2)
        shared_secret2 = self.dh_manager.compute_shared_secret(32, private_key2, public_key1)

        # Shared secrets should be identical
        assert shared_secret1 == shared_secret2
        assert len(shared_secret1) == 56  # Curve448 shared secret size

    def test_unsupported_group_errors(self):
        """Test error handling for unsupported groups."""
        with pytest.raises(ValueError, match="Unsupported DH group"):
            self.dh_manager.generate_keypair(999)

        with pytest.raises(ValueError, match="Unsupported DH group"):
            self.dh_manager.compute_shared_secret(999, b"private", b"public")

    def test_get_curve_info(self):
        """Test getting detailed curve information."""
        # Test secp256r1
        info = self.dh_manager.get_curve_info(19)
        assert info["group_id"] == 19
        assert info["name"] == "secp256r1"
        assert info["type"] == "ecp"
        assert info["curve_type"] == "secp"
        assert info["key_size"] == 256
        assert info["anssi_approved"] is True
        assert "curve_class" in info
        # field_size should be present unless there's an error
        if "curve_error" in info:
            # If there's an error, log it but don't fail the test
            print(f"Curve error for {info['name']}: {info['curve_error']}")
        else:
            assert "field_size" in info

        # Test Curve25519
        info_25519 = self.dh_manager.get_curve_info(31)
        assert info_25519["group_id"] == 31
        assert info_25519["name"] == "Curve25519"
        assert info_25519["type"] == "curve25519"
        assert info_25519["curve_type"] == "montgomery"

        # Test unsupported group
        with pytest.raises(ValueError, match="Unsupported DH group"):
            self.dh_manager.get_curve_info(999)

    def test_get_statistics(self):
        """Test getting DH manager statistics."""
        stats = self.dh_manager.get_statistics()

        assert "total_groups" in stats
        assert "anssi_approved" in stats
        assert "by_type" in stats
        assert "by_curve_type" in stats
        assert "key_sizes" in stats

        assert stats["total_groups"] > 0
        assert stats["anssi_approved"] > 0

        # Check type counts
        assert "modp" in stats["by_type"]
        assert "ecp" in stats["by_type"]
        assert "curve25519" in stats["by_type"]
        assert "curve448" in stats["by_type"]

        # Check curve type counts
        assert "secp" in stats["by_curve_type"]
        assert "brainpool" in stats["by_curve_type"]
        assert "montgomery" in stats["by_curve_type"]
        assert "edwards" in stats["by_curve_type"]

        # Check key sizes
        assert isinstance(stats["key_sizes"], list)
        assert len(stats["key_sizes"]) > 0
        assert 256 in stats["key_sizes"]  # secp256r1, brainpoolP256r1
