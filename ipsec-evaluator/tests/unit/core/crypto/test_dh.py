"""
Unit tests for enhanced DH manager.

Tests the comprehensive DH group support including secp and brainpool curves,
based on the ipsecdr test patterns but enhanced for the new architecture.
"""

import pytest
import secrets
from typing import List

from ipsec_evaluator.core.crypto.dh import (
    EnhancedDHManager,
    DHGroupInfo,
    DHGroupType,
    CurveType
)


class TestEnhancedDHManager:
    """Test suite for the Enhanced DH Manager."""
    
    def test_initialization(self, dh_manager):
        """Test DH manager initialization."""
        assert isinstance(dh_manager, EnhancedDHManager)
        assert len(dh_manager.get_supported_groups()) > 0
        
        # Check that we have the expected number of groups
        supported_groups = dh_manager.get_supported_groups()
        assert len(supported_groups) >= 16  # Should have at least 16 groups
    
    def test_supported_groups(self, dh_manager):
        """Test that all expected DH groups are supported."""
        supported_groups = dh_manager.get_supported_groups()
        
        # Check for essential MODP groups
        assert 14 in supported_groups  # MODP-2048
        assert 15 in supported_groups  # MODP-3072
        assert 16 in supported_groups  # MODP-4096
        
        # Check for essential secp curves
        assert 19 in supported_groups  # secp256r1
        assert 20 in supported_groups  # secp384r1
        assert 21 in supported_groups  # secp521r1
        
        # Check for brainpool curves
        assert 28 in supported_groups  # brainpoolP256r1
        assert 29 in supported_groups  # brainpoolP384r1
        assert 30 in supported_groups  # brainpoolP512r1
        
        # Check for modern curves
        assert 31 in supported_groups  # Curve25519
        assert 32 in supported_groups  # Curve448
    
    def test_anssi_approved_groups(self, dh_manager):
        """Test ANSSI-approved DH groups."""
        anssi_groups = dh_manager.get_anssi_approved_groups()
        
        # Should have exactly 7 ANSSI-approved groups
        assert len(anssi_groups) == 7
        
        # Check specific ANSSI-approved groups
        expected_anssi = [19, 20, 21, 27, 28, 29, 30]
        for group in expected_anssi:
            assert group in anssi_groups
            assert dh_manager.is_anssi_approved(group)
    
    def test_group_info(self, dh_manager):
        """Test DH group information retrieval."""
        # Test secp256r1 (group 19)
        info = dh_manager.get_group_info(19)
        assert info is not None
        assert info.name == "secp256r1"
        assert info.group_type == DHGroupType.ECP
        assert info.curve_type == CurveType.SECP
        assert info.key_size == 256
        assert info.anssi_approved is True
        
        # Test brainpoolP256r1 (group 28)
        info = dh_manager.get_group_info(28)
        assert info is not None
        assert info.name == "brainpoolP256r1"
        assert info.group_type == DHGroupType.ECP
        assert info.curve_type == CurveType.BRAINPOOL
        assert info.key_size == 256
        assert info.anssi_approved is True
        
        # Test Curve25519 (group 31)
        info = dh_manager.get_group_info(31)
        assert info is not None
        assert info.name == "Curve25519"
        assert info.group_type == DHGroupType.CURVE25519
        assert info.curve_type == CurveType.MONTGOMERY
        assert info.key_size == 255
        assert info.anssi_approved is False
    
    def test_groups_by_type(self, dh_manager):
        """Test filtering groups by type."""
        # Test ECP groups
        ecp_groups = dh_manager.get_groups_by_type(DHGroupType.ECP)
        assert len(ecp_groups) == 9  # Should have 9 ECP groups
        assert 19 in ecp_groups  # secp256r1
        assert 28 in ecp_groups  # brainpoolP256r1
        
        # Test MODP groups
        modp_groups = dh_manager.get_groups_by_type(DHGroupType.MODP)
        assert len(modp_groups) == 5  # Should have 5 MODP groups
        assert 14 in modp_groups  # MODP-2048
        assert 16 in modp_groups  # MODP-4096
        
        # Test modern curves
        curve25519_groups = dh_manager.get_groups_by_type(DHGroupType.CURVE25519)
        assert len(curve25519_groups) == 1
        assert 31 in curve25519_groups
        
        curve448_groups = dh_manager.get_groups_by_type(DHGroupType.CURVE448)
        assert len(curve448_groups) == 1
        assert 32 in curve448_groups
    
    def test_groups_by_curve_type(self, dh_manager):
        """Test filtering groups by curve type."""
        # Test secp curves
        secp_groups = dh_manager.get_groups_by_curve_type(CurveType.SECP)
        assert len(secp_groups) == 5  # Should have 5 secp curves
        assert 19 in secp_groups  # secp256r1
        assert 20 in secp_groups  # secp384r1
        assert 21 in secp_groups  # secp521r1
        
        # Test brainpool curves
        brainpool_groups = dh_manager.get_groups_by_curve_type(CurveType.BRAINPOOL)
        assert len(brainpool_groups) == 4  # Should have 4 brainpool curves
        assert 27 in brainpool_groups  # brainpoolP224r1
        assert 28 in brainpool_groups  # brainpoolP256r1
        assert 29 in brainpool_groups  # brainpoolP384r1
        assert 30 in brainpool_groups  # brainpoolP512r1
        
        # Test modern curve types
        montgomery_groups = dh_manager.get_groups_by_curve_type(CurveType.MONTGOMERY)
        assert len(montgomery_groups) == 1
        assert 31 in montgomery_groups  # Curve25519
        
        edwards_groups = dh_manager.get_groups_by_curve_type(CurveType.EDWARDS)
        assert len(edwards_groups) == 1
        assert 32 in edwards_groups  # Curve448
    
    @pytest.mark.parametrize("group_id", [19, 28, 20, 29, 31, 32])
    def test_keypair_generation(self, dh_manager, group_id):
        """Test DH keypair generation for various groups."""
        private_key, public_key = dh_manager.generate_keypair(group_id)
        
        # Basic validation
        assert isinstance(private_key, bytes)
        assert isinstance(public_key, bytes)
        assert len(private_key) > 0
        assert len(public_key) > 0
        
        # Keys should be different
        assert private_key != public_key
        
        # Test that we can generate multiple different keypairs
        private_key2, public_key2 = dh_manager.generate_keypair(group_id)
        assert private_key != private_key2
        assert public_key != public_key2
    
    @pytest.mark.parametrize("group_id", [19, 28, 20, 29])
    def test_shared_secret_computation(self, dh_manager, group_id):
        """Test DH shared secret computation for ECP groups."""
        # Generate keypairs for Alice and Bob
        alice_private, alice_public = dh_manager.generate_keypair(group_id)
        bob_private, bob_public = dh_manager.generate_keypair(group_id)
        
        # Compute shared secrets
        alice_shared = dh_manager.compute_shared_secret(group_id, alice_private, bob_public)
        bob_shared = dh_manager.compute_shared_secret(group_id, bob_private, alice_public)
        
        # Shared secrets should match
        assert alice_shared == bob_shared
        assert len(alice_shared) > 0
        
        # Shared secret should be different from public keys
        assert alice_shared != alice_public
        assert alice_shared != bob_public
    
    def test_curve25519_operations(self, dh_manager):
        """Test Curve25519 specific operations."""
        group_id = 31
        
        # Generate keypairs
        alice_private, alice_public = dh_manager.generate_keypair(group_id)
        bob_private, bob_public = dh_manager.generate_keypair(group_id)
        
        # Curve25519 keys should be 32 bytes
        assert len(alice_private) == 32
        assert len(alice_public) == 32
        assert len(bob_private) == 32
        assert len(bob_public) == 32
        
        # Compute shared secret
        alice_shared = dh_manager.compute_shared_secret(group_id, alice_private, bob_public)
        bob_shared = dh_manager.compute_shared_secret(group_id, bob_private, alice_public)
        
        # Shared secrets should match and be 32 bytes
        assert alice_shared == bob_shared
        assert len(alice_shared) == 32
    
    def test_curve448_operations(self, dh_manager):
        """Test Curve448 specific operations."""
        group_id = 32
        
        # Generate keypairs
        alice_private, alice_public = dh_manager.generate_keypair(group_id)
        bob_private, bob_public = dh_manager.generate_keypair(group_id)
        
        # Curve448 keys should be 56 bytes
        assert len(alice_private) == 56
        assert len(alice_public) == 56
        assert len(bob_private) == 56
        assert len(bob_public) == 56
        
        # Compute shared secret
        alice_shared = dh_manager.compute_shared_secret(group_id, alice_private, bob_public)
        bob_shared = dh_manager.compute_shared_secret(group_id, bob_private, alice_public)
        
        # Shared secrets should match and be 56 bytes
        assert alice_shared == bob_shared
        assert len(alice_shared) == 56
    
    def test_invalid_group_handling(self, dh_manager):
        """Test handling of invalid DH groups."""
        invalid_group = 999
        
        # Should raise ValueError for unsupported groups
        with pytest.raises(ValueError, match="Unsupported DH group"):
            dh_manager.generate_keypair(invalid_group)
        
        with pytest.raises(ValueError, match="Unsupported DH group"):
            dh_manager.compute_shared_secret(invalid_group, b"fake_key", b"fake_public")
        
        # Validation should return False
        assert not dh_manager.validate_group(invalid_group)
        assert not dh_manager.is_anssi_approved(invalid_group)
        
        # Group info should return None
        assert dh_manager.get_group_info(invalid_group) is None
    
    def test_statistics(self, dh_manager):
        """Test DH manager statistics."""
        stats = dh_manager.get_statistics()
        
        # Check basic statistics structure
        assert "total_groups" in stats
        assert "anssi_approved" in stats
        assert "by_type" in stats
        assert "by_curve_type" in stats
        assert "key_sizes" in stats
        
        # Validate statistics values
        assert stats["total_groups"] == 16
        assert stats["anssi_approved"] == 7
        
        # Check type distribution
        assert stats["by_type"]["modp"] == 5
        assert stats["by_type"]["ecp"] == 9
        assert stats["by_type"]["curve25519"] == 1
        assert stats["by_type"]["curve448"] == 1
        
        # Check curve type distribution
        assert stats["by_curve_type"]["secp"] == 5
        assert stats["by_curve_type"]["brainpool"] == 4
        assert stats["by_curve_type"]["montgomery"] == 1
        assert stats["by_curve_type"]["edwards"] == 1
        
        # Check key sizes
        expected_sizes = [192, 224, 255, 256, 384, 448, 512, 521, 2048, 3072, 4096, 6144, 8192]
        assert sorted(stats["key_sizes"]) == sorted(expected_sizes)
    
    def test_curve_info_detailed(self, dh_manager):
        """Test detailed curve information retrieval."""
        # Test secp256r1
        info = dh_manager.get_curve_info(19)
        assert info["name"] == "secp256r1"
        assert info["type"] == "ecp"
        assert info["curve_type"] == "secp"
        assert info["key_size"] == 256
        assert info["anssi_approved"] is True
        assert "curve_class" in info
        assert "field_size" in info
        
        # Test brainpoolP256r1
        info = dh_manager.get_curve_info(28)
        assert info["name"] == "brainpoolP256r1"
        assert info["curve_type"] == "brainpool"
        assert info["anssi_approved"] is True
        
        # Test Curve25519
        info = dh_manager.get_curve_info(31)
        assert info["name"] == "Curve25519"
        assert info["curve_type"] == "montgomery"
        assert info["anssi_approved"] is False
