"""
Unit tests for ECDSA (Elliptic Curve Digital Signature Algorithm) implementation.

This module provides comprehensive tests for the ECDSA implementation including:
- Key pair generation for multiple curves
- Message signing and verification
- Hash-based signing and verification
- Multiple hash algorithms (SHA-256, SHA-384, SHA-512)
- Curve information and metadata
- Edge cases and error handling
- ANSSI compliance validation
"""

import pytest
import hashlib
import secrets

from ipsecator.core.crypto.ecdsa import ECDSAManager


class TestECDSAManager:
    """Test suite for ECDSAManager class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.ecdsa_manager = ECDSAManager()

    def test_initialization(self):
        """Test ECDSAManager initialization."""
        assert len(self.ecdsa_manager._supported_curves) > 0
        assert len(self.ecdsa_manager._supported_hashes) > 0
        
        # Check specific curves are supported
        curves = self.ecdsa_manager.get_supported_curves()
        assert "secp256r1" in curves
        assert "secp384r1" in curves
        assert "secp521r1" in curves
        assert "brainpoolP256r1" in curves
        assert "brainpoolP384r1" in curves
        assert "brainpoolP512r1" in curves
        
        # Check specific hash algorithms are supported
        hashes = self.ecdsa_manager.get_supported_hashes()
        assert "SHA256" in hashes
        assert "SHA384" in hashes
        assert "SHA512" in hashes

    def test_generate_keypair_secp256r1(self):
        """Test key pair generation for secp256r1."""
        private_key, public_key = self.ecdsa_manager.generate_keypair("secp256r1")
        
        assert isinstance(private_key, bytes)
        assert isinstance(public_key, bytes)
        assert len(private_key) > 0
        assert len(public_key) > 0
        
        # Keys should be different each time
        private_key2, public_key2 = self.ecdsa_manager.generate_keypair("secp256r1")
        assert private_key != private_key2
        assert public_key != public_key2

    def test_generate_keypair_all_curves(self):
        """Test key pair generation for all supported curves."""
        curves = self.ecdsa_manager.get_supported_curves()
        
        for curve_name in curves:
            private_key, public_key = self.ecdsa_manager.generate_keypair(curve_name)
            assert isinstance(private_key, bytes)
            assert isinstance(public_key, bytes)
            assert len(private_key) > 0
            assert len(public_key) > 0

    def test_generate_keypair_unsupported_curve(self):
        """Test error handling for unsupported curves."""
        with pytest.raises(ValueError, match="Unsupported curve"):
            self.ecdsa_manager.generate_keypair("unsupported_curve")

    def test_sign_verify_message_sha256(self):
        """Test message signing and verification with SHA-256."""
        private_key, public_key = self.ecdsa_manager.generate_keypair("secp256r1")
        message = b"Hello, ECDSA with SHA-256!"
        
        # Sign message
        signature = self.ecdsa_manager.sign_message(private_key, message, "SHA256")
        assert isinstance(signature, bytes)
        assert len(signature) > 0
        
        # Verify signature
        is_valid = self.ecdsa_manager.verify_signature(public_key, message, signature, "SHA256")
        assert is_valid is True
        
        # Verify with wrong message
        wrong_message = b"Wrong message"
        is_valid_wrong = self.ecdsa_manager.verify_signature(public_key, wrong_message, signature, "SHA256")
        assert is_valid_wrong is False

    def test_sign_verify_message_all_hashes(self):
        """Test message signing and verification with all hash algorithms."""
        private_key, public_key = self.ecdsa_manager.generate_keypair("secp384r1")
        message = b"Test message for all hash algorithms"
        
        hash_algorithms = self.ecdsa_manager.get_supported_hashes()
        
        for hash_alg in hash_algorithms:
            # Sign message
            signature = self.ecdsa_manager.sign_message(private_key, message, hash_alg)
            assert isinstance(signature, bytes)
            assert len(signature) > 0
            
            # Verify signature
            is_valid = self.ecdsa_manager.verify_signature(public_key, message, signature, hash_alg)
            assert is_valid is True

    def test_sign_verify_different_curves(self):
        """Test signing and verification with different curves."""
        message = b"Test message for different curves"
        curves = self.ecdsa_manager.get_supported_curves()
        
        for curve_name in curves:
            private_key, public_key = self.ecdsa_manager.generate_keypair(curve_name)
            
            # Sign message
            signature = self.ecdsa_manager.sign_message(private_key, message)
            
            # Verify signature
            is_valid = self.ecdsa_manager.verify_signature(public_key, message, signature)
            assert is_valid is True, f"Signature verification failed for curve {curve_name}"

    def test_sign_verify_hash_sha256(self):
        """Test hash signing and verification with SHA-256."""
        private_key, public_key = self.ecdsa_manager.generate_keypair("secp256r1")
        message = b"Message to hash and sign"
        
        # Compute hash
        message_hash = hashlib.sha256(message).digest()
        
        # Sign hash
        signature = self.ecdsa_manager.sign_hash(private_key, message_hash, "SHA256")
        assert isinstance(signature, bytes)
        assert len(signature) > 0
        
        # Verify hash signature
        is_valid = self.ecdsa_manager.verify_hash_signature(public_key, message_hash, signature, "SHA256")
        assert is_valid is True
        
        # Verify with wrong hash
        wrong_hash = hashlib.sha256(b"wrong message").digest()
        is_valid_wrong = self.ecdsa_manager.verify_hash_signature(public_key, wrong_hash, signature, "SHA256")
        assert is_valid_wrong is False

    def test_sign_verify_hash_all_algorithms(self):
        """Test hash signing and verification with all hash algorithms."""
        private_key, public_key = self.ecdsa_manager.generate_keypair("secp521r1")
        message = b"Test message for hash signing"
        
        hash_functions = {
            "SHA256": hashlib.sha256,
            "SHA384": hashlib.sha384,
            "SHA512": hashlib.sha512,
        }
        
        for hash_alg, hash_func in hash_functions.items():
            # Compute hash
            message_hash = hash_func(message).digest()
            
            # Sign hash
            signature = self.ecdsa_manager.sign_hash(private_key, message_hash, hash_alg)
            
            # Verify hash signature
            is_valid = self.ecdsa_manager.verify_hash_signature(public_key, message_hash, signature, hash_alg)
            assert is_valid is True, f"Hash signature verification failed for {hash_alg}"

    def test_unsupported_hash_algorithm(self):
        """Test error handling for unsupported hash algorithms."""
        private_key, public_key = self.ecdsa_manager.generate_keypair("secp256r1")
        message = b"test message"
        
        with pytest.raises(ValueError, match="Unsupported hash algorithm"):
            self.ecdsa_manager.sign_message(private_key, message, "UNSUPPORTED_HASH")
        
        with pytest.raises(ValueError, match="Unsupported hash algorithm"):
            self.ecdsa_manager.verify_signature(public_key, message, b"signature", "UNSUPPORTED_HASH")

    def test_get_public_key_from_private(self):
        """Test extracting public key from private key."""
        private_key, original_public_key = self.ecdsa_manager.generate_keypair("secp256r1")
        
        # Extract public key from private key
        extracted_public_key = self.ecdsa_manager.get_public_key_from_private(private_key)
        
        assert isinstance(extracted_public_key, bytes)
        assert extracted_public_key == original_public_key

    def test_get_curve_info(self):
        """Test getting curve information."""
        # Test secp256r1
        info = self.ecdsa_manager.get_curve_info("secp256r1")
        assert info["name"] == "secp256r1"
        assert info["curve_class"] == "SECP256R1"
        assert info["key_size"] == 256
        assert info["anssi_approved"] is True
        
        # Test brainpoolP256r1
        info_bp = self.ecdsa_manager.get_curve_info("brainpoolP256r1")
        assert info_bp["name"] == "brainpoolP256r1"
        assert info_bp["curve_class"] == "BrainpoolP256R1"
        assert info_bp["key_size"] == 256
        assert info_bp["anssi_approved"] is True
        
        # Test unsupported curve
        with pytest.raises(ValueError, match="Unsupported curve"):
            self.ecdsa_manager.get_curve_info("unsupported_curve")

    def test_signature_deterministic_behavior(self):
        """Test that signatures are different for same message (due to randomness)."""
        private_key, public_key = self.ecdsa_manager.generate_keypair("secp256r1")
        message = b"Test message for deterministic behavior"
        
        # Sign same message multiple times
        signature1 = self.ecdsa_manager.sign_message(private_key, message)
        signature2 = self.ecdsa_manager.sign_message(private_key, message)
        signature3 = self.ecdsa_manager.sign_message(private_key, message)
        
        # Signatures should be different (due to random k)
        assert signature1 != signature2
        assert signature2 != signature3
        assert signature1 != signature3
        
        # But all should verify correctly
        assert self.ecdsa_manager.verify_signature(public_key, message, signature1) is True
        assert self.ecdsa_manager.verify_signature(public_key, message, signature2) is True
        assert self.ecdsa_manager.verify_signature(public_key, message, signature3) is True

    def test_cross_curve_signature_failure(self):
        """Test that signatures from one curve don't verify on another."""
        # Generate keys for different curves
        private_key1, public_key1 = self.ecdsa_manager.generate_keypair("secp256r1")
        private_key2, public_key2 = self.ecdsa_manager.generate_keypair("secp384r1")
        
        message = b"Cross-curve test message"
        
        # Sign with first key
        signature1 = self.ecdsa_manager.sign_message(private_key1, message)
        
        # Should verify with correct public key
        assert self.ecdsa_manager.verify_signature(public_key1, message, signature1) is True
        
        # Should not verify with wrong public key (different curve)
        assert self.ecdsa_manager.verify_signature(public_key2, message, signature1) is False

    def test_large_message_signing(self):
        """Test signing and verification of large messages."""
        private_key, public_key = self.ecdsa_manager.generate_keypair("secp256r1")
        
        # Create large message (1MB)
        large_message = secrets.token_bytes(1024 * 1024)
        
        # Sign large message
        signature = self.ecdsa_manager.sign_message(private_key, large_message)
        
        # Verify signature
        is_valid = self.ecdsa_manager.verify_signature(public_key, large_message, signature)
        assert is_valid is True

    def test_empty_message_signing(self):
        """Test signing and verification of empty messages."""
        private_key, public_key = self.ecdsa_manager.generate_keypair("secp256r1")
        
        # Empty message
        empty_message = b""
        
        # Sign empty message
        signature = self.ecdsa_manager.sign_message(private_key, empty_message)
        
        # Verify signature
        is_valid = self.ecdsa_manager.verify_signature(public_key, empty_message, signature)
        assert is_valid is True

    def test_brainpool_curves_anssi_compliance(self):
        """Test ANSSI compliance for brainpool curves."""
        brainpool_curves = ["brainpoolP256r1", "brainpoolP384r1", "brainpoolP512r1"]
        
        for curve_name in brainpool_curves:
            info = self.ecdsa_manager.get_curve_info(curve_name)
            assert info["anssi_approved"] is True, f"Brainpool curve {curve_name} should be ANSSI approved"
            
            # Test signing with brainpool curve
            private_key, public_key = self.ecdsa_manager.generate_keypair(curve_name)
            message = f"ANSSI compliance test for {curve_name}".encode()
            
            signature = self.ecdsa_manager.sign_message(private_key, message)
            is_valid = self.ecdsa_manager.verify_signature(public_key, message, signature)
            assert is_valid is True, f"Signature verification failed for ANSSI curve {curve_name}"
