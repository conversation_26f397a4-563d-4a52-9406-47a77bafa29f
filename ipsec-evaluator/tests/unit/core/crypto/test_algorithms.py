"""
Unit tests for cryptographic algorithms.

This module provides comprehensive tests for encryption, integrity, and PRF algorithms
with focus on:
- AES-GCM (AEAD) encryption
- AES-CBC (traditional) encryption
- ChaCha20-Poly1305 (AEAD) encryption
- HMAC-SHA2 integrity algorithms
- PRF algorithms for key derivation
- Edge cases and error handling
"""

import pytest
import secrets
from cryptography.hazmat.primitives import hashes

from ipsecator.core.crypto.algorithms import (
    EncryptionAlgorithm,
    IntegrityAlgorithm,
    PRFAlgorithm,
)


class TestEncryptionAlgorithm:
    """Test suite for encryption algorithms."""

    def test_aes_gcm_16_encryption_decryption(self):
        """Test AES-GCM-16 AEAD encryption and decryption."""
        alg = EncryptionAlgorithm(
            name="AES_GCM_16",
            algorithm_id=20,
            key_sizes=[128, 192, 256],
            block_size=16,
            iv_size=8,
            is_aead=True,
        )

        # Test with 256-bit key
        key = secrets.token_bytes(32)  # 256 bits
        plaintext = b"Hello, AES-GCM world! This is a test message."
        iv = secrets.token_bytes(8)
        aad = b"Additional authenticated data"

        # Encrypt
        ciphertext = alg.encrypt(key, plaintext, iv, aad)
        assert len(ciphertext) > len(plaintext)  # Should include auth tag

        # Decrypt
        decrypted = alg.decrypt(key, ciphertext, iv, aad)
        assert decrypted == plaintext

    def test_aes_gcm_16_authentication_failure(self):
        """Test AES-GCM-16 authentication failure with tampered data."""
        alg = EncryptionAlgorithm(
            name="AES_GCM_16",
            algorithm_id=20,
            key_sizes=[128, 192, 256],
            block_size=16,
            iv_size=8,
            is_aead=True,
        )

        key = secrets.token_bytes(32)
        plaintext = b"Secret message"
        iv = secrets.token_bytes(8)
        aad = b"AAD data"

        ciphertext = alg.encrypt(key, plaintext, iv, aad)

        # Tamper with ciphertext
        tampered_ciphertext = ciphertext[:-1] + b"\x00"

        # Should raise authentication error
        with pytest.raises(Exception):  # cryptography raises InvalidTag
            alg.decrypt(key, tampered_ciphertext, iv, aad)

    def test_aes_cbc_encryption_decryption(self):
        """Test AES-CBC traditional encryption and decryption."""
        alg = EncryptionAlgorithm(
            name="AES_CBC",
            algorithm_id=12,
            key_sizes=[128, 192, 256],
            block_size=16,
            iv_size=16,
            is_aead=False,
        )

        # Test with 256-bit key
        key = secrets.token_bytes(32)
        plaintext = b"Hello, AES-CBC world! This message needs padding."
        iv = secrets.token_bytes(16)

        # Encrypt
        ciphertext = alg.encrypt(key, plaintext, iv)
        assert len(ciphertext) % 16 == 0  # Should be block-aligned

        # Decrypt
        decrypted = alg.decrypt(key, ciphertext, iv)
        assert decrypted == plaintext

    def test_aes_cbc_padding(self):
        """Test AES-CBC PKCS7 padding behavior."""
        alg = EncryptionAlgorithm(
            name="AES_CBC",
            algorithm_id=12,
            key_sizes=[128, 192, 256],
            block_size=16,
            iv_size=16,
            is_aead=False,
        )

        key = secrets.token_bytes(32)
        iv = secrets.token_bytes(16)

        # Test with exact block size (should add full padding block)
        plaintext_16 = b"A" * 16
        ciphertext_16 = alg.encrypt(key, plaintext_16, iv)
        assert len(ciphertext_16) == 32  # 16 + 16 padding

        decrypted_16 = alg.decrypt(key, ciphertext_16, iv)
        assert decrypted_16 == plaintext_16

        # Test with partial block
        plaintext_10 = b"A" * 10
        ciphertext_10 = alg.encrypt(key, plaintext_10, iv)
        assert len(ciphertext_10) == 16  # Padded to 16

        decrypted_10 = alg.decrypt(key, ciphertext_10, iv)
        assert decrypted_10 == plaintext_10

    def test_chacha20_poly1305_encryption_decryption(self):
        """Test ChaCha20-Poly1305 AEAD encryption and decryption."""
        alg = EncryptionAlgorithm(
            name="CHACHA20_POLY1305",
            algorithm_id=28,
            key_sizes=[256],
            block_size=1,
            iv_size=12,  # ChaCha20-Poly1305 requires 12-byte nonce
            is_aead=True,
        )

        key = secrets.token_bytes(32)  # 256 bits
        plaintext = b"Hello, ChaCha20-Poly1305! This is a test."
        iv = secrets.token_bytes(12)  # 12-byte nonce
        aad = b"Additional data for ChaCha20"

        # Encrypt
        ciphertext = alg.encrypt(key, plaintext, iv, aad)
        assert len(ciphertext) > len(plaintext)

        # Decrypt
        decrypted = alg.decrypt(key, ciphertext, iv, aad)
        assert decrypted == plaintext

    def test_unsupported_aead_algorithm(self):
        """Test error handling for unsupported AEAD algorithms."""
        alg = EncryptionAlgorithm(
            name="UNSUPPORTED_AEAD",
            algorithm_id=999,
            key_sizes=[256],
            block_size=16,
            iv_size=8,
            is_aead=True,
        )

        key = secrets.token_bytes(32)
        plaintext = b"test"
        iv = secrets.token_bytes(8)

        with pytest.raises(ValueError, match="Unsupported AEAD algorithm"):
            alg.encrypt(key, plaintext, iv)

    def test_unsupported_traditional_algorithm(self):
        """Test error handling for unsupported traditional algorithms."""
        alg = EncryptionAlgorithm(
            name="UNSUPPORTED_TRADITIONAL",
            algorithm_id=999,
            key_sizes=[256],
            block_size=16,
            iv_size=16,
            is_aead=False,
        )

        key = secrets.token_bytes(32)
        plaintext = b"test"
        iv = secrets.token_bytes(16)

        with pytest.raises(ValueError, match="Unsupported traditional algorithm"):
            alg.encrypt(key, plaintext, iv)


class TestIntegrityAlgorithm:
    """Test suite for integrity algorithms."""

    def test_hmac_sha2_256_compute_verify(self):
        """Test HMAC-SHA2-256 computation and verification."""
        alg = IntegrityAlgorithm(
            name="HMAC_SHA2_256",
            algorithm_id=12,
            key_size=32,
            hash_size=16,
            hash_algorithm_name="SHA256",
        )

        key = secrets.token_bytes(32)
        data = b"Data to authenticate with HMAC-SHA2-256"

        # Compute ICV
        icv = alg.compute(key, data)
        assert len(icv) == 16  # Truncated to hash_size

        # Verify ICV
        assert alg.verify(key, data, icv) is True

        # Verify with wrong data
        wrong_data = b"Wrong data"
        assert alg.verify(key, wrong_data, icv) is False

        # Verify with wrong key
        wrong_key = secrets.token_bytes(32)
        assert alg.verify(wrong_key, data, icv) is False

    def test_hmac_sha2_384_compute_verify(self):
        """Test HMAC-SHA2-384 computation and verification."""
        alg = IntegrityAlgorithm(
            name="HMAC_SHA2_384",
            algorithm_id=13,
            key_size=48,
            hash_size=24,
            hash_algorithm_name="SHA384",
        )

        key = secrets.token_bytes(48)
        data = b"Data to authenticate with HMAC-SHA2-384"

        icv = alg.compute(key, data)
        assert len(icv) == 24

        assert alg.verify(key, data, icv) is True

    def test_hmac_sha2_512_compute_verify(self):
        """Test HMAC-SHA2-512 computation and verification."""
        alg = IntegrityAlgorithm(
            name="HMAC_SHA2_512",
            algorithm_id=14,
            key_size=64,
            hash_size=32,
            hash_algorithm_name="SHA512",
        )

        key = secrets.token_bytes(64)
        data = b"Data to authenticate with HMAC-SHA2-512"

        icv = alg.compute(key, data)
        assert len(icv) == 32

        assert alg.verify(key, data, icv) is True

    def test_hash_function_property(self):
        """Test hash function property getter."""
        alg = IntegrityAlgorithm(
            name="HMAC_SHA2_256",
            algorithm_id=12,
            key_size=32,
            hash_size=16,
            hash_algorithm_name="SHA256",
        )

        hash_func = alg.hash_function
        assert isinstance(hash_func, hashes.SHA256)

        # Test unsupported hash algorithm
        alg_unsupported = IntegrityAlgorithm(
            name="HMAC_UNSUPPORTED",
            algorithm_id=999,
            key_size=32,
            hash_size=16,
            hash_algorithm_name="UNSUPPORTED",
        )

        assert alg_unsupported.hash_function is None


class TestPRFAlgorithm:
    """Test suite for PRF algorithms."""

    def test_prf_hmac_sha2_256_compute(self):
        """Test PRF-HMAC-SHA2-256 computation."""
        alg = PRFAlgorithm(
            name="PRF_HMAC_SHA2_256",
            algorithm_id=5,
            key_size=32,
            hash_algorithm_name="SHA256",
        )

        key = secrets.token_bytes(32)
        data = b"Data for PRF computation"

        output = alg.compute(key, data)
        assert len(output) == 32  # SHA256 output size

        # Same input should produce same output
        output2 = alg.compute(key, data)
        assert output == output2

        # Different key should produce different output
        different_key = secrets.token_bytes(32)
        output3 = alg.compute(different_key, data)
        assert output != output3

    def test_prf_hmac_sha2_384_compute(self):
        """Test PRF-HMAC-SHA2-384 computation."""
        alg = PRFAlgorithm(
            name="PRF_HMAC_SHA2_384",
            algorithm_id=6,
            key_size=48,
            hash_algorithm_name="SHA384",
        )

        key = secrets.token_bytes(48)
        data = b"Data for PRF-384 computation"

        output = alg.compute(key, data)
        assert len(output) == 48  # SHA384 output size

    def test_prf_hmac_sha2_512_compute(self):
        """Test PRF-HMAC-SHA2-512 computation."""
        alg = PRFAlgorithm(
            name="PRF_HMAC_SHA2_512",
            algorithm_id=7,
            key_size=64,
            hash_algorithm_name="SHA512",
        )

        key = secrets.token_bytes(64)
        data = b"Data for PRF-512 computation"

        output = alg.compute(key, data)
        assert len(output) == 64  # SHA512 output size

    def test_prf_expand_function(self):
        """Test PRF expand function for key derivation."""
        alg = PRFAlgorithm(
            name="PRF_HMAC_SHA2_256",
            algorithm_id=5,
            key_size=32,
            hash_algorithm_name="SHA256",
        )

        key = secrets.token_bytes(32)
        info = b"Key expansion info"

        # Test expanding to different lengths
        output_16 = alg.expand(key, info, 16)
        assert len(output_16) == 16

        output_64 = alg.expand(key, info, 64)
        assert len(output_64) == 64

        output_100 = alg.expand(key, info, 100)
        assert len(output_100) == 100

        # First 16 bytes should match
        assert output_64[:16] == output_16

        # Same parameters should produce same output
        output_64_2 = alg.expand(key, info, 64)
        assert output_64 == output_64_2

    def test_prf_hash_function_property(self):
        """Test PRF hash function property getter."""
        alg = PRFAlgorithm(
            name="PRF_HMAC_SHA2_256",
            algorithm_id=5,
            key_size=32,
            hash_algorithm_name="SHA256",
        )

        hash_func = alg.hash_function
        assert isinstance(hash_func, hashes.SHA256)

        # Test unsupported hash algorithm
        alg_unsupported = PRFAlgorithm(
            name="PRF_UNSUPPORTED",
            algorithm_id=999,
            key_size=32,
            hash_algorithm_name="UNSUPPORTED",
        )

        assert alg_unsupported.hash_function is None


class TestAlgorithmIntegration:
    """Integration tests for algorithm combinations."""

    def test_aes_gcm_with_different_key_sizes(self):
        """Test AES-GCM with different key sizes."""
        alg = EncryptionAlgorithm(
            name="AES_GCM_16",
            algorithm_id=20,
            key_sizes=[128, 192, 256],
            block_size=16,
            iv_size=8,
            is_aead=True,
        )

        plaintext = b"Test message for different key sizes"
        iv = secrets.token_bytes(8)
        aad = b"AAD"

        # Test 128-bit key
        key_128 = secrets.token_bytes(16)
        ciphertext_128 = alg.encrypt(key_128, plaintext, iv, aad)
        decrypted_128 = alg.decrypt(key_128, ciphertext_128, iv, aad)
        assert decrypted_128 == plaintext

        # Test 192-bit key
        key_192 = secrets.token_bytes(24)
        ciphertext_192 = alg.encrypt(key_192, plaintext, iv, aad)
        decrypted_192 = alg.decrypt(key_192, ciphertext_192, iv, aad)
        assert decrypted_192 == plaintext

        # Test 256-bit key
        key_256 = secrets.token_bytes(32)
        ciphertext_256 = alg.encrypt(key_256, plaintext, iv, aad)
        decrypted_256 = alg.decrypt(key_256, ciphertext_256, iv, aad)
        assert decrypted_256 == plaintext

        # Different keys should produce different ciphertexts
        assert ciphertext_128 != ciphertext_192 != ciphertext_256

    def test_integrity_with_large_data(self):
        """Test integrity algorithms with large data sets."""
        alg = IntegrityAlgorithm(
            name="HMAC_SHA2_256",
            algorithm_id=12,
            key_size=32,
            hash_size=16,
            hash_algorithm_name="SHA256",
        )

        key = secrets.token_bytes(32)

        # Test with large data (1MB)
        large_data = secrets.token_bytes(1024 * 1024)
        icv = alg.compute(key, large_data)
        assert len(icv) == 16
        assert alg.verify(key, large_data, icv) is True

        # Test with empty data
        empty_data = b""
        icv_empty = alg.compute(key, empty_data)
        assert len(icv_empty) == 16
        assert alg.verify(key, empty_data, icv_empty) is True

    def test_prf_deterministic_behavior(self):
        """Test PRF deterministic behavior for key derivation."""
        alg = PRFAlgorithm(
            name="PRF_HMAC_SHA2_256",
            algorithm_id=5,
            key_size=32,
            hash_algorithm_name="SHA256",
        )

        key = b"test_key_for_deterministic_behavior"
        info = b"test_info_for_expansion"

        # Multiple calls should produce identical results
        output1 = alg.expand(key, info, 128)
        output2 = alg.expand(key, info, 128)
        output3 = alg.expand(key, info, 128)

        assert output1 == output2 == output3
        assert len(output1) == 128

        # Different info should produce different output
        different_info = b"different_info_for_expansion"
        output_diff = alg.expand(key, different_info, 128)
        assert output1 != output_diff
