"""
Unit tests for key management and derivation.

This module provides comprehensive tests for key generation, derivation, and management
with focus on:
- Random key generation
- IKEv2 key derivation (RFC 7296)
- Child SA key derivation
- ESP key extraction
- PRF+ function implementation
- Edge cases and error handling
"""

import pytest
import secrets
from unittest.mock import Mock

from ipsecator.core.crypto.keys import Key<PERSON><PERSON><PERSON>
from ipsecator.core.crypto.algorithms import PRFAlgorithm


class TestKeyManager:
    """Test suite for KeyManager class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.key_manager = KeyManager()

    def test_generate_random_key(self):
        """Test random key generation."""
        # Test different key lengths
        key_16 = self.key_manager.generate_random_key(16)
        assert len(key_16) == 16
        assert isinstance(key_16, bytes)

        key_32 = self.key_manager.generate_random_key(32)
        assert len(key_32) == 32
        assert isinstance(key_32, bytes)

        key_64 = self.key_manager.generate_random_key(64)
        assert len(key_64) == 64
        assert isinstance(key_64, bytes)

        # Keys should be different
        key1 = self.key_manager.generate_random_key(32)
        key2 = self.key_manager.generate_random_key(32)
        assert key1 != key2

        # Test zero length
        key_0 = self.key_manager.generate_random_key(0)
        assert len(key_0) == 0
        assert key_0 == b""

    def test_derive_ikev2_keys(self):
        """Test IKEv2 key derivation according to RFC 7296."""
        # Create mock PRF algorithm
        prf = PRFAlgorithm(
            name="PRF_HMAC_SHA2_256",
            algorithm_id=5,
            key_size=32,
            hash_algorithm_name="SHA256",
        )

        # Test data
        shared_secret = secrets.token_bytes(32)
        nonce_i = secrets.token_bytes(16)
        nonce_r = secrets.token_bytes(16)
        spi_i = secrets.token_bytes(8)
        spi_r = secrets.token_bytes(8)

        key_lengths = {
            "SK_d": 32,    # Key derivation key
            "SK_ai": 32,   # Initiator auth key
            "SK_ar": 32,   # Responder auth key
            "SK_ei": 32,   # Initiator encryption key
            "SK_er": 32,   # Responder encryption key
            "SK_pi": 32,   # Initiator PRF key
            "SK_pr": 32,   # Responder PRF key
        }

        keys = self.key_manager.derive_ikev2_keys(
            prf, shared_secret, nonce_i, nonce_r, spi_i, spi_r, key_lengths
        )

        # Verify all keys are present and correct length
        assert len(keys) == 7
        for key_name, expected_length in key_lengths.items():
            assert key_name in keys
            assert len(keys[key_name]) == expected_length
            assert isinstance(keys[key_name], bytes)

        # Keys should be different
        key_values = list(keys.values())
        for i, key1 in enumerate(key_values):
            for j, key2 in enumerate(key_values):
                if i != j:
                    assert key1 != key2

    def test_derive_ikev2_keys_deterministic(self):
        """Test IKEv2 key derivation is deterministic."""
        prf = PRFAlgorithm(
            name="PRF_HMAC_SHA2_256",
            algorithm_id=5,
            key_size=32,
            hash_algorithm_name="SHA256",
        )

        # Fixed test data
        shared_secret = b"shared_secret_for_testing_deterministic_behavior"
        nonce_i = b"initiator_nonce_"
        nonce_r = b"responder_nonce_"
        spi_i = b"init_spi"
        spi_r = b"resp_spi"

        key_lengths = {"SK_d": 32, "SK_ai": 32}

        # Derive keys twice
        keys1 = self.key_manager.derive_ikev2_keys(
            prf, shared_secret, nonce_i, nonce_r, spi_i, spi_r, key_lengths
        )
        keys2 = self.key_manager.derive_ikev2_keys(
            prf, shared_secret, nonce_i, nonce_r, spi_i, spi_r, key_lengths
        )

        # Should be identical
        assert keys1 == keys2

    def test_derive_child_sa_keys(self):
        """Test Child SA key derivation for ESP."""
        prf = PRFAlgorithm(
            name="PRF_HMAC_SHA2_256",
            algorithm_id=5,
            key_size=32,
            hash_algorithm_name="SHA256",
        )

        sk_d = secrets.token_bytes(32)  # Key derivation key from IKE SA
        nonce_i = secrets.token_bytes(16)
        nonce_r = secrets.token_bytes(16)

        key_lengths = {
            "ESP_enc_i": 32,  # Initiator ESP encryption key
            "ESP_enc_r": 32,  # Responder ESP encryption key
            "ESP_auth_i": 32, # Initiator ESP auth key
            "ESP_auth_r": 32, # Responder ESP auth key
        }

        keys = self.key_manager.derive_child_sa_keys(
            prf, sk_d, nonce_i, nonce_r, key_lengths
        )

        # Verify all keys are present and correct length
        assert len(keys) == 4
        for key_name, expected_length in key_lengths.items():
            assert key_name in keys
            assert len(keys[key_name]) == expected_length
            assert isinstance(keys[key_name], bytes)

        # Keys should be different
        key_values = list(keys.values())
        for i, key1 in enumerate(key_values):
            for j, key2 in enumerate(key_values):
                if i != j:
                    assert key1 != key2

    def test_derive_child_sa_keys_deterministic(self):
        """Test Child SA key derivation is deterministic."""
        prf = PRFAlgorithm(
            name="PRF_HMAC_SHA2_256",
            algorithm_id=5,
            key_size=32,
            hash_algorithm_name="SHA256",
        )

        # Fixed test data
        sk_d = b"key_derivation_key_for_child_sa_testing"
        nonce_i = b"child_sa_init_nonce"
        nonce_r = b"child_sa_resp_nonce"

        key_lengths = {"ESP_enc": 32, "ESP_auth": 32}

        # Derive keys twice
        keys1 = self.key_manager.derive_child_sa_keys(
            prf, sk_d, nonce_i, nonce_r, key_lengths
        )
        keys2 = self.key_manager.derive_child_sa_keys(
            prf, sk_d, nonce_i, nonce_r, key_lengths
        )

        # Should be identical
        assert keys1 == keys2

    def test_prf_plus_function(self):
        """Test PRF+ function implementation."""
        prf = PRFAlgorithm(
            name="PRF_HMAC_SHA2_256",
            algorithm_id=5,
            key_size=32,
            hash_algorithm_name="SHA256",
        )

        key = b"test_key_for_prf_plus"
        seed = b"test_seed_data"

        # Test different output lengths
        output_32 = self.key_manager._prf_plus(prf, key, seed, 32)
        assert len(output_32) == 32

        output_64 = self.key_manager._prf_plus(prf, key, seed, 64)
        assert len(output_64) == 64

        output_100 = self.key_manager._prf_plus(prf, key, seed, 100)
        assert len(output_100) == 100

        # First 32 bytes should match
        assert output_64[:32] == output_32
        assert output_100[:32] == output_32

        # Test deterministic behavior
        output_64_2 = self.key_manager._prf_plus(prf, key, seed, 64)
        assert output_64 == output_64_2

        # Different seed should produce different output
        different_seed = b"different_seed_data"
        output_diff = self.key_manager._prf_plus(prf, key, different_seed, 64)
        assert output_64 != output_diff

    def test_derive_esp_keys(self):
        """Test ESP key extraction from key material."""
        # Test AES-GCM (AEAD - no separate integrity key)
        key_material_aead = secrets.token_bytes(64)
        keys_aead = self.key_manager.derive_esp_keys(
            "AES_GCM_16", None, key_material_aead
        )

        assert "encryption" in keys_aead
        assert len(keys_aead["encryption"]) == 32  # 256-bit key
        assert "integrity" not in keys_aead  # No integrity key for AEAD

        # Test AES-CBC with HMAC (traditional)
        key_material_trad = secrets.token_bytes(96)  # 32 + 32 + extra
        keys_trad = self.key_manager.derive_esp_keys(
            "AES_CBC", "HMAC_SHA2_256", key_material_trad
        )

        assert "encryption" in keys_trad
        assert "integrity" in keys_trad
        assert len(keys_trad["encryption"]) == 32  # 256-bit AES key
        assert len(keys_trad["integrity"]) == 32   # SHA256 key

        # Test ChaCha20-Poly1305
        key_material_chacha = secrets.token_bytes(64)
        keys_chacha = self.key_manager.derive_esp_keys(
            "CHACHA20_POLY1305", None, key_material_chacha
        )

        assert "encryption" in keys_chacha
        assert len(keys_chacha["encryption"]) == 32  # 256-bit key
        assert "integrity" not in keys_chacha  # No integrity key for AEAD

    def test_derive_esp_keys_unsupported_algorithms(self):
        """Test ESP key derivation with unsupported algorithms."""
        key_material = secrets.token_bytes(64)

        # Unsupported encryption algorithm
        keys_unsupported = self.key_manager.derive_esp_keys(
            "UNSUPPORTED_ALG", "HMAC_SHA2_256", key_material
        )
        assert "encryption" not in keys_unsupported
        assert "integrity" in keys_unsupported  # Integrity should still work

        # Unsupported integrity algorithm
        keys_unsupported2 = self.key_manager.derive_esp_keys(
            "AES_GCM_16", "UNSUPPORTED_INTEGRITY", key_material
        )
        assert "encryption" in keys_unsupported2
        assert "integrity" not in keys_unsupported2

    def test_key_derivation_with_empty_inputs(self):
        """Test key derivation with edge case inputs."""
        prf = PRFAlgorithm(
            name="PRF_HMAC_SHA2_256",
            algorithm_id=5,
            key_size=32,
            hash_algorithm_name="SHA256",
        )

        # Test with empty key lengths
        keys_empty = self.key_manager.derive_ikev2_keys(
            prf, b"secret", b"nonce_i", b"nonce_r", b"spi_i___", b"spi_r___", {}
        )
        assert keys_empty == {}

        # Test with single key
        keys_single = self.key_manager.derive_ikev2_keys(
            prf, b"secret", b"nonce_i", b"nonce_r", b"spi_i___", b"spi_r___", {"SK_d": 16}
        )
        assert len(keys_single) == 1
        assert "SK_d" in keys_single
        assert len(keys_single["SK_d"]) == 16
