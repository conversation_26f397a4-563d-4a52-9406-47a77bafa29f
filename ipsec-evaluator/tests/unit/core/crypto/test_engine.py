"""
Unit tests for the main cryptographic engine.

This module provides comprehensive tests for the CryptoEngine class with focus on:
- Encryption/decryption operations
- Integrity computation and verification
- Key derivation coordination
- <PERSON><PERSON><PERSON>-<PERSON><PERSON> key exchange
- Algorithm management
- Integration testing
- Error handling
"""

import pytest
import secrets
from unittest.mock import Mock, patch

from ipsecator.core.crypto.engine import CryptoEngine


class TestCryptoEngine:
    """Test suite for CryptoEngine class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.engine = CryptoEngine()

    def test_initialization(self):
        """Test CryptoEngine initialization."""
        assert self.engine.key_manager is not None
        assert self.engine.dh_manager is not None
        assert len(self.engine._encryption_algs) > 0
        assert len(self.engine._integrity_algs) > 0
        assert len(self.engine._prf_algs) > 0

        # Check specific algorithms are initialized
        assert "AES_GCM_16" in self.engine._encryption_algs
        assert "AES_CBC" in self.engine._encryption_algs
        assert "CHACHA20_POLY1305" in self.engine._encryption_algs
        assert "HMAC_SHA2_256" in self.engine._integrity_algs
        assert "HMAC_SHA2_384" in self.engine._integrity_algs
        assert "HMAC_SHA2_512" in self.engine._integrity_algs
        assert "PRF_HMAC_SHA2_256" in self.engine._prf_algs

    def test_aes_gcm_encryption_decryption(self):
        """Test AES-GCM encryption and decryption."""
        key = secrets.token_bytes(32)  # 256-bit key
        plaintext = b"Hello, AES-GCM encryption test!"
        aad = b"Additional authenticated data"

        # Encrypt
        ciphertext, iv = self.engine.encrypt("AES_GCM_16", key, plaintext, aad=aad)
        assert len(iv) == 8  # AES-GCM IV size
        assert len(ciphertext) > len(plaintext)  # Includes auth tag

        # Decrypt
        decrypted = self.engine.decrypt("AES_GCM_16", key, ciphertext, iv, aad=aad)
        assert decrypted == plaintext

    def test_aes_gcm_with_custom_iv(self):
        """Test AES-GCM with custom IV."""
        key = secrets.token_bytes(32)
        plaintext = b"Test with custom IV"
        custom_iv = secrets.token_bytes(8)
        aad = b"AAD"

        # Encrypt with custom IV
        ciphertext, returned_iv = self.engine.encrypt(
            "AES_GCM_16", key, plaintext, iv=custom_iv, aad=aad
        )
        assert returned_iv == custom_iv

        # Decrypt
        decrypted = self.engine.decrypt("AES_GCM_16", key, ciphertext, custom_iv, aad=aad)
        assert decrypted == plaintext

    def test_aes_cbc_encryption_decryption(self):
        """Test AES-CBC encryption and decryption."""
        key = secrets.token_bytes(32)  # 256-bit key
        plaintext = b"Hello, AES-CBC encryption test with padding!"

        # Encrypt
        ciphertext, iv = self.engine.encrypt("AES_CBC", key, plaintext)
        assert len(iv) == 16  # AES-CBC IV size
        assert len(ciphertext) % 16 == 0  # Block aligned

        # Decrypt
        decrypted = self.engine.decrypt("AES_CBC", key, ciphertext, iv)
        assert decrypted == plaintext

    def test_chacha20_poly1305_encryption_decryption(self):
        """Test ChaCha20-Poly1305 encryption and decryption."""
        key = secrets.token_bytes(32)  # 256-bit key
        plaintext = b"Hello, ChaCha20-Poly1305 test!"
        aad = b"Additional data"

        # Encrypt
        ciphertext, iv = self.engine.encrypt("CHACHA20_POLY1305", key, plaintext, aad=aad)
        assert len(iv) == 12  # ChaCha20-Poly1305 nonce size
        assert len(ciphertext) > len(plaintext)  # Includes auth tag

        # Decrypt
        decrypted = self.engine.decrypt("CHACHA20_POLY1305", key, ciphertext, iv, aad=aad)
        assert decrypted == plaintext

    def test_unsupported_encryption_algorithm(self):
        """Test error handling for unsupported encryption algorithms."""
        key = secrets.token_bytes(32)
        plaintext = b"test"

        with pytest.raises(ValueError, match="Unsupported encryption algorithm"):
            self.engine.encrypt("UNSUPPORTED_ALG", key, plaintext)

        with pytest.raises(ValueError, match="Unsupported encryption algorithm"):
            self.engine.decrypt("UNSUPPORTED_ALG", key, b"ciphertext", b"iv")

    def test_hmac_sha2_256_integrity(self):
        """Test HMAC-SHA2-256 integrity computation and verification."""
        key = secrets.token_bytes(32)
        data = b"Data to authenticate with HMAC-SHA2-256"

        # Compute ICV
        icv = self.engine.compute_integrity("HMAC_SHA2_256", key, data)
        assert len(icv) == 16  # Truncated to 16 bytes

        # Verify ICV
        assert self.engine.verify_integrity("HMAC_SHA2_256", key, data, icv) is True

        # Verify with wrong data
        wrong_data = b"Wrong data"
        assert self.engine.verify_integrity("HMAC_SHA2_256", key, wrong_data, icv) is False

    def test_hmac_sha2_384_integrity(self):
        """Test HMAC-SHA2-384 integrity computation and verification."""
        key = secrets.token_bytes(48)
        data = b"Data to authenticate with HMAC-SHA2-384"

        icv = self.engine.compute_integrity("HMAC_SHA2_384", key, data)
        assert len(icv) == 24  # Truncated to 24 bytes

        assert self.engine.verify_integrity("HMAC_SHA2_384", key, data, icv) is True

    def test_hmac_sha2_512_integrity(self):
        """Test HMAC-SHA2-512 integrity computation and verification."""
        key = secrets.token_bytes(64)
        data = b"Data to authenticate with HMAC-SHA2-512"

        icv = self.engine.compute_integrity("HMAC_SHA2_512", key, data)
        assert len(icv) == 32  # Truncated to 32 bytes

        assert self.engine.verify_integrity("HMAC_SHA2_512", key, data, icv) is True

    def test_unsupported_integrity_algorithm(self):
        """Test error handling for unsupported integrity algorithms."""
        key = secrets.token_bytes(32)
        data = b"test data"

        with pytest.raises(ValueError, match="Unsupported integrity algorithm"):
            self.engine.compute_integrity("UNSUPPORTED_INTEGRITY", key, data)

        with pytest.raises(ValueError, match="Unsupported integrity algorithm"):
            self.engine.verify_integrity("UNSUPPORTED_INTEGRITY", key, data, b"icv")

    def test_derive_keys(self):
        """Test IKEv2 key derivation."""
        shared_secret = secrets.token_bytes(32)
        nonce_i = secrets.token_bytes(16)
        nonce_r = secrets.token_bytes(16)
        spi_i = secrets.token_bytes(8)
        spi_r = secrets.token_bytes(8)

        key_lengths = {
            "SK_d": 32,
            "SK_ai": 32,
            "SK_ar": 32,
        }

        keys = self.engine.derive_keys(
            "PRF_HMAC_SHA2_256", shared_secret, nonce_i, nonce_r, spi_i, spi_r, key_lengths
        )

        assert len(keys) == 3
        for key_name, expected_length in key_lengths.items():
            assert key_name in keys
            assert len(keys[key_name]) == expected_length

    def test_derive_keys_unsupported_prf(self):
        """Test error handling for unsupported PRF algorithms."""
        with pytest.raises(ValueError, match="Unsupported PRF algorithm"):
            self.engine.derive_keys(
                "UNSUPPORTED_PRF", b"secret", b"ni", b"nr", b"spi_i___", b"spi_r___", {"SK_d": 32}
            )

    def test_generate_dh_keypair(self):
        """Test Diffie-Hellman key pair generation."""
        # Test with secp256r1 (group 19)
        private_key, public_key = self.engine.generate_dh_keypair(19)
        assert isinstance(private_key, bytes)
        assert isinstance(public_key, bytes)
        assert len(private_key) > 0
        assert len(public_key) > 0

        # Keys should be different each time
        private_key2, public_key2 = self.engine.generate_dh_keypair(19)
        assert private_key != private_key2
        assert public_key != public_key2

    def test_compute_dh_shared_secret(self):
        """Test Diffie-Hellman shared secret computation."""
        # Generate two key pairs
        private_key1, public_key1 = self.engine.generate_dh_keypair(19)
        private_key2, public_key2 = self.engine.generate_dh_keypair(19)

        # Compute shared secrets
        shared_secret1 = self.engine.compute_dh_shared_secret(19, private_key1, public_key2)
        shared_secret2 = self.engine.compute_dh_shared_secret(19, private_key2, public_key1)

        # Shared secrets should be identical
        assert shared_secret1 == shared_secret2
        assert len(shared_secret1) > 0

    def test_get_supported_algorithms(self):
        """Test getting supported algorithms."""
        algorithms = self.engine.get_supported_algorithms()

        assert "encryption" in algorithms
        assert "integrity" in algorithms
        assert "prf" in algorithms
        assert "dh_groups" in algorithms

        # Check specific algorithms are listed
        assert "AES_GCM_16" in algorithms["encryption"]
        assert "AES_CBC" in algorithms["encryption"]
        assert "CHACHA20_POLY1305" in algorithms["encryption"]
        assert "HMAC_SHA2_256" in algorithms["integrity"]
        assert "PRF_HMAC_SHA2_256" in algorithms["prf"]
        assert isinstance(algorithms["dh_groups"], list)
        assert len(algorithms["dh_groups"]) > 0

    def test_encryption_with_different_key_sizes(self):
        """Test encryption with different key sizes."""
        plaintext = b"Test message for different key sizes"
        aad = b"AAD"

        # Test AES-GCM with different key sizes
        for key_size in [16, 24, 32]:  # 128, 192, 256 bits
            key = secrets.token_bytes(key_size)
            ciphertext, iv = self.engine.encrypt("AES_GCM_16", key, plaintext, aad=aad)
            decrypted = self.engine.decrypt("AES_GCM_16", key, ciphertext, iv, aad=aad)
            assert decrypted == plaintext

    def test_integrity_with_large_data(self):
        """Test integrity algorithms with large data."""
        key = secrets.token_bytes(32)
        large_data = secrets.token_bytes(1024 * 1024)  # 1MB

        icv = self.engine.compute_integrity("HMAC_SHA2_256", key, large_data)
        assert len(icv) == 16
        assert self.engine.verify_integrity("HMAC_SHA2_256", key, large_data, icv) is True
