"""
Unit tests for enhanced tester implementation.

Tests the comprehensive test execution engine with IKEv2/ESP simulation,
based on ipsecdr patterns but enhanced for the new architecture.
"""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, patch

from ipsec_evaluator.engine.tester import (
    EnhancedTester,
    TestPhase,
    TestData
)
from ipsec_evaluator.models.base import TestMode


class TestEnhancedTester:
    """Test suite for the Enhanced Tester."""
    
    def test_initialization_initiator(self, enhanced_tester_initiator):
        """Test enhanced tester initialization as initiator."""
        tester = enhanced_tester_initiator
        
        assert tester.mode == TestMode.INITIATOR
        assert tester.current_phase == TestPhase.INITIALIZATION
        assert isinstance(tester.test_data, TestData)
        assert tester.test_data.mode == TestMode.INITIATOR
        assert tester.ikev2_protocol is None  # Not initialized yet
        assert tester.esp_protocol is None  # Not initialized yet
    
    def test_initialization_responder(self, enhanced_tester_responder):
        """Test enhanced tester initialization as responder."""
        tester = enhanced_tester_responder
        
        assert tester.mode == TestMode.RESPONDER
        assert tester.current_phase == TestPhase.INITIALIZATION
        assert tester.test_data.mode == TestMode.RESPONDER
    
    @pytest.mark.asyncio
    async def test_full_test_execution_initiator(self, enhanced_tester_initiator):
        """Test complete test execution as initiator."""
        tester = enhanced_tester_initiator
        
        # Execute the test
        test_data = await tester.execute()
        
        # Verify test completion
        assert test_data is not None
        assert isinstance(test_data, TestData)
        assert test_data.end_time is not None
        assert tester.current_phase == TestPhase.COMPLETED
        
        # Verify test data structure
        assert test_data.test_id == tester.test_id
        assert test_data.mode == TestMode.INITIATOR
        assert len(test_data.ikev2_exchanges) > 0
        assert len(test_data.esp_packets) > 0
        
        # Verify exchange data
        exchange_types = [ex.get("exchange_type") for ex in test_data.ikev2_exchanges]
        assert "IKE_SA_INIT" in exchange_types
        assert "IKE_AUTH" in exchange_types
        assert "CREATE_CHILD_SA" in exchange_types
        
        # Verify timing data
        assert "IKE_SA_INIT" in test_data.exchange_timings
        assert "IKE_AUTH" in test_data.exchange_timings
        assert "CREATE_CHILD_SA" in test_data.exchange_timings
        
        # Verify ESP packet data
        assert len(test_data.esp_packets) == 5  # Default number of test packets
        for packet in test_data.esp_packets:
            assert "packet_number" in packet
            assert "timestamp" in packet
            assert "direction" in packet
            assert packet["direction"] == "outbound"  # Initiator sends outbound
    
    @pytest.mark.asyncio
    async def test_full_test_execution_responder(self, enhanced_tester_responder):
        """Test complete test execution as responder."""
        tester = enhanced_tester_responder
        
        # Execute the test
        test_data = await tester.execute()
        
        # Verify responder-specific behavior
        assert test_data.mode == TestMode.RESPONDER
        
        # ESP packets should be inbound for responder
        for packet in test_data.esp_packets:
            assert packet["direction"] == "inbound"
    
    @pytest.mark.asyncio
    async def test_protocol_initialization_phase(self, enhanced_tester_initiator):
        """Test protocol initialization phase."""
        tester = enhanced_tester_initiator
        
        # Initialize protocols
        await tester._initialize_protocols()
        
        # Verify protocols are initialized
        assert tester.ikev2_protocol is not None
        assert tester.esp_protocol is not None
        assert tester.current_phase == TestPhase.PROTOCOL_SETUP
        
        # Verify IKEv2 protocol configuration
        ikev2 = tester.ikev2_protocol
        assert ikev2.role.value == "initiator"
        assert ikev2.local_ip == tester.config.network.initiator_ip
        assert ikev2.remote_ip == tester.config.network.responder_ip
        
        # Verify ESP protocol configuration
        esp = tester.esp_protocol
        assert esp.mode.value == "tunnel"
        assert esp.enable_anti_replay is True
        
        # Verify metadata collection
        metadata = tester.test_data.metadata
        assert "ikev2_role" in metadata
        assert "esp_mode" in metadata
        assert "local_ip" in metadata
        assert "remote_ip" in metadata
    
    @pytest.mark.asyncio
    async def test_ikev2_exchange_simulation(self, enhanced_tester_initiator):
        """Test IKEv2 exchange simulation."""
        tester = enhanced_tester_initiator
        
        # Initialize protocols first
        await tester._initialize_protocols()
        
        # Execute IKEv2 tests
        await tester._execute_ikev2_tests()
        
        # Verify exchanges were simulated
        test_data = tester.test_data
        assert len(test_data.ikev2_exchanges) == 3  # IKE_SA_INIT, IKE_AUTH, CREATE_CHILD_SA
        
        # Verify exchange types and order
        exchange_types = [ex.get("exchange_type") for ex in test_data.ikev2_exchanges]
        assert exchange_types[0] == "IKE_SA_INIT"
        assert exchange_types[1] == "IKE_AUTH"
        assert exchange_types[2] == "CREATE_CHILD_SA"
        
        # Verify timing data
        assert len(test_data.exchange_timings) == 3
        for exchange_type in exchange_types:
            assert exchange_type in test_data.exchange_timings
            assert test_data.exchange_timings[exchange_type] > 0
    
    @pytest.mark.asyncio
    async def test_esp_packet_simulation(self, enhanced_tester_initiator):
        """Test ESP packet simulation."""
        tester = enhanced_tester_initiator
        
        # Initialize protocols first
        await tester._initialize_protocols()
        
        # Execute ESP tests
        await tester._execute_esp_tests()
        
        # Verify ESP packets were simulated
        test_data = tester.test_data
        assert len(test_data.esp_packets) == 5  # Default number of test packets
        
        # Verify packet data structure
        for i, packet in enumerate(test_data.esp_packets):
            assert packet["packet_number"] == i + 1
            assert "timestamp" in packet
            assert "direction" in packet
            assert "size" in packet
            assert "encryption_algorithm" in packet
            assert "processing_time" in packet
            assert packet["processing_time"] > 0
    
    @pytest.mark.asyncio
    async def test_teardown_phase(self, enhanced_tester_initiator):
        """Test teardown phase."""
        tester = enhanced_tester_initiator
        
        # Initialize and run protocols
        await tester._initialize_protocols()
        await tester._execute_ikev2_tests()
        await tester._execute_esp_tests()
        
        # Execute teardown
        await tester._teardown_protocols()
        
        # Verify teardown phase
        assert tester.current_phase == TestPhase.TEARDOWN
        
        # Verify statistics collection
        metadata = tester.test_data.metadata
        assert "ikev2_statistics" in metadata
        assert "esp_statistics" in metadata
        
        # Verify packet counts
        packet_counts = tester.test_data.packet_counts
        assert "ikev2_exchanges" in packet_counts
        assert "esp_packets" in packet_counts
        assert packet_counts["ikev2_exchanges"] == 3
        assert packet_counts["esp_packets"] == 5
    
    def test_test_summary_generation(self, enhanced_tester_initiator):
        """Test test summary generation."""
        tester = enhanced_tester_initiator
        
        # Get initial summary
        summary = tester.get_test_summary()
        
        # Verify summary structure
        assert "test_id" in summary
        assert "scenario" in summary
        assert "mode" in summary
        assert "phase" in summary
        assert "total_time" in summary
        assert "exchanges" in summary
        assert "esp_packets" in summary
        assert "errors" in summary
        assert "warnings" in summary
        
        # Verify initial values
        assert summary["test_id"] == tester.test_id
        assert summary["scenario"] == tester.scenario.name
        assert summary["mode"] == TestMode.INITIATOR.value
        assert summary["phase"] == TestPhase.INITIALIZATION.value
        assert summary["total_time"] == 0.0
        assert summary["exchanges"] == 0
        assert summary["esp_packets"] == 0
        assert summary["errors"] == 0
        assert summary["warnings"] == 0
    
    @pytest.mark.asyncio
    async def test_error_handling_during_execution(self, enhanced_tester_initiator):
        """Test error handling during test execution."""
        tester = enhanced_tester_initiator
        
        # Mock a failure in protocol initialization
        with patch.object(tester, '_initialize_protocols', side_effect=Exception("Test error")):
            with pytest.raises(Exception, match="Test error"):
                await tester.execute()
            
            # Verify error was recorded
            assert len(tester.test_data.errors) == 1
            assert "Test error" in tester.test_data.errors[0]
            assert tester.test_data.end_time is not None
    
    @pytest.mark.asyncio
    async def test_performance_tracking(self, enhanced_tester_initiator):
        """Test performance tracking during execution."""
        tester = enhanced_tester_initiator
        
        # Execute test
        test_data = await tester.execute()
        
        # Verify performance data
        assert test_data.start_time is not None
        assert test_data.end_time is not None
        assert test_data.end_time > test_data.start_time
        
        # Verify exchange timings
        for exchange_type, timing in test_data.exchange_timings.items():
            assert timing > 0
            assert timing < 1.0  # Should complete within 1 second
        
        # Verify ESP packet processing times
        for packet in test_data.esp_packets:
            assert packet["processing_time"] > 0
            assert packet["processing_time"] < 0.1  # Should process within 100ms
    
    @pytest.mark.asyncio
    async def test_metadata_collection(self, enhanced_tester_initiator):
        """Test comprehensive metadata collection."""
        tester = enhanced_tester_initiator
        
        # Execute test
        test_data = await tester.execute()
        
        # Verify metadata structure
        metadata = test_data.metadata
        assert "ikev2_role" in metadata
        assert "esp_mode" in metadata
        assert "local_ip" in metadata
        assert "remote_ip" in metadata
        assert "nat_traversal" in metadata
        assert "ikev2_statistics" in metadata
        assert "esp_statistics" in metadata
        
        # Verify metadata values
        assert metadata["ikev2_role"] == "initiator"
        assert metadata["esp_mode"] == "tunnel"
        assert metadata["local_ip"] == tester.config.network.initiator_ip
        assert metadata["remote_ip"] == tester.config.network.responder_ip
    
    @pytest.mark.asyncio
    async def test_hook_integration(self, enhanced_tester_initiator):
        """Test hook system integration."""
        tester = enhanced_tester_initiator
        hook_manager = tester.hook_manager
        
        # Track hook executions
        hook_calls = []
        
        def test_hook(context):
            hook_calls.append(context.packet_number if hasattr(context, 'packet_number') else 'exchange')
            return {"test": "executed"}
        
        # Register test hook
        from ipsec_evaluator.core.ikev2 import HookType, HookPriority
        hook_manager.register_hook(
            HookType.PRE_PACKET,
            test_hook,
            HookPriority.NORMAL,
            "test_hook"
        )
        
        # Execute test
        await tester.execute()
        
        # Verify hooks were called (implementation dependent)
        # Note: This depends on the actual hook integration in the protocols
        assert len(hook_calls) >= 0  # May be 0 if hooks aren't triggered in simulation
    
    @pytest.mark.asyncio
    async def test_concurrent_tester_execution(self, test_configuration, basic_test_scenario, hook_manager):
        """Test concurrent execution of multiple testers."""
        # Create multiple testers
        testers = []
        for i in range(3):
            tester = EnhancedTester(
                test_id=f"concurrent-test-{i}",
                config=test_configuration,
                scenario=basic_test_scenario,
                mode=TestMode.INITIATOR,
                hook_manager=hook_manager
            )
            testers.append(tester)
        
        # Execute concurrently
        tasks = [tester.execute() for tester in testers]
        results = await asyncio.gather(*tasks)
        
        # Verify all tests completed
        assert len(results) == 3
        for result in results:
            assert isinstance(result, TestData)
            assert result.end_time is not None
            assert len(result.ikev2_exchanges) > 0
            assert len(result.esp_packets) > 0
