"""
Unit tests for enhanced checker implementation.

Tests the comprehensive compliance validation engine with ANSSI support,
based on ipsecdr patterns but enhanced for the new architecture.
"""

import pytest
import asyncio
from datetime import datetime

from ipsec_evaluator.engine.checker import (
    EnhancedChecker,
    ComplianceStandard,
    ComplianceRule
)
from ipsec_evaluator.models.base import ComplianceLevel
from ipsec_evaluator.models.results import ComplianceResult
from tests.support.helpers import generate_test_data, assert_anssi_compliance


class TestEnhancedChecker:
    """Test suite for the Enhanced Checker."""
    
    def test_initialization(self, enhanced_checker):
        """Test enhanced checker initialization."""
        checker = enhanced_checker
        
        assert isinstance(checker.standards, list)
        assert ComplianceStandard.ANSSI in checker.standards
        assert len(checker.ANSSI_RULES) > 0
        assert len(checker.ANSSI_APPROVED) > 0
        
        # Verify ANSSI rules structure
        for rule_id, rule in checker.ANSSI_RULES.items():
            assert isinstance(rule, ComplianceRule)
            assert rule.rule_id == rule_id
            assert rule.standard == ComplianceStandard.ANSSI
            assert rule.severity in ["critical", "major", "minor", "info"]
    
    def test_anssi_approved_algorithms(self, enhanced_checker):
        """Test ANSSI-approved algorithm lists."""
        checker = enhanced_checker
        approved = checker.ANSSI_APPROVED
        
        # Check encryption algorithms
        assert "AES_GCM_16" in approved["encryption"]
        assert "AES_CTR" in approved["encryption"]
        assert "CHACHA20_POLY1305" in approved["encryption"]
        
        # Check integrity algorithms
        assert "HMAC_SHA2_256" in approved["integrity"]
        assert "HMAC_SHA2_384" in approved["integrity"]
        assert "HMAC_SHA2_512" in approved["integrity"]
        
        # Check PRF algorithms
        assert "PRF_HMAC_SHA2_256" in approved["prf"]
        assert "PRF_HMAC_SHA2_384" in approved["prf"]
        assert "PRF_HMAC_SHA2_512" in approved["prf"]
        
        # Check DH groups (secp and brainpool)
        expected_dh_groups = [19, 20, 21, 27, 28, 29, 30]
        for group in expected_dh_groups:
            assert group in approved["dh_groups"]
    
    @pytest.mark.asyncio
    async def test_compliant_test_data_validation(self, enhanced_checker):
        """Test validation of compliant test data."""
        checker = enhanced_checker
        
        # Generate ANSSI-compliant test data
        test_data = generate_test_data(
            test_id="compliant-test",
            scenario_name="anssi_compliant",
            num_exchanges=3,
            num_esp_packets=5
        )
        
        # Check compliance
        result = await checker.check_compliance(test_data)
        
        # Should be compliant
        assert isinstance(result, ComplianceResult)
        assert result.level in [ComplianceLevel.COMPLIANT, ComplianceLevel.PARTIAL]
        assert result.score >= 80.0
        assert len(result.issues) == 0  # No critical issues for compliant data
    
    @pytest.mark.asyncio
    async def test_non_compliant_encryption_algorithms(self, enhanced_checker):
        """Test detection of non-compliant encryption algorithms."""
        checker = enhanced_checker
        
        # Generate test data with non-compliant encryption
        test_data = generate_test_data()
        
        # Modify to use non-compliant algorithm
        for exchange in test_data.ikev2_exchanges:
            if "algorithms" in exchange:
                exchange["algorithms"]["encryption"] = ["DES", "3DES"]  # Non-compliant
        
        # Check compliance
        result = await checker.check_compliance(test_data)
        
        # Should detect non-compliance
        assert result.level == ComplianceLevel.NON_COMPLIANT
        assert result.score < 80.0
        assert len(result.issues) > 0
        
        # Should have specific encryption algorithm issues
        encryption_issues = [issue for issue in result.issues if "ANSSI-001" in issue]
        assert len(encryption_issues) > 0
    
    @pytest.mark.asyncio
    async def test_non_compliant_dh_groups(self, enhanced_checker):
        """Test detection of non-compliant DH groups."""
        checker = enhanced_checker
        
        # Generate test data with non-compliant DH groups
        test_data = generate_test_data()
        
        # Modify to use non-compliant DH groups
        for exchange in test_data.ikev2_exchanges:
            if "algorithms" in exchange:
                exchange["algorithms"]["dh_groups"] = [14, 15]  # MODP groups, not ANSSI approved
        
        # Check compliance
        result = await checker.check_compliance(test_data)
        
        # Should detect non-compliance
        assert result.level == ComplianceLevel.NON_COMPLIANT
        assert len(result.issues) > 0
        
        # Should have specific DH group issues
        dh_issues = [issue for issue in result.issues if "ANSSI-003" in issue]
        assert len(dh_issues) > 0
    
    @pytest.mark.asyncio
    async def test_insufficient_key_sizes(self, enhanced_checker):
        """Test detection of insufficient key sizes."""
        checker = enhanced_checker
        
        # Mock configuration with insufficient key sizes
        checker.config.crypto.encryption_key_sizes = [128, 192]  # Below 256-bit minimum
        
        test_data = generate_test_data()
        
        # Check compliance
        result = await checker.check_compliance(test_data)
        
        # Should detect key size issues
        key_size_issues = [issue for issue in result.issues if "ANSSI-002" in issue]
        assert len(key_size_issues) > 0
    
    @pytest.mark.asyncio
    async def test_missing_required_exchanges(self, enhanced_checker):
        """Test detection of missing required exchanges."""
        checker = enhanced_checker
        
        # Generate test data with missing exchanges
        test_data = generate_test_data(num_exchanges=1)  # Only one exchange
        test_data.ikev2_exchanges = [
            {
                "exchange_type": "IKE_SA_INIT",
                "role": "initiator",
                "timestamp": datetime.now().isoformat()
            }
        ]
        
        # Check compliance
        result = await checker.check_compliance(test_data)
        
        # Should detect missing exchanges
        assert result.level == ComplianceLevel.NON_COMPLIANT
        missing_auth_issues = [issue for issue in result.issues if "Missing required IKE_AUTH" in issue]
        assert len(missing_auth_issues) > 0
    
    @pytest.mark.asyncio
    async def test_timing_requirements_validation(self, enhanced_checker):
        """Test validation of timing requirements."""
        checker = enhanced_checker
        
        # Generate test data with slow exchanges
        test_data = generate_test_data()
        test_data.exchange_timings = {
            "IKE_SA_INIT": 6.0,  # Over 5 second threshold
            "IKE_AUTH": 3.0,     # Over 2 second recommendation
            "CREATE_CHILD_SA": 0.5
        }
        
        # Check compliance
        result = await checker.check_compliance(test_data)
        
        # Should have timing warnings
        timing_warnings = [warning for warning in result.warnings if "ANSSI-006" in warning]
        assert len(timing_warnings) > 0
        
        # Should have recommendations for optimization
        timing_recommendations = [rec for rec in result.recommendations if "timing" in rec.lower()]
        assert len(timing_recommendations) >= 0  # May have timing recommendations
    
    @pytest.mark.asyncio
    async def test_esp_packet_processing_validation(self, enhanced_checker):
        """Test ESP packet processing validation."""
        checker = enhanced_checker
        
        # Generate test data with slow ESP processing
        test_data = generate_test_data()
        for packet in test_data.esp_packets:
            packet["processing_time"] = 0.15  # Over 100ms threshold
        
        # Check compliance
        result = await checker.check_compliance(test_data)
        
        # Should have ESP processing warnings
        esp_warnings = [warning for warning in result.warnings if "ESP packet processing" in warning]
        assert len(esp_warnings) > 0
    
    @pytest.mark.asyncio
    async def test_compliance_scoring(self, enhanced_checker):
        """Test compliance scoring mechanism."""
        checker = enhanced_checker
        
        # Test perfect compliance
        compliant_data = generate_test_data()
        result = await checker.check_compliance(compliant_data)
        assert result.score >= 95.0
        
        # Test partial compliance with warnings
        partial_data = generate_test_data()
        partial_data.exchange_timings["IKE_SA_INIT"] = 3.0  # Slow but not critical
        result = await checker.check_compliance(partial_data)
        assert 80.0 <= result.score < 95.0
        
        # Test non-compliance with critical issues
        non_compliant_data = generate_test_data()
        for exchange in non_compliant_data.ikev2_exchanges:
            if "algorithms" in exchange:
                exchange["algorithms"]["encryption"] = ["DES"]  # Critical issue
        result = await checker.check_compliance(non_compliant_data)
        assert result.score < 80.0
    
    def test_compliance_summary_generation(self, enhanced_checker):
        """Test compliance summary generation."""
        checker = enhanced_checker
        
        # Create a mock compliance result
        result = ComplianceResult(
            level=ComplianceLevel.PARTIAL,
            score=85.5,
            issues=["Critical issue 1", "Major issue 2"],
            warnings=["Warning 1", "Warning 2", "Warning 3"],
            recommendations=["Recommendation 1"]
        )
        
        # Generate summary
        summary = checker.get_compliance_summary(result)
        
        # Verify summary structure
        assert "test_id" in summary
        assert "scenario" in summary
        assert "compliance_level" in summary
        assert "score" in summary
        assert "standards_checked" in summary
        assert "critical_issues" in summary
        assert "total_issues" in summary
        assert "warnings" in summary
        assert "recommendations" in summary
        
        # Verify summary values
        assert summary["compliance_level"] == "partial"
        assert summary["score"] == 85.5
        assert summary["total_issues"] == 2
        assert summary["warnings"] == 3
        assert summary["recommendations"] == 1
        assert "anssi" in summary["standards_checked"]
    
    @pytest.mark.asyncio
    async def test_multiple_standards_checking(self, test_configuration, basic_test_scenario):
        """Test checking against multiple compliance standards."""
        # Create checker with multiple standards
        checker = EnhancedChecker(
            test_id="multi-standard-test",
            config=test_configuration,
            scenario=basic_test_scenario,
            standards=[ComplianceStandard.ANSSI, ComplianceStandard.NIST]
        )
        
        test_data = generate_test_data()
        
        # Check compliance
        result = await checker.check_compliance(test_data)
        
        # Should check all specified standards
        summary = checker.get_compliance_summary(result)
        assert len(summary["standards_checked"]) == 2
        assert "anssi" in summary["standards_checked"]
        assert "nist" in summary["standards_checked"]
    
    @pytest.mark.asyncio
    async def test_brainpool_curve_preference_validation(self, enhanced_checker):
        """Test validation with brainpool curve preference."""
        checker = enhanced_checker
        
        # Generate test data with brainpool curves
        test_data = generate_test_data()
        for exchange in test_data.ikev2_exchanges:
            if "algorithms" in exchange:
                exchange["algorithms"]["dh_groups"] = [28, 29]  # brainpool curves
        
        # Check compliance
        result = await checker.check_compliance(test_data)
        
        # Should be compliant with brainpool curves
        assert result.level in [ComplianceLevel.COMPLIANT, ComplianceLevel.PARTIAL]
        
        # Should have recommendations for brainpool usage
        brainpool_recommendations = [
            rec for rec in result.recommendations 
            if "brainpool" in rec.lower()
        ]
        assert len(brainpool_recommendations) > 0
    
    @pytest.mark.asyncio
    async def test_error_handling_in_compliance_check(self, enhanced_checker):
        """Test error handling during compliance checking."""
        checker = enhanced_checker
        
        # Create malformed test data
        test_data = generate_test_data()
        test_data.ikev2_exchanges = [{"malformed": "data"}]  # Missing required fields
        
        # Should handle errors gracefully
        result = await checker.check_compliance(test_data)
        
        # Should still return a result, possibly with warnings
        assert isinstance(result, ComplianceResult)
        assert result.level is not None
    
    @pytest.mark.asyncio
    async def test_performance_with_large_datasets(self, enhanced_checker):
        """Test checker performance with large test datasets."""
        checker = enhanced_checker
        
        # Generate large test data
        test_data = generate_test_data(
            num_exchanges=50,
            num_esp_packets=1000
        )
        
        # Measure compliance checking time
        import time
        start_time = time.time()
        
        result = await checker.check_compliance(test_data)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Should complete within reasonable time (less than 1 second)
        assert processing_time < 1.0
        assert isinstance(result, ComplianceResult)
    
    def test_anssi_compliance_helper_integration(self):
        """Test integration with ANSSI compliance helper."""
        # Generate compliant test data
        test_data = generate_test_data()
        
        # Should pass ANSSI compliance assertion
        assert assert_anssi_compliance(test_data) is True
        
        # Test with non-compliant data
        non_compliant_data = generate_test_data(num_exchanges=1)
        non_compliant_data.ikev2_exchanges = []  # No exchanges
        
        # Should fail ANSSI compliance assertion
        with pytest.raises(AssertionError):
            assert_anssi_compliance(non_compliant_data)
