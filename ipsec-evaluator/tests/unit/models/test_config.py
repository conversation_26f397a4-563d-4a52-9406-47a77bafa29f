"""
Unit tests for configuration models.

This module provides comprehensive tests for configuration models including:
- GlobalConfig validation and defaults
- NetworkConfig IP address and port validation
- PKIConfig file path validation
- CryptoConfig algorithm validation
- AuthConfig method validation
- TSConfig traffic selector validation
- IDConfig identity validation
- NotifyPayload data validation
- IPsecConfig integration
- Complex validation scenarios
"""

import pytest
import tempfile
import os
from pathlib import Path
from pydantic import ValidationError
from ipaddress import IPv4Address, IPv6Address

from ipsecator.models.config import (
    GlobalConfig,
    NetworkConfig,
    PKIConfig,
    CryptoConfig,
    AuthConfig,
    TSConfig,
    IDConfig,
    NATConfig,
    NotifyPayload,
    IPsecConfig,
    ESPTests,
    ESPConfig,
    TestConfiguration,
)


class TestGlobalConfig:
    """Test suite for GlobalConfig model."""

    def test_global_config_defaults(self):
        """Test GlobalConfig default values."""
        config = GlobalConfig()

        assert config.timeout == 30
        assert config.verbose is False
        assert config.log_level == "INFO"
        assert config.log_file is None
        assert config.max_concurrent_tests == 5

    def test_global_config_custom_values(self):
        """Test GlobalConfig with custom values."""
        config = GlobalConfig(
            timeout=60,
            verbose=True,
            log_level="DEBUG",
            log_file=Path("/tmp/test.log"),
            max_concurrent_tests=10
        )

        assert config.timeout == 60
        assert config.verbose is True
        assert config.log_level == "DEBUG"
        assert config.log_file == Path("/tmp/test.log")
        assert config.max_concurrent_tests == 10

    def test_global_config_timeout_validation(self):
        """Test timeout validation."""
        # Valid timeout
        config = GlobalConfig(timeout=1)
        assert config.timeout == 1

        # Invalid timeout (too small)
        with pytest.raises(ValidationError):
            GlobalConfig(timeout=0)

        with pytest.raises(ValidationError):
            GlobalConfig(timeout=-1)

    def test_global_config_log_level_validation(self):
        """Test log level validation."""
        # Valid log levels
        for level in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            config = GlobalConfig(log_level=level)
            assert config.log_level == level

        # Case insensitive
        config = GlobalConfig(log_level="debug")
        assert config.log_level == "DEBUG"

        # Invalid log level
        with pytest.raises(ValidationError):
            GlobalConfig(log_level="INVALID")

    def test_global_config_max_concurrent_tests_validation(self):
        """Test max_concurrent_tests validation."""
        # Valid value
        config = GlobalConfig(max_concurrent_tests=1)
        assert config.max_concurrent_tests == 1

        # Invalid value (too small)
        with pytest.raises(ValidationError):
            GlobalConfig(max_concurrent_tests=0)


class TestNetworkConfig:
    """Test suite for NetworkConfig model."""

    def test_network_config_defaults(self):
        """Test NetworkConfig default values."""
        config = NetworkConfig()

        assert config.interface == "eth0"
        assert str(config.ipsec_src) == "***********"
        assert str(config.ipsec_dst) == "********"
        assert str(config.ip_src) == "***********"
        assert str(config.ip_dst) == "*********"
        assert config.port_src == 4500
        assert config.port_dst == 4500
        assert config.nat_traversal is True
        assert config.test_network == "*************/24"

    def test_network_config_ipv4_addresses(self):
        """Test IPv4 address validation."""
        config = NetworkConfig(
            ipsec_src="***********",
            ipsec_dst="***********",
            ip_src="********",
            ip_dst="********"
        )

        assert str(config.ipsec_src) == "***********"
        assert str(config.ipsec_dst) == "***********"
        assert str(config.ip_src) == "********"
        assert str(config.ip_dst) == "********"

    def test_network_config_ipv6_addresses(self):
        """Test IPv6 address validation."""
        config = NetworkConfig(
            ipsec_src="2001:db8::1",
            ipsec_dst="2001:db8::2",
            ip_src="fe80::1",
            ip_dst="fe80::2"
        )

        assert str(config.ipsec_src) == "2001:db8::1"
        assert str(config.ipsec_dst) == "2001:db8::2"
        assert str(config.ip_src) == "fe80::1"
        assert str(config.ip_dst) == "fe80::2"

    def test_network_config_invalid_ip_addresses(self):
        """Test invalid IP address handling."""
        with pytest.raises(ValidationError):
            NetworkConfig(ipsec_src="invalid.ip.address")

        with pytest.raises(ValidationError):
            NetworkConfig(ipsec_dst="999.999.999.999")

    def test_network_config_port_validation(self):
        """Test port validation."""
        # Valid ports
        config = NetworkConfig(port_src=80, port_dst=443)
        assert config.port_src == 80
        assert config.port_dst == 443

        # Edge cases
        config = NetworkConfig(port_src=0, port_dst=65535)
        assert config.port_src == 0
        assert config.port_dst == 65535

        # Invalid ports
        with pytest.raises(ValidationError):
            NetworkConfig(port_src=-1)

        with pytest.raises(ValidationError):
            NetworkConfig(port_dst=65536)

        # String ports (should be converted)
        config = NetworkConfig(port_src="8080", port_dst="9090")
        assert config.port_src == 8080
        assert config.port_dst == 9090


class TestPKIConfig:
    """Test suite for PKIConfig model."""

    def test_pki_config_with_valid_files(self):
        """Test PKIConfig with valid file paths."""
        # Create temporary files
        with tempfile.NamedTemporaryFile(delete=False) as ca_cert:
            ca_cert_path = ca_cert.name
        with tempfile.NamedTemporaryFile(delete=False) as cert:
            cert_path = cert.name
        with tempfile.NamedTemporaryFile(delete=False) as key:
            key_path = key.name

        try:
            config = PKIConfig(
                ca_cert_path=ca_cert_path,
                cert_path=cert_path,
                key_path=key_path
            )

            assert config.ca_cert_path == ca_cert_path
            assert config.cert_path == cert_path
            assert config.key_path == key_path
            assert config.trust_chain == []
            assert config.verify_certificates is True
            assert config.certificate_validation is True

        finally:
            # Clean up
            os.unlink(ca_cert_path)
            os.unlink(cert_path)
            os.unlink(key_path)

    def test_pki_config_invalid_file_paths(self):
        """Test PKIConfig with invalid file paths."""
        with pytest.raises(ValidationError):
            PKIConfig(
                ca_cert_path="/nonexistent/ca.pem",
                cert_path="/nonexistent/cert.pem",
                key_path="/nonexistent/key.pem"
            )

    def test_pki_config_trust_chain_validation(self):
        """Test trust chain validation."""
        # Create temporary files
        with tempfile.NamedTemporaryFile(delete=False) as ca_cert:
            ca_cert_path = ca_cert.name
        with tempfile.NamedTemporaryFile(delete=False) as cert:
            cert_path = cert.name
        with tempfile.NamedTemporaryFile(delete=False) as key:
            key_path = key.name
        with tempfile.NamedTemporaryFile(delete=False) as trust1:
            trust1_path = trust1.name
        with tempfile.NamedTemporaryFile(delete=False) as trust2:
            trust2_path = trust2.name

        try:
            # Test with list of trust chain files
            config = PKIConfig(
                ca_cert_path=ca_cert_path,
                cert_path=cert_path,
                key_path=key_path,
                trust_chain=[trust1_path, trust2_path]
            )

            assert config.trust_chain == [trust1_path, trust2_path]

            # Test with single trust chain file (should be converted to list)
            config = PKIConfig(
                ca_cert_path=ca_cert_path,
                cert_path=cert_path,
                key_path=key_path,
                trust_chain=trust1_path
            )

            assert config.trust_chain == [trust1_path]

        finally:
            # Clean up
            for path in [ca_cert_path, cert_path, key_path, trust1_path, trust2_path]:
                os.unlink(path)


class TestCryptoConfig:
    """Test suite for CryptoConfig model."""

    def test_crypto_config_defaults(self):
        """Test CryptoConfig default values."""
        config = CryptoConfig()

        assert config.encryption_algorithms == ["AES-GCM-256"]
        assert config.integrity_algorithms == ["HMAC-SHA2-256"]
        assert config.dh_groups == [19]
        assert config.prf_algorithms == ["HMAC-SHA2-256"]

    def test_crypto_config_custom_values(self):
        """Test CryptoConfig with custom values."""
        config = CryptoConfig(
            encryption_algorithms=["AES-GCM-128", "AES-GCM-256", "ChaCha20-Poly1305"],
            integrity_algorithms=["HMAC-SHA2-256", "HMAC-SHA2-384"],
            dh_groups=[14, 19, 20],
            prf_algorithms=["HMAC-SHA2-256", "HMAC-SHA2-384"]
        )

        assert "AES-GCM-128" in config.encryption_algorithms
        assert "ChaCha20-Poly1305" in config.encryption_algorithms
        assert "HMAC-SHA2-384" in config.integrity_algorithms
        assert 14 in config.dh_groups
        assert 20 in config.dh_groups

    def test_crypto_config_dh_groups_validation(self):
        """Test DH groups validation."""
        # Valid DH groups
        valid_groups = [14, 15, 16, 17, 18, 19, 20, 21]
        config = CryptoConfig(dh_groups=valid_groups)
        assert config.dh_groups == valid_groups

        # Invalid DH group
        with pytest.raises(ValidationError):
            CryptoConfig(dh_groups=[99])  # Invalid group

        # Mixed valid and invalid
        with pytest.raises(ValidationError):
            CryptoConfig(dh_groups=[19, 99])


class TestAuthConfig:
    """Test suite for AuthConfig model."""

    def test_auth_config_defaults(self):
        """Test AuthConfig default values."""
        config = AuthConfig()

        assert config.auth_method == "ECDSA_SECP256R1_SHA256"
        assert config.certificate_auth is True
        assert config.psk_auth is False

    def test_auth_config_valid_methods(self):
        """Test valid authentication methods."""
        valid_methods = [
            "ECDSA_SECP256R1_SHA256",
            "ECDSA_SECP384R1_SHA384",
            "RSA_PSS_SHA256",
            "RSA_PSS_SHA384",
            "PSK"
        ]

        for method in valid_methods:
            config = AuthConfig(auth_method=method)
            assert config.auth_method == method

    def test_auth_config_invalid_method(self):
        """Test invalid authentication method."""
        with pytest.raises(ValidationError):
            AuthConfig(auth_method="INVALID_METHOD")

    def test_auth_config_custom_values(self):
        """Test AuthConfig with custom values."""
        config = AuthConfig(
            auth_method="PSK",
            certificate_auth=False,
            psk_auth=True
        )

        assert config.auth_method == "PSK"
        assert config.certificate_auth is False
        assert config.psk_auth is True


class TestTSConfig:
    """Test suite for TSConfig model."""

    def test_ts_config_defaults(self):
        """Test TSConfig default values."""
        config = TSConfig()

        assert config.tsi_ip_range == ("***********", "************")
        assert config.tsi_port_range == (0, 65535)
        assert config.tsr_ip_range == ("10.0.0.0", "********54")
        assert config.tsr_port_range == (0, 65535)

    def test_ts_config_port_range_validation(self):
        """Test port range validation."""
        # Tuple format
        config = TSConfig(
            tsi_port_range=(80, 443),
            tsr_port_range=(8080, 9090)
        )
        assert config.tsi_port_range == (80, 443)
        assert config.tsr_port_range == (8080, 9090)

        # String format
        config = TSConfig(
            tsi_port_range="80-443",
            tsr_port_range="8080-9090"
        )
        assert config.tsi_port_range == (80, 443)
        assert config.tsr_port_range == (8080, 9090)

        # String tuple format
        config = TSConfig(
            tsi_port_range=("80", "443"),
            tsr_port_range=("8080", "9090")
        )
        assert config.tsi_port_range == (80, 443)
        assert config.tsr_port_range == (8080, 9090)

    def test_ts_config_ip_range_validation(self):
        """Test IP range validation."""
        # Tuple format
        config = TSConfig(
            tsi_ip_range=("***********", "***********00"),
            tsr_ip_range=("********", "*********0")
        )
        assert config.tsi_ip_range == ("***********", "***********00")
        assert config.tsr_ip_range == ("********", "*********0")

        # String format
        config = TSConfig(
            tsi_ip_range="***********-***********00",
            tsr_ip_range="********-*********0"
        )
        assert config.tsi_ip_range == ("***********", "***********00")
        assert config.tsr_ip_range == ("********", "*********0")


class TestIDConfig:
    """Test suite for IDConfig model."""

    def test_id_config_defaults(self):
        """Test IDConfig default values."""
        config = IDConfig()

        assert config.idi_type == "ID_FQDN"
        assert config.idi_data == "debian-client.lan"
        assert config.idr_type == "ID_FQDN"
        assert config.idr_data == "strongswan-gw.lan"

    def test_id_config_valid_types(self):
        """Test valid ID types."""
        valid_types = ["ID_FQDN", "ID_RFC822_ADDR", "ID_IPV4_ADDR", "ID_IPV6_ADDR", "ID_DER_ASN1_DN"]

        for id_type in valid_types:
            config = IDConfig(idi_type=id_type, idr_type=id_type)
            assert config.idi_type == id_type
            assert config.idr_type == id_type

    def test_id_config_invalid_types(self):
        """Test invalid ID types."""
        with pytest.raises(ValidationError):
            IDConfig(idi_type="INVALID_TYPE")

        with pytest.raises(ValidationError):
            IDConfig(idr_type="INVALID_TYPE")


class TestNotifyPayload:
    """Test suite for NotifyPayload model."""

    def test_notify_payload_creation(self):
        """Test NotifyPayload creation."""
        payload = NotifyPayload(notify_type="NAT_DETECTION_SOURCE_IP")

        assert payload.notify_type == "NAT_DETECTION_SOURCE_IP"
        assert payload.data == b""

    def test_notify_payload_with_data(self):
        """Test NotifyPayload with data."""
        # Bytes data
        payload = NotifyPayload(
            notify_type="COOKIE",
            data=b"\x01\x02\x03\x04"
        )
        assert payload.data == b"\x01\x02\x03\x04"

        # Hex string data
        payload = NotifyPayload(
            notify_type="COOKIE",
            data="01020304"
        )
        assert payload.data == b"\x01\x02\x03\x04"

    def test_notify_payload_invalid_hex_data(self):
        """Test NotifyPayload with invalid hex data."""
        with pytest.raises(ValidationError):
            NotifyPayload(
                notify_type="COOKIE",
                data="invalid_hex"
            )

    def test_notify_payload_string_representation(self):
        """Test NotifyPayload string representation."""
        payload = NotifyPayload(
            notify_type="COOKIE",
            data=b"\x01\x02\x03\x04"
        )

        str_repr = str(payload)
        assert "Notify COOKIE" in str_repr
        assert "01020304" in str_repr


class TestNATConfig:
    """Test suite for NATConfig model."""

    def test_nat_config_defaults(self):
        """Test NATConfig default values."""
        config = NATConfig()

        assert config.nat_t is False
        assert config.nat_port_src == 4500
        assert config.nat_port_dst == 4500

    def test_nat_config_boolean_validation(self):
        """Test NAT-T boolean validation."""
        # Boolean values
        config = NATConfig(nat_t=True)
        assert config.nat_t is True

        config = NATConfig(nat_t=False)
        assert config.nat_t is False

        # String values (should be converted)
        config = NATConfig(nat_t="true")
        assert config.nat_t is True

        config = NATConfig(nat_t="false")
        assert config.nat_t is False


class TestESPTests:
    """Test suite for ESPTests model."""

    def test_esp_tests_defaults(self):
        """Test ESPTests default values."""
        tests = ESPTests()

        assert tests.crypto is False
        assert tests.replay is False
        assert tests.sp is False
        assert tests.nat_t is False

    def test_esp_tests_boolean_validation(self):
        """Test ESPTests boolean validation."""
        tests = ESPTests(
            crypto=True,
            replay="true",
            sp=False,
            nat_t="false"
        )

        assert tests.crypto is True
        assert tests.replay is True
        assert tests.sp is False
        assert tests.nat_t is False


class TestESPConfig:
    """Test suite for ESPConfig model."""

    def test_esp_config_defaults(self):
        """Test ESPConfig default values."""
        config = ESPConfig()

        assert isinstance(config.tests, ESPTests)
        assert config.when == 12

    def test_esp_config_custom_values(self):
        """Test ESPConfig with custom values."""
        tests = ESPTests(crypto=True, replay=True)
        config = ESPConfig(tests=tests, when=20)

        assert config.tests.crypto is True
        assert config.tests.replay is True
        assert config.when == 20


class TestIPsecConfig:
    """Test suite for IPsecConfig model."""

    def test_ipsec_config_defaults(self):
        """Test IPsecConfig default values."""
        config = IPsecConfig()

        assert isinstance(config.crypto, CryptoConfig)
        assert isinstance(config.auth, AuthConfig)
        assert isinstance(config.ts, TSConfig)
        assert isinstance(config.identity, IDConfig)
        assert isinstance(config.nat, NATConfig)
        assert config.childless is True
        assert config.include_idr is False
        assert config.include_init_contact is False

    def test_ipsec_config_notify_payloads(self):
        """Test IPsecConfig notify payloads."""
        notify_init = [NotifyPayload(notify_type="NAT_DETECTION_SOURCE_IP")]
        notify_auth = [NotifyPayload(notify_type="COOKIE", data=b"\x01\x02")]

        config = IPsecConfig(
            notify_extras_init=notify_init,
            notify_options_auth=notify_auth
        )

        assert config.notify_extras_init is not None
        assert len(config.notify_extras_init) == 1
        assert config.notify_extras_init[0].notify_type == "NAT_DETECTION_SOURCE_IP"
        assert config.notify_options_auth is not None
        assert len(config.notify_options_auth) == 1
        assert config.notify_options_auth[0].notify_type == "COOKIE"

    def test_ipsec_config_boolean_flags_validation(self):
        """Test IPsecConfig boolean flags validation."""
        config = IPsecConfig(
            childless="true",
            include_idr="false",
            include_init_contact=True
        )

        assert config.childless is True
        assert config.include_idr is False
        assert config.include_init_contact is True


class TestTestConfiguration:
    """Test suite for TestConfiguration model."""

    def test_test_configuration_defaults(self):
        """Test TestConfiguration default values."""
        config = TestConfiguration()

        assert isinstance(config.global_config, GlobalConfig)
        assert isinstance(config.network, NetworkConfig)
        assert isinstance(config.crypto, CryptoConfig)
        assert config.pki is None

    def test_test_configuration_with_pki(self):
        """Test TestConfiguration with PKI config."""
        # Create temporary files for PKI
        with tempfile.NamedTemporaryFile(delete=False) as ca_cert:
            ca_cert_path = ca_cert.name
        with tempfile.NamedTemporaryFile(delete=False) as cert:
            cert_path = cert.name
        with tempfile.NamedTemporaryFile(delete=False) as key:
            key_path = key.name

        try:
            pki_config = PKIConfig(
                ca_cert_path=ca_cert_path,
                cert_path=cert_path,
                key_path=key_path
            )

            config = TestConfiguration(pki=pki_config)

            assert config.pki is not None
            assert config.pki.ca_cert_path == ca_cert_path

        finally:
            # Clean up
            os.unlink(ca_cert_path)
            os.unlink(cert_path)
            os.unlink(key_path)

    def test_test_configuration_integration(self):
        """Test TestConfiguration with all components."""
        global_config = GlobalConfig(timeout=60, verbose=True)
        network_config = NetworkConfig(interface="eth1", port_src=8080)
        crypto_config = CryptoConfig(dh_groups=[19, 20])

        config = TestConfiguration(
            global_config=global_config,
            network=network_config,
            crypto=crypto_config
        )

        assert config.global_config.timeout == 60
        assert config.global_config.verbose is True
        assert config.network.interface == "eth1"
        assert config.network.port_src == 8080
        assert 19 in config.crypto.dh_groups
        assert 20 in config.crypto.dh_groups


class TestConfigModelIntegration:
    """Test suite for configuration model integration."""

    def test_config_model_extra_fields(self):
        """Test that ConfigModel allows extra fields."""
        # All config models should inherit from ConfigModel
        config = GlobalConfig(extra_field="allowed")
        assert config.extra_field == "allowed"

        config = NetworkConfig(custom_setting="test")
        assert config.custom_setting == "test"

    def test_config_validation_errors(self):
        """Test comprehensive validation error scenarios."""
        # Multiple validation errors
        with pytest.raises(ValidationError) as exc_info:
            GlobalConfig(
                timeout=-1,  # Invalid
                log_level="INVALID",  # Invalid
                max_concurrent_tests=0  # Invalid
            )

        errors = exc_info.value.errors()
        assert len(errors) >= 2  # Should have multiple errors

    def test_config_serialization(self):
        """Test configuration serialization."""
        config = IPsecConfig()

        # Should be serializable
        serialized = config.model_dump()
        assert isinstance(serialized, dict)
        assert "crypto" in serialized
        assert "auth" in serialized

        # Should be deserializable
        new_config = IPsecConfig(**serialized)
        assert new_config.crypto.encryption_algorithms == config.crypto.encryption_algorithms
