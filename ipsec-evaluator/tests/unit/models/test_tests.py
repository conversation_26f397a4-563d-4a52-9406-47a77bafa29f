"""
Unit tests for test results and compliance reporting models.

This module provides comprehensive tests for test models including:
- Scenario model validation and list length checking
- Outcome model result validation and combination
- Summary model outcome aggregation
- TestResult model status validation
- ComplianceResult model scoring
- ComplianceReport model generation and serialization
- Test model scenario management
- TestPoolEntry model execution tracking
- Model serialization and deserialization
"""

import pytest
import tempfile
import json
from pathlib import Path
from datetime import datetime
from pydantic import ValidationError

from ipsecator.models.tests import (
    Scenario,
    Outcome,
    Summary,
    ComplianceResult,
    TestResult,
    ComplianceReport,
    Test,
    TestPoolEntry,
)
from ipsecator.models.base import ComplianceLevel


class TestScenario:
    """Test suite for Scenario model."""

    def test_scenario_creation(self):
        """Test basic Scenario creation."""
        scenario = Scenario()

        assert scenario.exchanges == []
        assert scenario.overlay_configs == []
        assert scenario.check_functions == []

    def test_scenario_with_data(self):
        """Test Scenario with data."""
        scenario = Scenario(
            exchanges=["IKE_SA_INIT", "IKE_AUTH"],
            overlay_configs=[{"config1": "value1"}, {"config2": "value2"}],
            check_functions=["check1", "check2"]
        )

        assert scenario.exchanges == ["IKE_SA_INIT", "IKE_AUTH"]
        assert len(scenario.overlay_configs) == 2
        assert scenario.overlay_configs[0] == {"config1": "value1"}
        assert scenario.check_functions == ["check1", "check2"]

    def test_scenario_list_length_validation(self):
        """Test that all lists must have the same length."""
        # Valid - all same length
        scenario = Scenario(
            exchanges=["IKE_SA_INIT", "IKE_AUTH"],
            overlay_configs=[{"config1": "value1"}, {"config2": "value2"}],
            check_functions=["check1", "check2"]
        )
        assert len(scenario.exchanges) == 2

        # Invalid - different lengths
        with pytest.raises(ValidationError):
            Scenario(
                exchanges=["IKE_SA_INIT", "IKE_AUTH"],
                overlay_configs=[{"config1": "value1"}],  # Length 1
                check_functions=["check1", "check2"]  # Length 2
            )

        # Invalid - mismatched lengths
        with pytest.raises(ValidationError):
            Scenario(
                exchanges=["IKE_SA_INIT"],
                overlay_configs=[{"config1": "value1"}, {"config2": "value2"}],
                check_functions=["check1"]
            )

    def test_scenario_with_none_values(self):
        """Test Scenario with None values in lists."""
        scenario = Scenario(
            exchanges=["IKE_SA_INIT", "IKE_AUTH"],
            overlay_configs=[{"config1": "value1"}, None],
            check_functions=[None, "check2"]
        )

        assert scenario.exchanges == ["IKE_SA_INIT", "IKE_AUTH"]
        assert scenario.overlay_configs[0] == {"config1": "value1"}
        assert scenario.overlay_configs[1] is None
        assert scenario.check_functions[0] is None
        assert scenario.check_functions[1] == "check2"


class TestOutcome:
    """Test suite for Outcome model."""

    def test_outcome_creation(self):
        """Test basic Outcome creation."""
        outcome = Outcome(test_id=1)

        assert outcome.test_id == 1
        assert outcome.test_name is None
        assert outcome.result == "Unknown"
        assert outcome.reason is None
        assert outcome.details is None

    def test_outcome_with_all_fields(self):
        """Test Outcome with all fields."""
        outcome = Outcome(
            test_id=42,
            test_name="Test Authentication",
            result="Passed",
            reason="All checks passed",
            details={"duration": 1.5, "packets": 10}
        )

        assert outcome.test_id == 42
        assert outcome.test_name == "Test Authentication"
        assert outcome.result == "Passed"
        assert outcome.reason == "All checks passed"
        assert outcome.details == {"duration": 1.5, "packets": 10}

    def test_outcome_result_validation(self):
        """Test result field validation."""
        # Valid string results
        for result in ["Passed", "Failed", "Unknown"]:
            outcome = Outcome(test_id=1, result=result)
            assert outcome.result == result

        # Boolean to string conversion
        outcome = Outcome(test_id=1, result=True)
        assert outcome.result == "Passed"

        outcome = Outcome(test_id=1, result=False)
        assert outcome.result == "Failed"

        # Invalid result
        with pytest.raises(ValidationError):
            Outcome(test_id=1, result="Invalid")

    def test_outcome_pretty_print(self):
        """Test pretty_print method."""
        outcome = Outcome(
            test_id=1,
            test_name="Test Case",
            result="Passed",
            reason="Success",
            details={"key": "value"}
        )

        output = outcome.pretty_print()
        assert "Test 1" in output
        assert "Test Case" in output
        assert "Passed" in output
        assert "Reason: Success" in output
        assert "Details: {'key': 'value'}" in output

    def test_outcome_str_representation(self):
        """Test string representation."""
        outcome = Outcome(test_id=1, result="Passed")
        str_repr = str(outcome)
        assert "Test 1" in str_repr
        assert "Passed" in str_repr

    def test_outcome_addition(self):
        """Test outcome addition operator."""
        outcome1 = Outcome(
            test_id=1,
            test_name="Test 1",
            result="Passed",
            reason="First passed",
            details={"test1": "data1"}
        )

        outcome2 = Outcome(
            test_id=1,
            test_name="Test 1",
            result="Passed",
            reason="Second passed",
            details={"test2": "data2"}
        )

        # Both passed -> combined passed
        combined = outcome1 + outcome2
        assert combined.result == "Passed"
        assert "First passed; Second passed" in combined.reason
        assert combined.details == {"test1": "data1", "test2": "data2"}

        # One failed -> combined failed
        outcome2.result = "Failed"
        combined = outcome1 + outcome2
        assert combined.result == "Failed"

    def test_outcome_addition_invalid(self):
        """Test outcome addition with invalid operand."""
        outcome = Outcome(test_id=1, result="Passed")

        # Adding non-Outcome should raise TypeError
        with pytest.raises(TypeError):
            outcome + "invalid"


class TestSummary:
    """Test suite for Summary model."""

    def test_summary_creation(self):
        """Test basic Summary creation."""
        summary = Summary()

        assert summary.outcomes == []

    def test_summary_add_outcome(self):
        """Test adding outcomes to summary."""
        summary = Summary()

        outcome1 = Outcome(test_id=1, result="Passed")
        outcome2 = Outcome(test_id=2, result="Failed")

        summary.add_outcome(outcome1)
        summary.add_outcome(outcome2)

        assert len(summary.outcomes) == 2
        assert outcome1 in summary.outcomes
        assert outcome2 in summary.outcomes

    def test_summary_pretty_print(self):
        """Test pretty_print method."""
        summary = Summary()

        outcome1 = Outcome(test_id=1, result="Passed")
        outcome2 = Outcome(test_id=2, result="Failed")

        summary.add_outcome(outcome1)
        summary.add_outcome(outcome2)

        output = summary.pretty_print()
        assert "Test Summary:" in output
        assert "Test 1" in output
        assert "Test 2" in output
        assert "Passed" in output
        assert "Failed" in output

    def test_summary_str_representation(self):
        """Test string representation."""
        summary = Summary()
        outcome = Outcome(test_id=1, result="Passed")
        summary.add_outcome(outcome)

        str_repr = str(summary)
        assert "Test Summary:" in str_repr
        assert "Test 1" in str_repr


class TestComplianceResult:
    """Test suite for ComplianceResult model."""

    def test_compliance_result_creation(self):
        """Test basic ComplianceResult creation."""
        result = ComplianceResult(level=ComplianceLevel.COMPLIANT)

        assert result.level == ComplianceLevel.COMPLIANT
        assert result.score == 0.0
        assert result.issues == []
        assert result.warnings == []
        assert result.recommendations == []

    def test_compliance_result_with_data(self):
        """Test ComplianceResult with data."""
        result = ComplianceResult(
            level=ComplianceLevel.PARTIAL,
            score=75.5,
            issues=["Issue 1", "Issue 2"],
            warnings=["Warning 1"],
            recommendations=["Recommendation 1", "Recommendation 2"]
        )

        assert result.level == ComplianceLevel.PARTIAL
        assert result.score == 75.5
        assert result.issues == ["Issue 1", "Issue 2"]
        assert result.warnings == ["Warning 1"]
        assert result.recommendations == ["Recommendation 1", "Recommendation 2"]


class TestTestResult:
    """Test suite for TestResult model."""

    def test_test_result_creation(self):
        """Test basic TestResult creation."""
        result = TestResult(
            test_id="test-001",
            test_name="Basic IKE Test",
            status="passed"
        )

        assert result.test_id == "test-001"
        assert result.test_name == "Basic IKE Test"
        assert result.status == "passed"
        assert result.duration == 0.0
        assert result.error_message is None
        assert result.details == {}

    def test_test_result_with_all_fields(self):
        """Test TestResult with all fields."""
        result = TestResult(
            test_id="test-002",
            test_name="Advanced Test",
            status="failed",
            duration=5.5,
            error_message="Connection timeout",
            details={"attempts": 3, "last_error": "timeout"}
        )

        assert result.test_id == "test-002"
        assert result.test_name == "Advanced Test"
        assert result.status == "failed"
        assert result.duration == 5.5
        assert result.error_message == "Connection timeout"
        assert result.details == {"attempts": 3, "last_error": "timeout"}

    def test_test_result_status_validation(self):
        """Test status validation."""
        # Valid statuses
        valid_statuses = ["passed", "failed", "skipped", "error"]
        for status in valid_statuses:
            result = TestResult(
                test_id="test",
                test_name="Test",
                status=status
            )
            assert result.status == status

        # Case insensitive
        result = TestResult(
            test_id="test",
            test_name="Test",
            status="PASSED"
        )
        assert result.status == "passed"

        # Invalid status
        with pytest.raises(ValidationError):
            TestResult(
                test_id="test",
                test_name="Test",
                status="invalid"
            )


class TestComplianceReport:
    """Test suite for ComplianceReport model."""

    def test_compliance_report_creation(self):
        """Test basic ComplianceReport creation."""
        report = ComplianceReport()

        assert isinstance(report.generated_at, datetime)
        assert report.total_tests == 0
        assert report.passed_tests == 0
        assert report.failed_tests == 0
        assert report.results == []
        assert report.created_at is not None

    def test_compliance_report_with_results(self):
        """Test ComplianceReport with test results."""
        results = [
            TestResult(
                test_id="test-001",
                test_name="Test 1",
                status="passed"
            ),
            TestResult(
                test_id="test-002",
                test_name="Test 2",
                status="failed"
            )
        ]

        report = ComplianceReport(
            total_tests=2,
            passed_tests=1,
            failed_tests=1,
            results=results
        )

        assert report.total_tests == 2
        assert report.passed_tests == 1
        assert report.failed_tests == 1
        assert len(report.results) == 2

    def test_compliance_report_save_to_file(self):
        """Test saving ComplianceReport to file."""
        report = ComplianceReport(
            total_tests=1,
            passed_tests=1,
            failed_tests=0,
            results=[
                TestResult(
                    test_id="test-001",
                    test_name="Test 1",
                    status="passed"
                )
            ]
        )

        # Save to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_file = Path(f.name)

        try:
            report.save_to_file(temp_file)

            # Verify file was created and contains valid JSON
            assert temp_file.exists()

            with open(temp_file, 'r') as f:
                data = json.load(f)

            assert data["total_tests"] == 1
            assert data["passed_tests"] == 1
            assert data["failed_tests"] == 0
            assert len(data["results"]) == 1
            assert data["results"][0]["test_id"] == "test-001"

        finally:
            # Clean up
            temp_file.unlink()


class TestTest:
    """Test suite for Test model."""

    def test_test_creation(self):
        """Test basic Test creation."""
        test = Test(name="Basic IKE Test")

        assert test.name == "Basic IKE Test"
        assert test.initiator_scenarios == {}
        assert test.responder_scenarios == {}

    def test_test_with_scenarios(self):
        """Test Test with scenarios."""
        scenario1 = Scenario(
            exchanges=["IKE_SA_INIT"],
            overlay_configs=[{"config": "value"}],
            check_functions=["check1"]
        )
        scenario2 = Scenario(
            exchanges=["IKE_AUTH"],
            overlay_configs=[{"config": "value2"}],
            check_functions=["check2"]
        )

        test = Test(
            name="Complex Test",
            initiator_scenarios={"scenario1": [scenario1]},
            responder_scenarios={"scenario2": [scenario2]}
        )

        assert test.name == "Complex Test"
        assert "scenario1" in test.initiator_scenarios
        assert len(test.initiator_scenarios["scenario1"]) == 1
        assert "scenario2" in test.responder_scenarios
        assert len(test.responder_scenarios["scenario2"]) == 1


class TestTestPoolEntry:
    """Test suite for TestPoolEntry model."""

    def test_test_pool_entry_creation(self):
        """Test basic TestPoolEntry creation."""
        scenario = Scenario(
            exchanges=["IKE_SA_INIT"],
            overlay_configs=[{"config": "value"}],
            check_functions=["check1"]
        )

        entry = TestPoolEntry(
            test_id=1,
            test_name="Pool Test",
            mode="initiator",
            scenario_name="test_scenario",
            scenario=scenario,
            scenario_count=1
        )

        assert entry.test_id == 1
        assert entry.test_name == "Pool Test"
        assert entry.mode == "initiator"
        assert entry.lock_status == 0
        assert entry.outcome is None
        assert entry.scenario_name == "test_scenario"
        assert entry.scenario == scenario
        assert entry.scenario_count == 1
        assert entry.packet_pipe is None
        assert entry.tester is None
        assert entry.checker is None

    def test_test_pool_entry_with_outcome(self):
        """Test TestPoolEntry with outcome."""
        scenario = Scenario(
            exchanges=["IKE_SA_INIT"],
            overlay_configs=[{"config": "value"}],
            check_functions=["check1"]
        )

        outcome = Outcome(test_id=1, result="Passed")

        entry = TestPoolEntry(
            test_id=1,
            test_name="Pool Test",
            mode="responder",
            lock_status=2,
            outcome=outcome,
            scenario_name="test_scenario",
            scenario=scenario,
            scenario_count=1
        )

        assert entry.lock_status == 2
        assert entry.outcome == outcome
        assert entry.outcome.result == "Passed"

    def test_test_pool_entry_serialization(self):
        """Test TestPoolEntry serialization excludes certain fields."""
        scenario = Scenario(
            exchanges=["IKE_SA_INIT"],
            overlay_configs=[{"config": "value"}],
            check_functions=["check1"]
        )

        entry = TestPoolEntry(
            test_id=1,
            test_name="Pool Test",
            mode="initiator",
            scenario_name="test_scenario",
            scenario=scenario,
            scenario_count=1,
            packet_pipe="mock_pipe",  # Should be excluded
            tester="mock_tester",  # Should be excluded
            checker="mock_checker"  # Should be excluded
        )

        # Serialize to dict
        serialized = entry.model_dump()

        # Excluded fields should not be present
        assert "packet_pipe" not in serialized
        assert "tester" not in serialized
        assert "checker" not in serialized

        # Other fields should be present
        assert serialized["test_id"] == 1
        assert serialized["test_name"] == "Pool Test"
        assert serialized["mode"] == "initiator"
