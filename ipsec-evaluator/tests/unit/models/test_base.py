"""
Unit tests for base models and common data structures.

This module provides comprehensive tests for base models including:
- BaseModel and ConfigModel functionality
- Enum definitions and validation
- TimestampedModel behavior
- IdentifiedModel UUID generation
- MetadataModel operations
- Model configuration and validation
"""

import pytest
from datetime import datetime, timezone
from uuid import UUID
from pydantic import ValidationError

from ipsecator.models.base import (
    BaseModel,
    ConfigModel,
    TimestampedModel,
    IdentifiedModel,
    MetadataModel,
    ExchangeType,
    TestMode,
    TestStatus,
    ComplianceLevel,
    HookType,
)


class TestBaseModel:
    """Test suite for BaseModel class."""

    def test_base_model_creation(self):
        """Test basic BaseModel creation."""
        
        class TestModel(BaseModel):
            name: str
            value: int
        
        model = TestModel(name="test", value=42)
        assert model.name == "test"
        assert model.value == 42

    def test_base_model_extra_forbid(self):
        """Test that BaseModel forbids extra fields."""
        
        class TestModel(BaseModel):
            name: str
        
        with pytest.raises(ValidationError):
            TestModel(name="test", extra_field="not_allowed")

    def test_base_model_validate_assignment(self):
        """Test that BaseModel validates assignments."""
        
        class TestModel(BaseModel):
            value: int
        
        model = TestModel(value=42)
        
        # Valid assignment
        model.value = 100
        assert model.value == 100
        
        # Invalid assignment should raise error
        with pytest.raises(ValidationError):
            model.value = "not_an_int"

    def test_base_model_use_enum_values(self):
        """Test that BaseModel uses enum values."""
        
        class TestModel(BaseModel):
            status: TestStatus
        
        model = TestModel(status=TestStatus.PENDING)
        assert model.status == "pending"  # Should use enum value


class TestConfigModel:
    """Test suite for ConfigModel class."""

    def test_config_model_extra_allow(self):
        """Test that ConfigModel allows extra fields."""
        
        class TestConfig(ConfigModel):
            name: str
        
        # Should not raise error with extra fields
        config = TestConfig(name="test", extra_field="allowed")
        assert config.name == "test"
        assert config.extra_field == "allowed"

    def test_config_model_validate_assignment(self):
        """Test that ConfigModel validates assignments."""
        
        class TestConfig(ConfigModel):
            value: int
        
        config = TestConfig(value=42)
        
        # Valid assignment
        config.value = 100
        assert config.value == 100
        
        # Invalid assignment should raise error
        with pytest.raises(ValidationError):
            config.value = "not_an_int"


class TestEnums:
    """Test suite for enum definitions."""

    def test_exchange_type_enum(self):
        """Test ExchangeType enum values."""
        assert ExchangeType.INIT == "IKE_SA_INIT"
        assert ExchangeType.AUTH == "IKE_AUTH"
        assert ExchangeType.CREATE_CHILD == "CREATE_CHILD_SA"
        assert ExchangeType.INFORMATIONAL == "INFORMATIONAL"
        assert ExchangeType.ESP == "ESP"
        
        # Test all values are strings
        for exchange_type in ExchangeType:
            assert isinstance(exchange_type.value, str)

    def test_test_mode_enum(self):
        """Test TestMode enum values."""
        assert TestMode.INITIATOR == "initiator"
        assert TestMode.RESPONDER == "responder"
        assert TestMode.BOTH == "both"
        
        # Test all values are strings
        for test_mode in TestMode:
            assert isinstance(test_mode.value, str)

    def test_test_status_enum(self):
        """Test TestStatus enum values."""
        assert TestStatus.PENDING == "pending"
        assert TestStatus.RUNNING == "running"
        assert TestStatus.COMPLETED == "completed"
        assert TestStatus.FAILED == "failed"
        assert TestStatus.CANCELLED == "cancelled"
        
        # Test all values are strings
        for status in TestStatus:
            assert isinstance(status.value, str)

    def test_compliance_level_enum(self):
        """Test ComplianceLevel enum values."""
        assert ComplianceLevel.COMPLIANT == "compliant"
        assert ComplianceLevel.NON_COMPLIANT == "non_compliant"
        assert ComplianceLevel.PARTIAL == "partial"
        assert ComplianceLevel.UNKNOWN == "unknown"
        
        # Test all values are strings
        for level in ComplianceLevel:
            assert isinstance(level.value, str)

    def test_hook_type_enum(self):
        """Test HookType enum values."""
        assert HookType.PACKET_NUMBER == "packet_number"
        assert HookType.EXCHANGE_STEP == "exchange_step"
        assert HookType.UNIVERSAL == "universal"
        assert HookType.PRE_EXCHANGE == "pre_exchange"
        assert HookType.POST_EXCHANGE == "post_exchange"
        
        # Test all values are strings
        for hook_type in HookType:
            assert isinstance(hook_type.value, str)


class TestTimestampedModel:
    """Test suite for TimestampedModel class."""

    def test_timestamped_model_creation(self):
        """Test TimestampedModel automatic timestamp creation."""
        
        class TestModel(TimestampedModel):
            name: str
        
        before_creation = datetime.now(timezone.utc)
        model = TestModel(name="test")
        after_creation = datetime.now(timezone.utc)
        
        assert model.name == "test"
        assert model.created_at is not None
        assert model.updated_at is None
        assert before_creation <= model.created_at <= after_creation

    def test_timestamped_model_update_timestamp(self):
        """Test update_timestamp method."""
        
        class TestModel(TimestampedModel):
            name: str
        
        model = TestModel(name="test")
        original_created_at = model.created_at
        
        # Update timestamp
        before_update = datetime.now(timezone.utc)
        model.update_timestamp()
        after_update = datetime.now(timezone.utc)
        
        assert model.created_at == original_created_at  # Should not change
        assert model.updated_at is not None
        assert before_update <= model.updated_at <= after_update

    def test_timestamped_model_serialization(self):
        """Test datetime serialization."""
        
        class TestModel(TimestampedModel):
            name: str
        
        model = TestModel(name="test")
        model.update_timestamp()
        
        # Test serialization
        serialized = model.model_dump()
        assert isinstance(serialized["created_at"], str)
        assert isinstance(serialized["updated_at"], str)
        
        # Test ISO format
        assert "T" in serialized["created_at"]
        assert "T" in serialized["updated_at"]

    def test_timestamped_model_custom_timestamp(self):
        """Test TimestampedModel with custom timestamp."""
        
        class TestModel(TimestampedModel):
            name: str
        
        custom_time = datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
        model = TestModel(name="test", created_at=custom_time)
        
        assert model.created_at == custom_time


class TestIdentifiedModel:
    """Test suite for IdentifiedModel class."""

    def test_identified_model_creation(self):
        """Test IdentifiedModel automatic ID generation."""
        
        class TestModel(IdentifiedModel):
            value: int
        
        model = TestModel(value=42)
        
        assert isinstance(model.id, UUID)
        assert model.name is None
        assert model.description is None
        assert model.value == 42

    def test_identified_model_with_name_description(self):
        """Test IdentifiedModel with name and description."""
        
        class TestModel(IdentifiedModel):
            value: int
        
        model = TestModel(
            value=42,
            name="test_model",
            description="A test model"
        )
        
        assert isinstance(model.id, UUID)
        assert model.name == "test_model"
        assert model.description == "A test model"

    def test_identified_model_unique_ids(self):
        """Test that IdentifiedModel generates unique IDs."""
        
        class TestModel(IdentifiedModel):
            value: int
        
        model1 = TestModel(value=1)
        model2 = TestModel(value=2)
        
        assert model1.id != model2.id

    def test_identified_model_custom_id(self):
        """Test IdentifiedModel with custom ID."""
        
        class TestModel(IdentifiedModel):
            value: int
        
        custom_id = UUID("12345678-1234-5678-9abc-123456789abc")
        model = TestModel(value=42, id=custom_id)
        
        assert model.id == custom_id


class TestMetadataModel:
    """Test suite for MetadataModel class."""

    def test_metadata_model_creation(self):
        """Test MetadataModel creation with empty metadata."""
        
        class TestModel(MetadataModel):
            name: str
        
        model = TestModel(name="test")
        
        assert model.name == "test"
        assert model.metadata == {}

    def test_metadata_model_add_metadata(self):
        """Test adding metadata entries."""
        
        class TestModel(MetadataModel):
            name: str
        
        model = TestModel(name="test")
        
        model.add_metadata("key1", "value1")
        model.add_metadata("key2", 42)
        model.add_metadata("key3", {"nested": "dict"})
        
        assert model.metadata["key1"] == "value1"
        assert model.metadata["key2"] == 42
        assert model.metadata["key3"] == {"nested": "dict"}

    def test_metadata_model_get_metadata(self):
        """Test getting metadata entries."""
        
        class TestModel(MetadataModel):
            name: str
        
        model = TestModel(name="test")
        model.add_metadata("existing_key", "existing_value")
        
        # Get existing key
        assert model.get_metadata("existing_key") == "existing_value"
        
        # Get non-existing key with default
        assert model.get_metadata("non_existing_key") is None
        assert model.get_metadata("non_existing_key", "default") == "default"

    def test_metadata_model_initial_metadata(self):
        """Test MetadataModel with initial metadata."""
        
        class TestModel(MetadataModel):
            name: str
        
        initial_metadata = {"key1": "value1", "key2": 42}
        model = TestModel(name="test", metadata=initial_metadata)
        
        assert model.metadata == initial_metadata
        assert model.get_metadata("key1") == "value1"
        assert model.get_metadata("key2") == 42


class TestModelCombinations:
    """Test suite for combined model functionality."""

    def test_timestamped_identified_model(self):
        """Test model combining TimestampedModel and IdentifiedModel."""
        
        class TestModel(TimestampedModel, IdentifiedModel):
            name: str
        
        model = TestModel(name="test")
        
        # Should have both timestamp and ID functionality
        assert isinstance(model.id, UUID)
        assert model.created_at is not None
        assert model.updated_at is None
        assert model.name == "test"

    def test_full_featured_model(self):
        """Test model combining all base model features."""
        
        class TestModel(TimestampedModel, IdentifiedModel, MetadataModel):
            name: str
            value: int
        
        model = TestModel(name="test", value=42)
        
        # Should have all functionality
        assert isinstance(model.id, UUID)
        assert model.created_at is not None
        assert model.updated_at is None
        assert model.metadata == {}
        assert model.name == "test"
        assert model.value == 42
        
        # Test all methods work
        model.update_timestamp()
        model.add_metadata("test_key", "test_value")
        
        assert model.updated_at is not None
        assert model.get_metadata("test_key") == "test_value"
