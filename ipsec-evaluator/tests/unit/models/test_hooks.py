"""
Unit tests for hook system models.

This module provides comprehensive tests for hook models including:
- HookDefinition base functionality
- CallbackConfig validation
- HookResult tracking
- PacketHook packet number validation
- ExchangeHook exchange step validation
- UniversalHook custom conditions
- HookRegistry management
- Hook execution and filtering
- Priority and ordering
"""

import pytest
from uuid import UUID
from pydantic import ValidationError

from ipsecator.models.hooks import (
    HookDefinition,
    CallbackConfig,
    HookResult,
    PacketHook,
    ExchangeHook,
    UniversalHook,
    HookRegistry,
)
from ipsecator.models.base import HookType


class TestHookDefinition:
    """Test suite for HookDefinition model."""

    def test_hook_definition_creation(self):
        """Test basic HookDefinition creation."""
        hook = HookDefinition(
            hook_type=HookType.PACKET_NUMBER,
            trigger_condition="packet == 5",
            callback_function="test_callback"
        )
        
        assert isinstance(hook.id, UUID)
        assert hook.hook_type == HookType.PACKET_NUMBER
        assert hook.trigger_condition == "packet == 5"
        assert hook.callback_function == "test_callback"
        assert hook.priority == 100  # Default
        assert hook.enabled is True  # Default
        assert hook.packet_number is None
        assert hook.exchange_step is None

    def test_hook_definition_with_all_fields(self):
        """Test HookDefinition with all fields."""
        hook = HookDefinition(
            name="Test Hook",
            description="A test hook",
            hook_type=HookType.EXCHANGE_STEP,
            trigger_condition="exchange == 'IKE_AUTH'",
            callback_function="auth_callback",
            priority=50,
            enabled=False,
            packet_number=10,
            exchange_step="IKE_AUTH"
        )
        
        assert hook.name == "Test Hook"
        assert hook.description == "A test hook"
        assert hook.hook_type == HookType.EXCHANGE_STEP
        assert hook.priority == 50
        assert hook.enabled is False
        assert hook.packet_number == 10
        assert hook.exchange_step == "IKE_AUTH"

    def test_hook_definition_priority_validation(self):
        """Test hook priority validation."""
        # Valid priority
        hook = HookDefinition(
            hook_type=HookType.UNIVERSAL,
            trigger_condition="always",
            callback_function="test",
            priority=0
        )
        assert hook.priority == 0
        
        # Invalid priority (negative)
        with pytest.raises(ValidationError):
            HookDefinition(
                hook_type=HookType.UNIVERSAL,
                trigger_condition="always",
                callback_function="test",
                priority=-1
            )


class TestCallbackConfig:
    """Test suite for CallbackConfig model."""

    def test_callback_config_defaults(self):
        """Test CallbackConfig default values."""
        config = CallbackConfig()
        
        assert config.timeout == 5.0
        assert config.retry_count == 0
        assert config.fail_on_error is False
        assert config.capture_output is True

    def test_callback_config_custom_values(self):
        """Test CallbackConfig with custom values."""
        config = CallbackConfig(
            timeout=10.0,
            retry_count=3,
            fail_on_error=True,
            capture_output=False
        )
        
        assert config.timeout == 10.0
        assert config.retry_count == 3
        assert config.fail_on_error is True
        assert config.capture_output is False

    def test_callback_config_timeout_validation(self):
        """Test timeout validation."""
        # Valid timeout
        config = CallbackConfig(timeout=0.1)
        assert config.timeout == 0.1
        
        # Invalid timeout (zero)
        with pytest.raises(ValidationError):
            CallbackConfig(timeout=0.0)
        
        # Invalid timeout (negative)
        with pytest.raises(ValidationError):
            CallbackConfig(timeout=-1.0)


class TestHookResult:
    """Test suite for HookResult model."""

    def test_hook_result_creation(self):
        """Test HookResult creation."""
        result = HookResult(
            hook_id="test-hook-123",
            success=True,
            execution_time=1.5,
            output="Hook executed successfully",
            metadata={"test": "data"}
        )
        
        assert result.hook_id == "test-hook-123"
        assert result.success is True
        assert result.execution_time == 1.5
        assert result.output == "Hook executed successfully"
        assert result.error_message is None
        assert result.metadata == {"test": "data"}

    def test_hook_result_failure(self):
        """Test HookResult for failed execution."""
        result = HookResult(
            hook_id="test-hook-456",
            success=False,
            execution_time=0.5,
            error_message="Hook execution failed"
        )
        
        assert result.hook_id == "test-hook-456"
        assert result.success is False
        assert result.execution_time == 0.5
        assert result.output is None
        assert result.error_message == "Hook execution failed"
        assert result.metadata == {}


class TestPacketHook:
    """Test suite for PacketHook model."""

    def test_packet_hook_creation(self):
        """Test PacketHook creation."""
        hook = PacketHook(
            trigger_condition="packet == 5",
            callback_function="packet_callback",
            packet_number=5
        )
        
        assert hook.hook_type == HookType.PACKET_NUMBER
        assert hook.packet_number == 5
        assert hook.trigger_condition == "packet == 5"
        assert hook.callback_function == "packet_callback"

    def test_packet_hook_packet_number_validation(self):
        """Test packet number validation."""
        # Valid packet number
        hook = PacketHook(
            trigger_condition="packet == 1",
            callback_function="test",
            packet_number=1
        )
        assert hook.packet_number == 1
        
        # Invalid packet number (zero)
        with pytest.raises(ValidationError):
            PacketHook(
                trigger_condition="packet == 0",
                callback_function="test",
                packet_number=0
            )
        
        # Invalid packet number (negative)
        with pytest.raises(ValidationError):
            PacketHook(
                trigger_condition="packet == -1",
                callback_function="test",
                packet_number=-1
            )

    def test_packet_hook_frozen_type(self):
        """Test that hook_type is frozen."""
        hook = PacketHook(
            trigger_condition="packet == 5",
            callback_function="test",
            packet_number=5
        )
        
        # Should not be able to change hook_type
        with pytest.raises(ValidationError):
            hook.hook_type = HookType.EXCHANGE_STEP


class TestExchangeHook:
    """Test suite for ExchangeHook model."""

    def test_exchange_hook_creation(self):
        """Test ExchangeHook creation."""
        hook = ExchangeHook(
            trigger_condition="exchange == 'IKE_AUTH'",
            callback_function="auth_callback",
            exchange_step="IKE_AUTH"
        )
        
        assert hook.hook_type == HookType.EXCHANGE_STEP
        assert hook.exchange_step == "IKE_AUTH"
        assert hook.trigger_condition == "exchange == 'IKE_AUTH'"
        assert hook.callback_function == "auth_callback"

    def test_exchange_hook_valid_steps(self):
        """Test valid exchange steps."""
        valid_steps = [
            "IKE_SA_INIT", "IKE_AUTH", "CREATE_CHILD_SA",
            "INFORMATIONAL", "ESP", "pre_exchange", "post_exchange"
        ]
        
        for step in valid_steps:
            hook = ExchangeHook(
                trigger_condition=f"exchange == '{step}'",
                callback_function="test",
                exchange_step=step
            )
            assert hook.exchange_step == step

    def test_exchange_hook_invalid_step(self):
        """Test invalid exchange step."""
        with pytest.raises(ValidationError):
            ExchangeHook(
                trigger_condition="exchange == 'INVALID'",
                callback_function="test",
                exchange_step="INVALID_STEP"
            )

    def test_exchange_hook_frozen_type(self):
        """Test that hook_type is frozen."""
        hook = ExchangeHook(
            trigger_condition="exchange == 'IKE_AUTH'",
            callback_function="test",
            exchange_step="IKE_AUTH"
        )
        
        # Should not be able to change hook_type
        with pytest.raises(ValidationError):
            hook.hook_type = HookType.PACKET_NUMBER


class TestUniversalHook:
    """Test suite for UniversalHook model."""

    def test_universal_hook_creation(self):
        """Test UniversalHook creation."""
        hook = UniversalHook(
            trigger_condition="always",
            callback_function="universal_callback",
            custom_condition="custom logic here"
        )
        
        assert hook.hook_type == HookType.UNIVERSAL
        assert hook.trigger_condition == "always"
        assert hook.callback_function == "universal_callback"
        assert hook.custom_condition == "custom logic here"

    def test_universal_hook_frozen_type(self):
        """Test that hook_type is frozen."""
        hook = UniversalHook(
            trigger_condition="always",
            callback_function="test",
            custom_condition="test"
        )
        
        # Should not be able to change hook_type
        with pytest.raises(ValidationError):
            hook.hook_type = HookType.PACKET_NUMBER


class TestHookRegistry:
    """Test suite for HookRegistry model."""

    def test_hook_registry_creation(self):
        """Test HookRegistry creation."""
        registry = HookRegistry()
        
        assert registry.hooks == []
        assert isinstance(registry.global_config, CallbackConfig)

    def test_hook_registry_add_hook(self):
        """Test adding hooks to registry."""
        registry = HookRegistry()
        
        hook1 = PacketHook(
            trigger_condition="packet == 1",
            callback_function="test1",
            packet_number=1
        )
        hook2 = ExchangeHook(
            trigger_condition="exchange == 'IKE_AUTH'",
            callback_function="test2",
            exchange_step="IKE_AUTH"
        )
        
        registry.add_hook(hook1)
        registry.add_hook(hook2)
        
        assert len(registry.hooks) == 2
        assert hook1 in registry.hooks
        assert hook2 in registry.hooks

    def test_hook_registry_remove_hook(self):
        """Test removing hooks from registry."""
        registry = HookRegistry()
        
        hook = PacketHook(
            trigger_condition="packet == 1",
            callback_function="test",
            packet_number=1
        )
        
        registry.add_hook(hook)
        assert len(registry.hooks) == 1
        
        # Remove by ID
        removed = registry.remove_hook(str(hook.id))
        assert removed is True
        assert len(registry.hooks) == 0
        
        # Try to remove non-existent hook
        removed = registry.remove_hook("non-existent-id")
        assert removed is False

    def test_hook_registry_get_hooks_by_type(self):
        """Test getting hooks by type."""
        registry = HookRegistry()
        
        packet_hook = PacketHook(
            trigger_condition="packet == 1",
            callback_function="packet_test",
            packet_number=1
        )
        exchange_hook = ExchangeHook(
            trigger_condition="exchange == 'IKE_AUTH'",
            callback_function="exchange_test",
            exchange_step="IKE_AUTH"
        )
        disabled_hook = PacketHook(
            trigger_condition="packet == 2",
            callback_function="disabled_test",
            packet_number=2,
            enabled=False
        )
        
        registry.add_hook(packet_hook)
        registry.add_hook(exchange_hook)
        registry.add_hook(disabled_hook)
        
        # Get packet hooks (should only return enabled ones)
        packet_hooks = registry.get_hooks_by_type(HookType.PACKET_NUMBER)
        assert len(packet_hooks) == 1
        assert packet_hook in packet_hooks
        assert disabled_hook not in packet_hooks
        
        # Get exchange hooks
        exchange_hooks = registry.get_hooks_by_type(HookType.EXCHANGE_STEP)
        assert len(exchange_hooks) == 1
        assert exchange_hook in exchange_hooks

    def test_hook_registry_get_packet_hooks(self):
        """Test getting hooks for specific packet numbers."""
        registry = HookRegistry()
        
        hook1 = PacketHook(
            trigger_condition="packet == 1",
            callback_function="test1",
            packet_number=1
        )
        hook2 = PacketHook(
            trigger_condition="packet == 2",
            callback_function="test2",
            packet_number=2
        )
        hook3 = PacketHook(
            trigger_condition="packet == 1",
            callback_function="test3",
            packet_number=1,
            enabled=False
        )
        
        registry.add_hook(hook1)
        registry.add_hook(hook2)
        registry.add_hook(hook3)
        
        # Get hooks for packet 1 (should only return enabled ones)
        packet_1_hooks = registry.get_packet_hooks(1)
        assert len(packet_1_hooks) == 1
        assert hook1 in packet_1_hooks
        assert hook3 not in packet_1_hooks
        
        # Get hooks for packet 2
        packet_2_hooks = registry.get_packet_hooks(2)
        assert len(packet_2_hooks) == 1
        assert hook2 in packet_2_hooks
        
        # Get hooks for non-existent packet
        packet_3_hooks = registry.get_packet_hooks(3)
        assert len(packet_3_hooks) == 0

    def test_hook_registry_get_exchange_hooks(self):
        """Test getting hooks for specific exchange steps."""
        registry = HookRegistry()
        
        hook1 = ExchangeHook(
            trigger_condition="exchange == 'IKE_AUTH'",
            callback_function="auth_test",
            exchange_step="IKE_AUTH"
        )
        hook2 = ExchangeHook(
            trigger_condition="exchange == 'CREATE_CHILD_SA'",
            callback_function="child_test",
            exchange_step="CREATE_CHILD_SA"
        )
        hook3 = ExchangeHook(
            trigger_condition="exchange == 'IKE_AUTH'",
            callback_function="disabled_auth_test",
            exchange_step="IKE_AUTH",
            enabled=False
        )
        
        registry.add_hook(hook1)
        registry.add_hook(hook2)
        registry.add_hook(hook3)
        
        # Get hooks for IKE_AUTH (should only return enabled ones)
        auth_hooks = registry.get_exchange_hooks("IKE_AUTH")
        assert len(auth_hooks) == 1
        assert hook1 in auth_hooks
        assert hook3 not in auth_hooks
        
        # Get hooks for CREATE_CHILD_SA
        child_hooks = registry.get_exchange_hooks("CREATE_CHILD_SA")
        assert len(child_hooks) == 1
        assert hook2 in child_hooks
        
        # Get hooks for non-existent exchange
        info_hooks = registry.get_exchange_hooks("INFORMATIONAL")
        assert len(info_hooks) == 0
