"""
Unit tests for test scenario models.

This module provides comprehensive tests for scenario models including:
- ExchangeDefinition validation and configuration
- PacketDefinition direction and payload validation
- TestScenario creation and validation
- YAML file loading and parsing
- Test mode support (initiator/responder)
- Exchange filtering and retrieval
- Scenario validation and error checking
- Sequence number validation
"""

import pytest
import tempfile
import yaml
from pathlib import Path
from pydantic import ValidationError

from ipsecator.models.scenario import (
    ExchangeDefinition,
    PacketDefinition,
    TestScenario,
)
from ipsecator.models.base import ExchangeType, TestMode


class TestExchangeDefinition:
    """Test suite for ExchangeDefinition model."""

    def test_exchange_definition_creation(self):
        """Test basic ExchangeDefinition creation."""
        exchange = ExchangeDefinition(
            exchange_type=ExchangeType.INIT,
            sequence_number=1
        )
        
        assert exchange.exchange_type == ExchangeType.INIT
        assert exchange.sequence_number == 1
        assert exchange.configuration == {}
        assert exchange.expected_outcome == "success"
        assert exchange.timeout == 30.0

    def test_exchange_definition_with_configuration(self):
        """Test ExchangeDefinition with custom configuration."""
        config = {
            "encryption": "AES-GCM-256",
            "dh_group": 19,
            "custom_field": "test_value"
        }
        
        exchange = ExchangeDefinition(
            exchange_type=ExchangeType.AUTH,
            sequence_number=2,
            configuration=config,
            expected_outcome="failure",
            timeout=60.0
        )
        
        assert exchange.exchange_type == ExchangeType.AUTH
        assert exchange.sequence_number == 2
        assert exchange.configuration == config
        assert exchange.expected_outcome == "failure"
        assert exchange.timeout == 60.0

    def test_exchange_definition_sequence_number_validation(self):
        """Test sequence number validation."""
        # Valid sequence number
        exchange = ExchangeDefinition(
            exchange_type=ExchangeType.INIT,
            sequence_number=1
        )
        assert exchange.sequence_number == 1
        
        # Invalid sequence number (zero)
        with pytest.raises(ValidationError):
            ExchangeDefinition(
                exchange_type=ExchangeType.INIT,
                sequence_number=0
            )
        
        # Invalid sequence number (negative)
        with pytest.raises(ValidationError):
            ExchangeDefinition(
                exchange_type=ExchangeType.INIT,
                sequence_number=-1
            )

    def test_exchange_definition_all_exchange_types(self):
        """Test all exchange types."""
        for exchange_type in ExchangeType:
            exchange = ExchangeDefinition(
                exchange_type=exchange_type,
                sequence_number=1
            )
            assert exchange.exchange_type == exchange_type


class TestPacketDefinition:
    """Test suite for PacketDefinition model."""

    def test_packet_definition_creation(self):
        """Test basic PacketDefinition creation."""
        packet = PacketDefinition(
            packet_type="IKE_SA_INIT_REQUEST",
            direction="outbound"
        )
        
        assert packet.packet_type == "IKE_SA_INIT_REQUEST"
        assert packet.direction == "outbound"
        assert packet.payload_data is None
        assert packet.modifications == {}

    def test_packet_definition_with_payload(self):
        """Test PacketDefinition with payload data."""
        payload = b"\x01\x02\x03\x04"
        modifications = {"field1": "value1", "field2": 42}
        
        packet = PacketDefinition(
            packet_type="IKE_AUTH_REQUEST",
            direction="inbound",
            payload_data=payload,
            modifications=modifications
        )
        
        assert packet.packet_type == "IKE_AUTH_REQUEST"
        assert packet.direction == "inbound"
        assert packet.payload_data == payload
        assert packet.modifications == modifications

    def test_packet_definition_direction_validation(self):
        """Test packet direction validation."""
        # Valid directions
        for direction in ["inbound", "outbound"]:
            packet = PacketDefinition(
                packet_type="TEST",
                direction=direction
            )
            assert packet.direction == direction
        
        # Case insensitive
        packet = PacketDefinition(
            packet_type="TEST",
            direction="INBOUND"
        )
        assert packet.direction == "inbound"
        
        packet = PacketDefinition(
            packet_type="TEST",
            direction="OutBound"
        )
        assert packet.direction == "outbound"
        
        # Invalid direction
        with pytest.raises(ValidationError):
            PacketDefinition(
                packet_type="TEST",
                direction="invalid"
            )


class TestTestScenario:
    """Test suite for TestScenario model."""

    def test_test_scenario_creation(self):
        """Test basic TestScenario creation."""
        scenario = TestScenario(
            name="Basic IKE Test",
            description="A basic IKE exchange test"
        )
        
        assert scenario.name == "Basic IKE Test"
        assert scenario.description == "A basic IKE exchange test"
        assert scenario.version == "1.0"
        assert scenario.initiator_mode is True
        assert scenario.responder_mode is True
        assert scenario.exchanges == []
        assert scenario.packets == []
        assert scenario.tags == []
        assert scenario.compliance_level == "standard"

    def test_test_scenario_with_exchanges(self):
        """Test TestScenario with exchanges."""
        exchanges = [
            ExchangeDefinition(
                exchange_type=ExchangeType.INIT,
                sequence_number=1
            ),
            ExchangeDefinition(
                exchange_type=ExchangeType.AUTH,
                sequence_number=2
            )
        ]
        
        scenario = TestScenario(
            name="Multi-Exchange Test",
            description="Test with multiple exchanges",
            exchanges=exchanges
        )
        
        assert len(scenario.exchanges) == 2
        assert scenario.exchanges[0].exchange_type == ExchangeType.INIT
        assert scenario.exchanges[1].exchange_type == ExchangeType.AUTH

    def test_test_scenario_with_packets(self):
        """Test TestScenario with packets."""
        packets = [
            PacketDefinition(
                packet_type="IKE_SA_INIT_REQUEST",
                direction="outbound"
            ),
            PacketDefinition(
                packet_type="IKE_SA_INIT_RESPONSE",
                direction="inbound"
            )
        ]
        
        scenario = TestScenario(
            name="Packet Test",
            description="Test with packets",
            packets=packets
        )
        
        assert len(scenario.packets) == 2
        assert scenario.packets[0].packet_type == "IKE_SA_INIT_REQUEST"
        assert scenario.packets[1].packet_type == "IKE_SA_INIT_RESPONSE"

    def test_test_scenario_mode_configuration(self):
        """Test TestScenario mode configuration."""
        # Initiator only
        scenario = TestScenario(
            name="Initiator Test",
            description="Initiator only test",
            initiator_mode=True,
            responder_mode=False
        )
        
        assert scenario.initiator_mode is True
        assert scenario.responder_mode is False
        
        # Responder only
        scenario = TestScenario(
            name="Responder Test",
            description="Responder only test",
            initiator_mode=False,
            responder_mode=True
        )
        
        assert scenario.initiator_mode is False
        assert scenario.responder_mode is True

    def test_test_scenario_has_initiator_tests(self):
        """Test has_initiator_tests method."""
        scenario = TestScenario(
            name="Test",
            description="Test",
            initiator_mode=True,
            responder_mode=False
        )
        
        assert scenario.has_initiator_tests() is True
        
        scenario.initiator_mode = False
        assert scenario.has_initiator_tests() is False

    def test_test_scenario_has_responder_tests(self):
        """Test has_responder_tests method."""
        scenario = TestScenario(
            name="Test",
            description="Test",
            initiator_mode=False,
            responder_mode=True
        )
        
        assert scenario.has_responder_tests() is True
        
        scenario.responder_mode = False
        assert scenario.has_responder_tests() is False

    def test_test_scenario_get_test_modes(self):
        """Test get_test_modes method."""
        # Both modes
        scenario = TestScenario(
            name="Test",
            description="Test",
            initiator_mode=True,
            responder_mode=True
        )
        
        modes = scenario.get_test_modes()
        assert TestMode.INITIATOR in modes
        assert TestMode.RESPONDER in modes
        assert len(modes) == 2
        
        # Initiator only
        scenario.responder_mode = False
        modes = scenario.get_test_modes()
        assert TestMode.INITIATOR in modes
        assert TestMode.RESPONDER not in modes
        assert len(modes) == 1
        
        # Responder only
        scenario.initiator_mode = False
        scenario.responder_mode = True
        modes = scenario.get_test_modes()
        assert TestMode.INITIATOR not in modes
        assert TestMode.RESPONDER in modes
        assert len(modes) == 1
        
        # Neither mode
        scenario.responder_mode = False
        modes = scenario.get_test_modes()
        assert len(modes) == 0

    def test_test_scenario_get_exchanges_for_mode(self):
        """Test get_exchanges_for_mode method."""
        exchanges = [
            ExchangeDefinition(
                exchange_type=ExchangeType.INIT,
                sequence_number=1
            )
        ]
        
        scenario = TestScenario(
            name="Test",
            description="Test",
            exchanges=exchanges,
            initiator_mode=True,
            responder_mode=False
        )
        
        # Valid mode
        init_exchanges = scenario.get_exchanges_for_mode(TestMode.INITIATOR)
        assert len(init_exchanges) == 1
        assert init_exchanges[0].exchange_type == ExchangeType.INIT
        
        # Invalid mode
        resp_exchanges = scenario.get_exchanges_for_mode(TestMode.RESPONDER)
        assert len(resp_exchanges) == 0

    def test_test_scenario_get_exchange_by_type(self):
        """Test get_exchange_by_type method."""
        exchanges = [
            ExchangeDefinition(
                exchange_type=ExchangeType.INIT,
                sequence_number=1
            ),
            ExchangeDefinition(
                exchange_type=ExchangeType.AUTH,
                sequence_number=2
            )
        ]
        
        scenario = TestScenario(
            name="Test",
            description="Test",
            exchanges=exchanges
        )
        
        # Find existing exchange
        init_exchange = scenario.get_exchange_by_type(ExchangeType.INIT)
        assert init_exchange is not None
        assert init_exchange.exchange_type == ExchangeType.INIT
        
        auth_exchange = scenario.get_exchange_by_type(ExchangeType.AUTH)
        assert auth_exchange is not None
        assert auth_exchange.exchange_type == ExchangeType.AUTH
        
        # Find non-existing exchange
        child_exchange = scenario.get_exchange_by_type(ExchangeType.CREATE_CHILD)
        assert child_exchange is None

    def test_test_scenario_validate_scenario(self):
        """Test validate_scenario method."""
        # Valid scenario
        exchanges = [
            ExchangeDefinition(
                exchange_type=ExchangeType.INIT,
                sequence_number=1
            ),
            ExchangeDefinition(
                exchange_type=ExchangeType.AUTH,
                sequence_number=2
            )
        ]
        
        scenario = TestScenario(
            name="Valid Test",
            description="A valid test scenario",
            exchanges=exchanges,
            initiator_mode=True,
            responder_mode=True
        )
        
        issues = scenario.validate_scenario()
        assert len(issues) == 0
        
        # Scenario with no exchanges
        scenario.exchanges = []
        issues = scenario.validate_scenario()
        assert len(issues) == 1
        assert "at least one exchange" in issues[0]
        
        # Scenario with no supported modes
        scenario.exchanges = exchanges
        scenario.initiator_mode = False
        scenario.responder_mode = False
        issues = scenario.validate_scenario()
        assert len(issues) == 1
        assert "at least one test mode" in issues[0]
        
        # Scenario with duplicate sequence numbers
        exchanges_duplicate = [
            ExchangeDefinition(
                exchange_type=ExchangeType.INIT,
                sequence_number=1
            ),
            ExchangeDefinition(
                exchange_type=ExchangeType.AUTH,
                sequence_number=1  # Duplicate
            )
        ]
        
        scenario.exchanges = exchanges_duplicate
        scenario.initiator_mode = True
        issues = scenario.validate_scenario()
        assert len(issues) == 1
        assert "unique" in issues[0]

    def test_test_scenario_from_file(self):
        """Test loading TestScenario from YAML file."""
        scenario_data = {
            "name": "File Test Scenario",
            "description": "A test scenario loaded from file",
            "version": "2.0",
            "initiator_mode": True,
            "responder_mode": False,
            "exchanges": [
                {
                    "exchange_type": "IKE_SA_INIT",
                    "sequence_number": 1,
                    "configuration": {"test": "value"}
                }
            ],
            "tags": ["test", "file"],
            "compliance_level": "high"
        }
        
        # Create temporary YAML file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(scenario_data, f)
            temp_file = Path(f.name)
        
        try:
            # Load scenario from file
            scenario = TestScenario.from_file(temp_file)
            
            assert scenario.name == "File Test Scenario"
            assert scenario.description == "A test scenario loaded from file"
            assert scenario.version == "2.0"
            assert scenario.initiator_mode is True
            assert scenario.responder_mode is False
            assert len(scenario.exchanges) == 1
            assert scenario.exchanges[0].exchange_type == ExchangeType.INIT
            assert scenario.tags == ["test", "file"]
            assert scenario.compliance_level == "high"
        
        finally:
            # Clean up
            temp_file.unlink()

    def test_test_scenario_from_file_invalid(self):
        """Test loading TestScenario from invalid file."""
        # Non-existent file
        with pytest.raises(ValueError):
            TestScenario.from_file(Path("/nonexistent/file.yaml"))
        
        # Invalid YAML content
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write("invalid: yaml: content: [")
            temp_file = Path(f.name)
        
        try:
            with pytest.raises(ValueError):
                TestScenario.from_file(temp_file)
        finally:
            temp_file.unlink()
