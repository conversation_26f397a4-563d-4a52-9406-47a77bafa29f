"""
Unit tests for configuration utilities.

This module provides comprehensive tests for the IPsecEvaluatorConfigParser
and related configuration management functionality.
"""

import os
import sys
import tempfile
from pathlib import Path
from unittest.mock import patch, mock_open

# Add ipsecator to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent / "ipsecator"))

try:
    import pytest
    from pydantic import ValidationError

    from ipsecator.utils.configuration import (
        IPsecEvaluatorConfigParser,
        ConfigurationError
    )
    from ipsecator.models.config import (
        TestConfiguration,
        GlobalConfig,
        NetworkConfig,
        CryptoConfig,
        PKIConfig
    )
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Some dependencies not available: {e}")
    DEPENDENCIES_AVAILABLE = False

    # Create mock classes for when dependencies are not available
    class MockConfigurationError(Exception):
        pass

    class MockIPsecEvaluatorConfigParser:
        pass

    ConfigurationError = MockConfigurationError
    IPsecEvaluatorConfigParser = MockIPsecEvaluatorConfigParser
from tests.support.config_helpers import (
    write_config_file,
    write_toml_config,
    write_yaml_config,
    write_ini_config,
    temporary_config_files,
    create_basic_toml_config,
    create_basic_yaml_config,
    create_basic_ini_config,
    create_invalid_config,
    create_overlay_config,
    create_cli_override_args,
    create_env_variables,
    create_test_pki_config
)


class TestIPsecEvaluatorConfigParser:
    """Test suite for IPsecEvaluatorConfigParser."""

    def test_init_with_no_config_files(self):
        """Test parser initialization with no configuration files."""
        parser = IPsecEvaluatorConfigParser(validate_config=False)

        assert parser.config_files == []
        assert isinstance(parser.global_config, GlobalConfig)
        assert isinstance(parser.network, NetworkConfig)
        assert isinstance(parser.crypto, CryptoConfig)
        assert parser.pki is None

    def test_init_with_nonexistent_file(self):
        """Test parser initialization with non-existent configuration file."""
        parser = IPsecEvaluatorConfigParser(
            config_files=["/nonexistent/config.toml"],
            validate_config=False
        )

        assert parser.config_files == []

    def test_load_toml_config(self):
        """Test loading TOML configuration file."""
        config_data = create_basic_toml_config()
        config_file = write_toml_config(config_data)

        with temporary_config_files(config_file):
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                validate_config=False
            )

            assert parser.global_config.timeout == 30
            assert parser.global_config.verbose is True
            assert parser.global_config.log_level == "DEBUG"
            assert parser.network.interface == "eth0"
            assert str(parser.network.ipsec_src) == "************"
            assert parser.crypto.encryption_algorithms == ["AES-GCM-256"]

    def test_load_yaml_config(self):
        """Test loading YAML configuration file."""
        config_data = create_basic_yaml_config()
        config_file = write_yaml_config(config_data)

        with temporary_config_files(config_file):
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                validate_config=False
            )

            assert parser.global_config.timeout == 30
            assert parser.global_config.verbose is True
            assert parser.network.interface == "eth0"
            assert parser.crypto.dh_groups == [19]

    def test_load_ini_config_ipsecdr_compatibility(self):
        """Test loading INI configuration file (ipsecdr compatibility)."""
        config_data = create_basic_ini_config()
        config_file = write_ini_config(config_data)

        with temporary_config_files(config_file):
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                validate_config=False
            )

            # Check global config mapping from ARGS section
            assert parser.global_config.timeout == 30
            assert parser.global_config.verbose is True

            # Check network config mapping from IF section
            assert parser.network.interface == "eth0"
            assert str(parser.network.ipsec_src) == "************"
            assert parser.network.port_src == 500

    def test_load_multiple_config_files(self):
        """Test loading multiple configuration files with precedence."""
        base_config = create_basic_toml_config()
        overlay_config = create_overlay_config()

        base_file = write_toml_config(base_config)
        overlay_file = write_toml_config(overlay_config)

        with temporary_config_files(base_file, overlay_file):
            parser = IPsecEvaluatorConfigParser(
                config_files=[base_file, overlay_file],
                validate_config=False
            )

            # Base values should be preserved
            assert parser.global_config.timeout == 30

            # Overlay values should override base values
            assert parser.global_config.verbose is False  # Overridden
            assert parser.global_config.log_level == "WARNING"  # Overridden
            assert parser.network.interface == "eth1"  # Overridden
            assert parser.network.nat_traversal is True  # Overridden

    def test_cli_overrides(self):
        """Test CLI argument overrides."""
        config_data = create_basic_toml_config()
        config_file = write_toml_config(config_data)
        cli_args = create_cli_override_args()

        with temporary_config_files(config_file):
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                cli_args=cli_args,
                validate_config=False
            )

            # CLI overrides should take precedence
            assert parser.global_config.timeout == 60  # Overridden by CLI
            assert parser.global_config.verbose is True  # Overridden by CLI
            assert parser.network.interface == "eth2"  # Overridden by CLI
            assert parser.network.port_src == 4500  # Overridden by CLI

    @patch.dict(os.environ, create_env_variables())
    def test_environment_variable_overrides(self):
        """Test environment variable overrides."""
        config_data = create_basic_toml_config()
        config_file = write_toml_config(config_data)

        with temporary_config_files(config_file):
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                validate_config=False
            )

            # Environment variables should override config file values
            assert parser.global_config.timeout == 45  # From env var
            assert parser.global_config.verbose is False  # From env var
            assert parser.network.interface == "lo"  # From env var
            assert parser.network.nat_traversal is True  # From env var

    def test_unsupported_file_format(self):
        """Test handling of unsupported file formats."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write('{"test": "data"}')
            json_file = f.name

        with temporary_config_files(json_file):
            with pytest.raises(ConfigurationError, match="Unsupported configuration format"):
                IPsecEvaluatorConfigParser(
                    config_files=[json_file],
                    validate_config=True
                )

    def test_invalid_toml_file(self):
        """Test handling of invalid TOML file."""
        invalid_toml = "invalid toml content [unclosed section"
        config_file = write_config_file(invalid_toml, suffix=".toml")

        with temporary_config_files(config_file):
            with pytest.raises(ConfigurationError):
                IPsecEvaluatorConfigParser(
                    config_files=[config_file],
                    validate_config=True
                )

    def test_invalid_yaml_file(self):
        """Test handling of invalid YAML file."""
        invalid_yaml = "invalid: yaml: content: [unclosed"
        config_file = write_config_file(invalid_yaml, suffix=".yaml")

        with temporary_config_files(config_file):
            with pytest.raises(ConfigurationError):
                IPsecEvaluatorConfigParser(
                    config_files=[config_file],
                    validate_config=True
                )

    def test_configuration_validation_success(self):
        """Test successful configuration validation."""
        config_data = create_basic_toml_config()
        config_file = write_toml_config(config_data)

        with temporary_config_files(config_file):
            # Should not raise any exception
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                validate_config=True
            )

            # Should be able to get test configuration
            test_config = parser.get_test_configuration()
            assert isinstance(test_config, TestConfiguration)

    def test_configuration_validation_failure(self):
        """Test configuration validation failure."""
        invalid_config = create_invalid_config()
        config_file = write_toml_config(invalid_config)

        with temporary_config_files(config_file):
            with pytest.raises(ConfigurationError, match="Failed to load.*Failed to set"):
                IPsecEvaluatorConfigParser(
                    config_files=[config_file],
                    validate_config=True
                )

    def test_get_test_configuration(self):
        """Test getting complete test configuration."""
        config_data = create_basic_toml_config()
        config_file = write_toml_config(config_data)

        with temporary_config_files(config_file):
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                validate_config=False
            )

            test_config = parser.get_test_configuration()
            assert isinstance(test_config, TestConfiguration)
            assert isinstance(test_config.global_config, GlobalConfig)
            assert isinstance(test_config.network, NetworkConfig)
            assert isinstance(test_config.crypto, CryptoConfig)
            assert test_config.pki is None

    def test_overlay_config_method(self):
        """Test overlay_config method."""
        base_config = create_basic_toml_config()
        overlay_config = create_overlay_config()

        base_file = write_toml_config(base_config)
        overlay_file = write_toml_config(overlay_config)

        with temporary_config_files(base_file, overlay_file):
            parser = IPsecEvaluatorConfigParser(
                config_files=[base_file],
                validate_config=False
            )

            # Apply overlay
            parser.overlay_config([overlay_file])

            # Check that overlay values were applied
            assert parser.global_config.verbose is False  # From overlay
            assert parser.global_config.log_level == "WARNING"  # From overlay
            assert parser.network.interface == "eth1"  # From overlay

    def test_save_config_toml(self):
        """Test saving configuration to TOML file."""
        config_data = create_basic_toml_config()
        config_file = write_toml_config(config_data)

        with temporary_config_files(config_file):
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                validate_config=False
            )

            # Save to new file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
                output_file = Path(f.name)

            try:
                parser.save_config(output_file, format_type='toml')
                assert output_file.exists()

                # Verify saved content by loading it again
                new_parser = IPsecEvaluatorConfigParser(
                    config_files=[output_file],
                    validate_config=False
                )
                assert new_parser.global_config.timeout == parser.global_config.timeout
                assert new_parser.network.interface == parser.network.interface
            finally:
                if output_file.exists():
                    output_file.unlink()

    def test_save_config_yaml(self):
        """Test saving configuration to YAML file."""
        config_data = create_basic_toml_config()
        config_file = write_toml_config(config_data)

        with temporary_config_files(config_file):
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                validate_config=False
            )

            # Save to new file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                output_file = Path(f.name)

            try:
                parser.save_config(output_file, format_type='yaml')
                assert output_file.exists()

                # Verify saved content by loading it again
                new_parser = IPsecEvaluatorConfigParser(
                    config_files=[output_file],
                    validate_config=False
                )
                assert new_parser.global_config.timeout == parser.global_config.timeout
                assert new_parser.network.interface == parser.network.interface
            finally:
                if output_file.exists():
                    output_file.unlink()

    def test_save_config_unsupported_format(self):
        """Test saving configuration with unsupported format."""
        config_data = create_basic_toml_config()
        config_file = write_toml_config(config_data)

        with temporary_config_files(config_file):
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                validate_config=False
            )

            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                output_file = Path(f.name)

            try:
                with pytest.raises(ConfigurationError, match="Unsupported output format"):
                    parser.save_config(output_file, format_type='json')
            finally:
                if output_file.exists():
                    output_file.unlink()

    def test_get_config_summary(self):
        """Test getting configuration summary."""
        config_data = create_basic_toml_config()
        config_file = write_toml_config(config_data)

        with temporary_config_files(config_file):
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                validate_config=False
            )

            summary = parser.get_config_summary()

            assert isinstance(summary, dict)
            assert 'config_files' in summary
            assert 'global_config' in summary
            assert 'network' in summary
            assert 'crypto' in summary
            assert 'pki' in summary
            assert 'validation_enabled' in summary
            assert 'env_prefix' in summary

            assert summary['validation_enabled'] is False
            assert summary['env_prefix'] == "IPSEC_EVALUATOR"
            assert len(summary['config_files']) == 1

    def test_type_conversion_in_updates(self):
        """Test type conversion when updating configuration objects."""
        # Create config with string values that need conversion
        config_content = """
        [global_config]
        timeout = "45"
        verbose = "true"
        max_concurrent_tests = "3"

        [network]
        port_src = "4500"
        nat_traversal = "false"
        """
        config_file = write_config_file(config_content, suffix=".toml")

        with temporary_config_files(config_file):
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                validate_config=False
            )

            # Check that string values were converted to proper types
            assert parser.global_config.timeout == 45
            assert parser.global_config.verbose is True
            assert parser.global_config.max_concurrent_tests == 3
            assert parser.network.port_src == 4500
            assert parser.network.nat_traversal is False

    def test_parse_list_value(self):
        """Test parsing of list values from strings."""
        parser = IPsecEvaluatorConfigParser(validate_config=False)

        # Test comma-separated values
        result = parser._parse_list_value("19,20,21")
        assert result == [19, 20, 21]

        # Test space-separated values
        result = parser._parse_list_value("AES_GCM_16 AES_CTR CHACHA20_POLY1305")
        assert result == ["AES_GCM_16", "AES_CTR", "CHACHA20_POLY1305"]

        # Test mixed comma and space
        result = parser._parse_list_value("19, 20 21,28")
        assert result == [19, 20, 21, 28]

        # Test single value
        result = parser._parse_list_value("19")
        assert result == [19]

        # Test non-string input
        result = parser._parse_list_value(42)
        assert result == [42]

    def test_nested_env_override(self):
        """Test nested environment variable overrides."""
        config_data = create_basic_toml_config()
        config_file = write_toml_config(config_data)

        env_vars = {
            'IPSEC_EVALUATOR_GLOBAL_TIMEOUT': '120',
            'IPSEC_EVALUATOR_NETWORK_PORT_SRC': '4500',
            'IPSEC_EVALUATOR_CRYPTO_DH_GROUPS': '19,20,21'
        }

        with temporary_config_files(config_file):
            with patch.dict(os.environ, env_vars):
                parser = IPsecEvaluatorConfigParser(
                    config_files=[config_file],
                    validate_config=False
                )

                # Check that nested environment variables were applied
                assert parser.global_config.timeout == 120
                assert parser.network.port_src == 4500

    def test_cli_override_invalid_section(self):
        """Test CLI override with invalid section."""
        config_data = create_basic_toml_config()
        config_file = write_toml_config(config_data)

        cli_args = {
            'invalid_section.some_key': 'some_value',
            'global.timeout': '60'  # Valid override
        }

        with temporary_config_files(config_file):
            # Should not raise exception, just log warning
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                cli_args=cli_args,
                validate_config=False
            )

            # Valid override should still work
            assert parser.global_config.timeout == 60

    def test_cli_override_missing_section(self):
        """Test CLI override without section prefix."""
        config_data = create_basic_toml_config()
        config_file = write_toml_config(config_data)

        cli_args = {
            'timeout': '60',  # Missing section prefix
            'global.verbose': 'true'  # Valid override
        }

        with temporary_config_files(config_file):
            # Should not raise exception, just log warning
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                cli_args=cli_args,
                validate_config=False
            )

            # Valid override should still work
            assert parser.global_config.verbose is True


class TestConfigurationTemplates:
    """Test suite for configuration templates."""

    def test_create_basic_template(self):
        """Test creating basic configuration template."""
        template = IPsecEvaluatorConfigParser.create_template('basic')

        assert isinstance(template, dict)
        assert 'global_config' in template
        assert 'network' in template
        assert 'crypto' in template

        # Check basic template values
        assert template['global_config']['max_concurrent_tests'] == 2
        assert template['global_config']['timeout'] == 300
        assert template['network']['interface'] == 'eth0'
        assert template['crypto']['encryption_algorithms'] == ['AES-GCM-256']

    def test_create_anssi_template(self):
        """Test creating ANSSI compliance template."""
        template = IPsecEvaluatorConfigParser.create_template('anssi')

        assert isinstance(template, dict)
        assert 'pki' in template  # ANSSI template includes PKI

        # Check ANSSI-specific values
        assert template['global_config']['verbose'] is True
        assert template['global_config']['log_level'] == 'DEBUG'
        assert 'AES-GCM-256' in template['crypto']['encryption_algorithms']
        assert 19 in template['crypto']['dh_groups']  # secp256r1
        assert template['pki']['verify_certificates'] is True

    def test_create_performance_template(self):
        """Test creating performance testing template."""
        template = IPsecEvaluatorConfigParser.create_template('performance')

        assert isinstance(template, dict)

        # Check performance-specific values
        assert template['global_config']['max_concurrent_tests'] == 8
        assert template['global_config']['test_timeout'] == 600
        assert template['global_config']['log_level'] == 'WARNING'
        assert len(template['crypto']['ike_encryption']) > 1  # Multiple algorithms

    def test_create_development_template(self):
        """Test creating development template."""
        template = IPsecEvaluatorConfigParser.create_template('development')

        assert isinstance(template, dict)

        # Check development-specific values
        assert template['global_config']['max_concurrent_tests'] == 1
        assert template['global_config']['test_timeout'] == 60
        assert template['network']['initiator_ip'] == '127.0.0.1'
        assert template['network']['ike_port'] == 5000  # Non-privileged port

    def test_create_unknown_template(self):
        """Test creating unknown template type."""
        with pytest.raises(ConfigurationError, match="Unknown template type"):
            IPsecEvaluatorConfigParser.create_template('unknown_template')

    def test_from_template_basic(self):
        """Test creating parser from basic template."""
        parser = IPsecEvaluatorConfigParser.from_template(
            template_type='basic',
            validate_config=False
        )

        assert isinstance(parser, IPsecEvaluatorConfigParser)
        assert parser.global_config.max_concurrent_tests == 2
        assert parser.global_config.timeout == 300
        assert parser.network.interface == 'eth0'

    def test_from_template_with_overrides(self):
        """Test creating parser from template with overrides."""
        overrides = {
            'global_config': {
                'timeout': 600,
                'verbose': True
            },
            'network': {
                'interface': 'eth1'
            }
        }

        parser = IPsecEvaluatorConfigParser.from_template(
            template_type='basic',
            overrides=overrides,
            validate_config=False
        )

        # Check that overrides were applied
        assert parser.global_config.timeout == 600  # Overridden
        assert parser.global_config.verbose is True  # Overridden
        assert parser.network.interface == 'eth1'  # Overridden

        # Check that non-overridden values remain from template
        assert parser.global_config.max_concurrent_tests == 2  # From template

    def test_deep_update_utility(self):
        """Test the _deep_update utility method."""
        base_dict = {
            'level1': {
                'level2': {
                    'key1': 'value1',
                    'key2': 'value2'
                },
                'other_key': 'other_value'
            },
            'top_level': 'top_value'
        }

        update_dict = {
            'level1': {
                'level2': {
                    'key1': 'new_value1',  # Override existing
                    'key3': 'value3'       # Add new
                },
                'new_key': 'new_value'     # Add new at level1
            },
            'new_top': 'new_top_value'     # Add new at top level
        }

        IPsecEvaluatorConfigParser._deep_update(base_dict, update_dict)

        # Check overridden values
        assert base_dict['level1']['level2']['key1'] == 'new_value1'

        # Check preserved values
        assert base_dict['level1']['level2']['key2'] == 'value2'
        assert base_dict['level1']['other_key'] == 'other_value'
        assert base_dict['top_level'] == 'top_value'

        # Check new values
        assert base_dict['level1']['level2']['key3'] == 'value3'
        assert base_dict['level1']['new_key'] == 'new_value'
        assert base_dict['new_top'] == 'new_top_value'


class TestConfigurationErrorHandling:
    """Test suite for configuration error handling."""

    def test_invalid_boolean_conversion(self):
        """Test handling of invalid boolean conversion."""
        config_content = """
        [global_config]
        verbose = "invalid_boolean"
        """
        config_file = write_config_file(config_content, suffix=".toml")

        with temporary_config_files(config_file):
            # Should not raise exception during loading, but log warning
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                validate_config=False
            )

            # The invalid value should be converted to False (default boolean conversion)
            assert parser.global_config.verbose is False

    def test_invalid_integer_conversion(self):
        """Test handling of invalid integer conversion."""
        config_content = """
        [global_config]
        timeout = "not_a_number"
        """
        config_file = write_config_file(config_content, suffix=".toml")

        with temporary_config_files(config_file):
            # Should not raise exception during loading, but log warning
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                validate_config=False
            )

            # The invalid value should keep the default value (30) due to conversion error
            assert parser.global_config.timeout == 30

    def test_missing_config_section(self):
        """Test handling of missing configuration sections."""
        config_content = """
        [global_config]
        timeout = 30
        # Missing network and crypto sections
        """
        config_file = write_config_file(config_content, suffix=".toml")

        with temporary_config_files(config_file):
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                validate_config=False
            )

            # Should use default values for missing sections
            assert parser.global_config.timeout == 30
            assert isinstance(parser.network, NetworkConfig)  # Default instance
            assert isinstance(parser.crypto, CryptoConfig)    # Default instance

    def test_empty_config_file(self):
        """Test handling of empty configuration file."""
        config_file = write_config_file("", suffix=".toml")

        with temporary_config_files(config_file):
            parser = IPsecEvaluatorConfigParser(
                config_files=[config_file],
                validate_config=False
            )

            # Should use all default values
            assert isinstance(parser.global_config, GlobalConfig)
            assert isinstance(parser.network, NetworkConfig)
            assert isinstance(parser.crypto, CryptoConfig)
            assert parser.pki is None

    def test_file_permission_error(self):
        """Test handling of file permission errors."""
        # Create a file and make it unreadable
        config_file = write_config_file("test content", suffix=".toml")

        try:
            os.chmod(config_file, 0o000)  # Remove all permissions

            with pytest.raises(ConfigurationError):
                IPsecEvaluatorConfigParser(
                    config_files=[config_file],
                    validate_config=True
                )
        finally:
            # Restore permissions for cleanup
            os.chmod(config_file, 0o644)
            os.unlink(config_file)
