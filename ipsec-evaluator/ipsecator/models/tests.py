"""
Test results and compliance reporting models.
"""

from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional
from pydantic import Field, field_validator, model_validator

from .base import BaseModel, TimestampedModel, ComplianceLevel


class Scenario(BaseModel):
    exchanges: List[str] = Field(
        default=[],
        description="A list of exchanges in the scenario, such as INIT, AUTH, CREATE_CHILD_SA, etc.",
    )
    overlay_configs: List[Optional[Dict[str, Any]]] = Field(
        default=[],
        description="A list of overlay configurations (as dictionaries) for each exchange.",
    )
    check_functions: List[Optional[str]] = Field(
        default=[],
        description="A list of check functions corresponding to each exchange, which can be None or a function name.",
    )

    @model_validator(mode="before")
    @classmethod
    def ensure_list_lengths(cls, values):
        """Ensure all lists have the same length."""
        if not isinstance(values, dict):
            raise TypeError("Values must be a dictionary.")

        exchanges = values.get("exchanges", [])
        overlay_configs = values.get("overlay_configs", [])
        check_functions = values.get("check_functions", [])

        exchanges_len = len(exchanges)
        overlay_configs_len = len(overlay_configs)
        check_functions_len = len(check_functions)

        if not (exchanges_len == overlay_configs_len == check_functions_len):
            raise ValueError(
                "List lengths for exchanges, overlay_configs, and check_functions must be equal."
            )

        return values



class Outcome(BaseModel):
    test_id: int
    test_name: Optional[str] = None
    result: str = Field(
        "Unknown", description="Test result"
    )  # 'Passed', 'Failed', 'Unknown'
    reason: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

    @field_validator("result", mode="before")
    def validate_result(cls, v):
        if isinstance(v, bool):
            if v:
                v = "Passed"
            else:
                v = "Failed"
        if isinstance(v, str):
            if v not in ["Passed", "Failed", "Unknown"]:
                raise ValueError(f"{v} no in 'Passed', 'Failed', 'Unknown'")
        return v

    def __str__(self):
        return self.pretty_print()

    def pretty_print(self) -> str:
        output = f"Test {self.test_id}"
        if self.test_name:
            output += f" ({self.test_name})"
        output += f": {self.result}"
        if self.reason:
            output += f"\nReason: {self.reason}"
        if self.details:
            output += f"\nDetails: {self.details}"
        return output

    def __add__(self, other):
        if isinstance(other, Outcome):
            # Combine details and update the result accordingly
            combined_details = {}
            if self.details:
                combined_details.update(self.details)
            if other.details:
                combined_details.update(other.details)
            # Determine the combined result
            if self.result == "Passed" and other.result == "Passed":
                combined_result = "Passed"
            else:
                combined_result = "Failed"
            combined_reason = "; ".join(
                filter(None, [self.reason, other.reason])
            )
            return Outcome(
                test_id=self.test_id,
                test_name=self.test_name,
                result=combined_result,
                reason=combined_reason,
                details=combined_details,
            )
        else:
            return NotImplemented


class Summary(BaseModel):
    outcomes: List[Outcome] = []

    def add_outcome(self, outcome: Outcome):
        self.outcomes.append(outcome)

    def pretty_print(self) -> str:
        output = "Test Summary:\n"
        for outcome in self.outcomes:
            output += str(outcome) + "\n"
        return output

    def __str__(self):
        return self.pretty_print()


class ComplianceResult(BaseModel):
    """Result of compliance checking."""

    level: ComplianceLevel
    score: float = 0.0
    issues: List[str] = []
    warnings: List[str] = []
    recommendations: List[str] = []


class TestResult(BaseModel):
    """Individual test result."""

    test_id: str = Field(description="Unique test identifier")
    test_name: str = Field(description="Human-readable test name")
    status: str = Field(description="Test status: passed, failed, skipped")
    duration: float = Field(default=0.0, description="Test duration in seconds")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")
    details: Dict[str, Any] = Field(default_factory=dict, description="Additional test details")

    @field_validator("status")
    @classmethod
    def validate_status(cls, v: str) -> str:
        """Validate test status."""
        valid_statuses = ["passed", "failed", "skipped", "error"]
        if v.lower() not in valid_statuses:
            raise ValueError(f"Status must be one of {valid_statuses}")
        return v.lower()


class ComplianceReport(TimestampedModel):
    """Comprehensive compliance report."""

    generated_at: datetime = Field(default_factory=lambda: datetime.now())
    total_tests: int = 0
    passed_tests: int = 0
    failed_tests: int = 0
    results: List[TestResult] = Field(default_factory=list)

    def save_to_file(self, file_path: Path) -> None:
        """Save compliance report to JSON file."""
        import json

        with open(file_path, "w") as f:
            json.dump(self.model_dump(), f, indent=2, default=str)


class Test(BaseModel):
    name: str = Field(..., description="Name of the test")
    initiator_scenarios: Dict[str, List[Scenario]] = Field(
        default_factory=dict,
        description="Dictionary of initiator scenario names mapped to a list of Scenario objects",
    )
    responder_scenarios: Dict[str, List[Scenario]] = Field(
        default_factory=dict,
        description="Dictionary of responder scenario names mapped to a list of Scenario objects",
    )


class TestPoolEntry(BaseModel):
    test_id: int = Field(..., description="Unique identifier for the test.")
    test_name: str = Field(..., description="Name of the test.")
    mode: str = Field(
        ...,
        description="Execution mode for the test ('initiator' or 'responder').",
    )
    lock_status: int = Field(
        0,
        description="Status of the test execution: 0=not started, 1=in progress, etc.",
    )
    outcome: Optional[Outcome] = Field(
        None, description="The outcome of the test execution."
    )
    scenario_name: str = Field(..., description="Name of the scenario.")
    scenario: Scenario = Field(..., description="Scenario for the test")
    scenario_count: int = Field(
        ..., description="Number of scenarios under this test."
    )
    packet_pipe: Optional[Any] = Field(
        None,
        exclude=True,
        description="Queue for packets, excluded from serialization.",
    )
    tester: Optional[Any] = Field(
        None,
        exclude=True,
        description="Tester instance, excluded from serialization.",
    )
    checker: Optional[Any] = Field(
        None,
        exclude=True,
        description="Checker instance, excluded from serialization.",
    )
