"""
Base models and common data structures.
"""

from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, Optional
from uuid import UUID, uuid4

from pydantic import BaseModel as PydanticBaseModel
from pydantic import Field, ConfigDict, field_serializer


class BaseModel(PydanticBaseModel):
    """Base model with common configuration."""

    model_config = ConfigDict(
        extra="forbid",
        validate_assignment=True,
        use_enum_values=True,
        frozen=False,
    )


class ConfigModel(BaseModel):
    """Base model for configuration objects."""

    model_config = ConfigDict(
        extra="allow",  # Allow extra fields for configuration flexibility
        validate_assignment=True,
    )


class ExchangeType(str, Enum):
    """IPsec exchange types."""

    INIT = "IKE_SA_INIT"
    AUTH = "IKE_AUTH"
    CREATE_CHILD = "CREATE_CHILD_SA"
    INFORMATIONAL = "INFORMATIONAL"
    ESP = "ESP"


class TestMode(str, Enum):
    """Test execution modes."""

    INITIATOR = "initiator"
    RESPONDER = "responder"
    BOTH = "both"


class TestStatus(str, Enum):
    """Test execution status."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ComplianceLevel(str, Enum):
    """ANSSI compliance levels."""

    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    PARTIAL = "partial"
    UNKNOWN = "unknown"


class HookType(str, Enum):
    """Types of hooks available."""

    PACKET_NUMBER = "packet_number"
    EXCHANGE_STEP = "exchange_step"
    UNIVERSAL = "universal"
    PRE_EXCHANGE = "pre_exchange"
    POST_EXCHANGE = "post_exchange"


class TimestampedModel(BaseModel):
    """Model with automatic timestamp tracking."""

    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: Optional[datetime] = None

    def update_timestamp(self) -> None:
        """Update the updated_at timestamp."""
        self.updated_at = datetime.now(timezone.utc)

    @field_serializer('created_at', 'updated_at')
    def serialize_datetime(self, dt: Optional[datetime]) -> Optional[str]:
        """Serialize datetime to ISO format."""
        return dt.isoformat() if dt else None


class IdentifiedModel(BaseModel):
    """Model with unique identifier."""

    id: UUID = Field(default_factory=uuid4)
    name: Optional[str] = None
    description: Optional[str] = None


class MetadataModel(BaseModel):
    """Model with metadata support."""

    metadata: Dict[str, Any] = Field(default_factory=dict)

    def add_metadata(self, key: str, value: Any) -> None:
        """Add metadata entry."""
        self.metadata[key] = value

    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Get metadata entry."""
        return self.metadata.get(key, default)
