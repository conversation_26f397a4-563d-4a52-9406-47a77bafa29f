"""
Test execution engine for IPsec Evaluator.

This module contains the core test execution components:
- Orchestrator: Manages test execution and infrastructure
- Tester: Executes individual test scenarios
- Checker: Validates compliance and analyzes results
- Hook system: Provides callback capabilities
"""

from .orchestrator import Orchestrator
from .tester import Tester
from .checker import Checker
from .hooks import HookManager

# Backward compatibility aliases
Orchestrator = Orchestrator
Tester = Tester
Checker = Checker

__all__ = [
    "Orchestrator",
    "Tester",
    "Checker",
    "HookManager",
    "Orchestrator",  # Backward compatibility
    "Tester",  # Backward compatibility
    "Checker",  # Backward compatibility
]
