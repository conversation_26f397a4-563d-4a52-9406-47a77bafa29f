"""
Scenario validation for IPsec Evaluator.

This module provides comprehensive validation for test scenarios to ensure
they are properly formed, consistent, and compatible with the IPsec protocol
requirements and ANSSI compliance standards.
"""

from typing import List, Dict, Any, Optional, Set, Tuple
from dataclasses import dataclass

from models.tests import Test, Scenario
from models.base import ExchangeType, TestMode
from utils.logging import get_logger

logger = get_logger(__name__)


class ValidationError(Exception):
    """Exception raised when scenario validation fails."""
    pass


@dataclass
class ValidationResult:
    """Result of scenario validation."""

    is_valid: bool
    errors: List[str]
    warnings: List[str]
    info: List[str]

    def add_error(self, message: str) -> None:
        """Add an error message."""
        self.errors.append(message)
        self.is_valid = False

    def add_warning(self, message: str) -> None:
        """Add a warning message."""
        self.warnings.append(message)

    def add_info(self, message: str) -> None:
        """Add an info message."""
        self.info.append(message)

    def has_issues(self) -> bool:
        """Check if there are any errors or warnings."""
        return bool(self.errors or self.warnings)


class ScenarioValidator:
    """
    Comprehensive validator for test scenarios.

    Validates scenarios for:
    - Protocol compliance
    - Exchange sequence validity
    - Configuration consistency
    - ANSSI compliance requirements
    - Hook system compatibility
    """

    def __init__(self):
        """Initialize the scenario validator."""
        self.valid_exchanges = {
            ExchangeType.IKE_SA_INIT,
            ExchangeType.IKE_AUTH,
            ExchangeType.CREATE_CHILD_SA,
            ExchangeType.INFORMATIONAL,
        }

        self.valid_exchange_sequences = [
            [ExchangeType.IKE_SA_INIT, ExchangeType.IKE_AUTH],
            [ExchangeType.IKE_SA_INIT, ExchangeType.IKE_AUTH, ExchangeType.CREATE_CHILD_SA],
            [ExchangeType.IKE_SA_INIT, ExchangeType.IKE_AUTH, ExchangeType.CREATE_CHILD_SA, ExchangeType.INFORMATIONAL],
        ]

        logger.debug("Initialized ScenarioValidator")

    def validate_test(self, test: Test) -> ValidationResult:
        """
        Validate a complete test.

        Args:
            test: Test object to validate

        Returns:
            ValidationResult with validation details
        """
        result = ValidationResult(is_valid=True, errors=[], warnings=[], info=[])

        logger.debug(f"Validating test '{test.name}'")

        # Basic test validation
        self._validate_test_metadata(test, result)

        # Validate initiator scenarios
        if test.initiator_scenarios:
            result.add_info(f"Validating {len(test.initiator_scenarios)} initiator scenario groups")
            for scenario_name, scenarios in test.initiator_scenarios.items():
                self._validate_scenario_group(scenario_name, scenarios, TestMode.INITIATOR, result)
        else:
            result.add_warning("Test has no initiator scenarios")

        # Validate responder scenarios
        if test.responder_scenarios:
            result.add_info(f"Validating {len(test.responder_scenarios)} responder scenario groups")
            for scenario_name, scenarios in test.responder_scenarios.items():
                self._validate_scenario_group(scenario_name, scenarios, TestMode.RESPONDER, result)
        else:
            result.add_warning("Test has no responder scenarios")

        # Check that test has at least one mode
        if not test.initiator_scenarios and not test.responder_scenarios:
            result.add_error("Test must have at least one scenario (initiator or responder)")

        logger.debug(f"Test validation completed: {len(result.errors)} errors, {len(result.warnings)} warnings")
        return result

    def _validate_test_metadata(self, test: Test, result: ValidationResult) -> None:
        """Validate test metadata."""
        if not test.name:
            result.add_error("Test name is required")
        elif not test.name.strip():
            result.add_error("Test name cannot be empty")

        if not test.description:
            result.add_warning("Test description is missing")

        if not test.version:
            result.add_warning("Test version is missing")

    def _validate_scenario_group(
        self,
        scenario_name: str,
        scenarios: List[Scenario],
        mode: TestMode,
        result: ValidationResult
    ) -> None:
        """Validate a group of scenarios."""
        if not scenarios:
            result.add_error(f"Scenario group '{scenario_name}' is empty")
            return

        result.add_info(f"Validating scenario group '{scenario_name}' with {len(scenarios)} scenarios")

        for i, scenario in enumerate(scenarios):
            scenario_id = f"{scenario_name}[{i}]"
            self._validate_scenario(scenario_id, scenario, mode, result)

    def _validate_scenario(
        self,
        scenario_id: str,
        scenario: Scenario,
        mode: TestMode,
        result: ValidationResult
    ) -> None:
        """Validate an individual scenario."""
        logger.debug(f"Validating scenario {scenario_id}")

        # Validate exchanges
        self._validate_exchanges(scenario_id, scenario.exchanges, result)

        # Validate list lengths match
        self._validate_list_lengths(scenario_id, scenario, result)

        # Validate overlay configurations
        self._validate_overlay_configs(scenario_id, scenario.overlay_configs, result)

        # Validate check functions
        self._validate_check_functions(scenario_id, scenario.check_functions, result)

        # Validate exchange sequence
        self._validate_exchange_sequence(scenario_id, scenario.exchanges, mode, result)

    def _validate_exchanges(self, scenario_id: str, exchanges: List[str], result: ValidationResult) -> None:
        """Validate exchange list."""
        if not exchanges:
            result.add_error(f"Scenario {scenario_id}: exchanges list is empty")
            return

        for i, exchange in enumerate(exchanges):
            if not exchange:
                result.add_error(f"Scenario {scenario_id}: exchange {i} is empty")
                continue

            try:
                exchange_type = ExchangeType(exchange)
                if exchange_type not in self.valid_exchanges:
                    result.add_error(f"Scenario {scenario_id}: invalid exchange type '{exchange}'")
            except ValueError:
                result.add_error(f"Scenario {scenario_id}: unknown exchange type '{exchange}'")

    def _validate_list_lengths(self, scenario_id: str, scenario: Scenario, result: ValidationResult) -> None:
        """Validate that all lists have the same length."""
        exchanges_len = len(scenario.exchanges)
        overlay_configs_len = len(scenario.overlay_configs)
        check_functions_len = len(scenario.check_functions)

        if not (exchanges_len == overlay_configs_len == check_functions_len):
            result.add_error(
                f"Scenario {scenario_id}: list lengths must be equal "
                f"(exchanges: {exchanges_len}, overlay_configs: {overlay_configs_len}, "
                f"check_functions: {check_functions_len})"
            )

    def _validate_overlay_configs(
        self,
        scenario_id: str,
        overlay_configs: List[Optional[Dict[str, Any]]],
        result: ValidationResult
    ) -> None:
        """Validate overlay configurations."""
        for i, config in enumerate(overlay_configs):
            if config is None:
                continue

            if not isinstance(config, dict):
                result.add_error(f"Scenario {scenario_id}: overlay_config {i} must be a dictionary")
                continue

            # Validate configuration structure
            self._validate_config_structure(f"{scenario_id}[{i}]", config, result)

    def _validate_config_structure(
        self,
        config_id: str,
        config: Dict[str, Any],
        result: ValidationResult
    ) -> None:
        """Validate configuration structure."""
        # Check for known configuration sections
        known_sections = {
            "crypto", "auth", "network", "ts", "identity", "nat",
            "ipsec", "esp", "hooks", "compliance"
        }

        for section in config.keys():
            if section not in known_sections:
                result.add_warning(f"Config {config_id}: unknown section '{section}'")

        # Validate crypto section if present
        if "crypto" in config:
            self._validate_crypto_config(config_id, config["crypto"], result)

    def _validate_crypto_config(
        self,
        config_id: str,
        crypto_config: Any,
        result: ValidationResult
    ) -> None:
        """Validate crypto configuration."""
        if not isinstance(crypto_config, dict):
            result.add_error(f"Config {config_id}: crypto section must be a dictionary")
            return

        # Check for required crypto fields
        crypto_fields = {
            "encryption_algorithms", "integrity_algorithms",
            "dh_groups", "prf_algorithms"
        }

        for field in crypto_fields:
            if field in crypto_config:
                value = crypto_config[field]
                if not isinstance(value, list):
                    result.add_error(f"Config {config_id}: crypto.{field} must be a list")
                elif not value:
                    result.add_warning(f"Config {config_id}: crypto.{field} is empty")

    def _validate_check_functions(
        self,
        scenario_id: str,
        check_functions: List[Optional[str]],
        result: ValidationResult
    ) -> None:
        """Validate check functions."""
        for i, check_function in enumerate(check_functions):
            if check_function is None:
                continue

            if not isinstance(check_function, str):
                result.add_error(f"Scenario {scenario_id}: check_function {i} must be a string")
                continue

            if not check_function.strip():
                result.add_warning(f"Scenario {scenario_id}: check_function {i} is empty")

    def _validate_exchange_sequence(
        self,
        scenario_id: str,
        exchanges: List[str],
        mode: TestMode,
        result: ValidationResult
    ) -> None:
        """Validate exchange sequence for protocol compliance."""
        if not exchanges:
            return

        try:
            exchange_types = [ExchangeType(ex) for ex in exchanges]
        except ValueError as e:
            # Already handled in _validate_exchanges
            return

        # Check for valid IKE sequence
        if ExchangeType.IKE_SA_INIT in exchange_types:
            init_index = exchange_types.index(ExchangeType.IKE_SA_INIT)

            # IKE_SA_INIT should be first
            if init_index != 0:
                result.add_warning(f"Scenario {scenario_id}: IKE_SA_INIT should typically be first exchange")

            # IKE_AUTH should follow IKE_SA_INIT
            if ExchangeType.IKE_AUTH in exchange_types:
                auth_index = exchange_types.index(ExchangeType.IKE_AUTH)
                if auth_index != init_index + 1:
                    result.add_warning(f"Scenario {scenario_id}: IKE_AUTH should immediately follow IKE_SA_INIT")

        # Check for CREATE_CHILD_SA without IKE establishment
        if ExchangeType.CREATE_CHILD_SA in exchange_types:
            if (ExchangeType.IKE_SA_INIT not in exchange_types or
                ExchangeType.IKE_AUTH not in exchange_types):
                result.add_error(f"Scenario {scenario_id}: CREATE_CHILD_SA requires IKE SA establishment")

        # Check for INFORMATIONAL without IKE establishment
        if ExchangeType.INFORMATIONAL in exchange_types:
            if (ExchangeType.IKE_SA_INIT not in exchange_types or
                ExchangeType.IKE_AUTH not in exchange_types):
                result.add_warning(f"Scenario {scenario_id}: INFORMATIONAL typically requires IKE SA establishment")

    def validate_scenario(self, scenario: Scenario, mode: TestMode) -> ValidationResult:
        """
        Validate a single scenario.

        Args:
            scenario: Scenario to validate
            mode: Test mode for the scenario

        Returns:
            ValidationResult with validation details
        """
        result = ValidationResult(is_valid=True, errors=[], warnings=[], info=[])

        self._validate_scenario("standalone", scenario, mode, result)

        return result

    def validate_anssi_compliance(self, test: Test) -> ValidationResult:
        """
        Validate ANSSI compliance requirements.

        Args:
            test: Test to validate for ANSSI compliance

        Returns:
            ValidationResult with compliance validation details
        """
        result = ValidationResult(is_valid=True, errors=[], warnings=[], info=[])

        logger.debug(f"Validating ANSSI compliance for test '{test.name}'")

        # Check for ANSSI-required algorithms
        self._validate_anssi_algorithms(test, result)

        # Check for proper authentication methods
        self._validate_anssi_authentication(test, result)

        # Check for key size requirements
        self._validate_anssi_key_sizes(test, result)

        return result

    def _validate_anssi_algorithms(self, test: Test, result: ValidationResult) -> None:
        """Validate ANSSI algorithm requirements."""
        # ANSSI-approved algorithms
        approved_encryption = {"AES-GCM-256", "AES-CTR", "ChaCha20-Poly1305"}
        approved_integrity = {"HMAC-SHA2-256", "HMAC-SHA2-384", "HMAC-SHA2-512"}
        approved_dh_groups = {19, 20, 21}  # ECP-256, ECP-384, ECP-521

        # This is a simplified check - in a full implementation,
        # you would examine all overlay configurations
        result.add_info("ANSSI algorithm validation would check all scenario configurations")

    def _validate_anssi_authentication(self, test: Test, result: ValidationResult) -> None:
        """Validate ANSSI authentication requirements."""
        # ANSSI requires certificate-based authentication
        result.add_info("ANSSI authentication validation would check for certificate-based auth")

    def _validate_anssi_key_sizes(self, test: Test, result: ValidationResult) -> None:
        """Validate ANSSI key size requirements."""
        # ANSSI requires minimum key sizes
        result.add_info("ANSSI key size validation would check minimum key lengths")
