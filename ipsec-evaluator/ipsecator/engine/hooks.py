"""
Hook system for IPsec Evaluator.

This module provides a comprehensive hook management system that allows
for extensible callback registration and execution throughout the IPsec
testing process.
"""

import asyncio
from typing import Dict, List, Any, Callable, Optional
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from ..models.hooks import HookResult, HookRegistry
from ..models.base import HookType
from ..utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class HookContext:
    """Context information passed to hooks."""

    hook_type: HookType
    timestamp: datetime = field(default_factory=datetime.now)
    exchange_type: Optional[str] = None
    packet_number: Optional[int] = None
    role: Optional[str] = None
    message_id: Optional[int] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    hook_results: Dict[str, Any] = field(default_factory=dict)


class HookPriority(Enum):
    """Hook execution priorities."""

    CRITICAL = 0
    HIGH = 10
    NORMAL = 50
    LOW = 100


@dataclass
class HookRegistration:
    """Internal hook registration data."""

    hook_type: HookType
    callback: Callable[[HookContext], Any]
    priority: HookPriority
    name: str
    description: Optional[str] = None
    conditions: Dict[str, Any] = field(default_factory=dict)
    execution_count: int = 0
    total_execution_time: float = 0.0
    last_execution: Optional[datetime] = None

    def should_execute(self, context: HookContext) -> bool:
        """Check if this hook should execute for the given context."""
        # Check basic conditions
        for key, expected_value in self.conditions.items():
            context_value = getattr(context, key, None)
            if context_value != expected_value:
                return False
        return True

    def update_execution_stats(self, execution_time: float) -> None:
        """Update execution statistics."""
        self.execution_count += 1
        self.total_execution_time += execution_time
        self.last_execution = datetime.now()


class HookManager:
    """
    Comprehensive hook management system.

    Provides registration, execution, and management of hooks throughout
    the IPsec testing process with support for priorities, conditions,
    and comprehensive statistics.
    """

    def __init__(self):
        """Initialize the hook manager."""
        self.hooks: Dict[HookType, List[HookRegistration]] = {
            hook_type: [] for hook_type in HookType
        }
        self.execution_stats = {
            "total_executions": 0,
            "total_execution_time": 0.0,
            "hook_failures": 0,
        }
        self.registry = HookRegistry()

        logger.debug("Initialized HookManager")

    def register_hook(
        self,
        hook_type: HookType,
        callback: Callable[[HookContext], Any],
        priority: HookPriority = HookPriority.NORMAL,
        name: Optional[str] = None,
        description: Optional[str] = None,
        **conditions,
    ) -> str:
        """
        Register a hook callback.

        Args:
            hook_type: Type of hook to register
            callback: Callback function to execute
            priority: Execution priority
            name: Optional name for the hook
            description: Optional description
            **conditions: Additional conditions (packet_number, exchange_type, etc.)

        Returns:
            Hook registration ID
        """
        if name is None:
            name = f"{hook_type.value}_{len(self.hooks[hook_type])}"

        registration = HookRegistration(
            hook_type=hook_type,
            callback=callback,
            priority=priority,
            name=name,
            description=description,
            conditions=conditions,
        )

        # Insert in priority order
        self.hooks[hook_type].append(registration)
        self.hooks[hook_type].sort(key=lambda x: x.priority.value)

        logger.info(f"Registered hook '{name}' for {hook_type.value} with priority {priority.value}")
        return name

    def unregister_hook(self, hook_type: HookType, name: str) -> bool:
        """
        Unregister a hook by name.

        Args:
            hook_type: Type of hook
            name: Name of the hook to unregister

        Returns:
            True if hook was found and removed
        """
        for i, hook in enumerate(self.hooks[hook_type]):
            if hook.name == name:
                del self.hooks[hook_type][i]
                logger.info(f"Unregistered hook '{name}' from {hook_type.value}")
                return True

        logger.warning(f"Hook '{name}' not found in {hook_type.value}")
        return False

    async def execute_hooks(
        self, hook_type: HookType, context: HookContext, timeout: float = 5.0
    ) -> Dict[str, Any]:
        """
        Execute all registered hooks of a specific type.

        Args:
            hook_type: Type of hooks to execute
            context: Context information for the hooks
            timeout: Maximum execution time per hook

        Returns:
            Dictionary of hook results
        """
        results = {}

        # Get hooks to execute
        hooks_to_execute = []

        # Add specific hooks
        for hook in self.hooks[hook_type]:
            if hook.should_execute(context):
                hooks_to_execute.append(hook)

        # Add universal hooks for packet-related events
        if hook_type in [
            HookType.PACKET_NUMBER,
            HookType.EXCHANGE_STEP,
        ]:
            for hook in self.hooks[HookType.UNIVERSAL]:
                if hook.should_execute(context):
                    hooks_to_execute.append(hook)

        # Execute hooks
        for hook in hooks_to_execute:
            try:
                start_time = asyncio.get_event_loop().time()

                # Execute hook with timeout
                if asyncio.iscoroutinefunction(hook.callback):
                    result = await asyncio.wait_for(
                        hook.callback(context), timeout=timeout
                    )
                else:
                    result = hook.callback(context)

                execution_time = asyncio.get_event_loop().time() - start_time

                # Update statistics
                hook.update_execution_stats(execution_time)
                self.execution_stats["total_executions"] += 1
                self.execution_stats["total_execution_time"] += execution_time

                # Store result
                results[hook.name] = result
                context.hook_results[hook.name] = result

                logger.debug(f"Executed hook '{hook.name}' in {execution_time:.3f}s")

            except asyncio.TimeoutError:
                logger.error(f"Hook '{hook.name}' timed out after {timeout}s")
                self.execution_stats["hook_failures"] += 1
                results[hook.name] = {"error": "timeout"}

            except Exception as e:
                logger.error(f"Hook '{hook.name}' failed: {e}")
                self.execution_stats["hook_failures"] += 1
                results[hook.name] = {"error": str(e)}

        return results

    def get_hook_statistics(self) -> Dict[str, Any]:
        """Get comprehensive hook execution statistics."""
        hook_stats = {}

        for hook_type, hooks in self.hooks.items():
            hook_stats[hook_type.value] = {
                "total_hooks": len(hooks),
                "hooks": [
                    {
                        "name": hook.name,
                        "execution_count": hook.execution_count,
                        "total_execution_time": hook.total_execution_time,
                        "average_execution_time": (
                            hook.total_execution_time / hook.execution_count
                            if hook.execution_count > 0 else 0
                        ),
                        "last_execution": hook.last_execution.isoformat() if hook.last_execution else None,
                    }
                    for hook in hooks
                ],
            }

        return {
            "global_stats": self.execution_stats,
            "hook_types": hook_stats,
        }

    def list_hooks(self, hook_type: Optional[HookType] = None) -> Dict[str, List[str]]:
        """List all registered hooks."""
        if hook_type:
            return {
                hook_type.value: [hook.name for hook in self.hooks[hook_type]]
            }

        return {
            hook_type.value: [hook.name for hook in hooks]
            for hook_type, hooks in self.hooks.items()
        }

    def clear_hooks(self, hook_type: Optional[HookType] = None) -> None:
        """Clear hooks of a specific type or all hooks."""
        if hook_type:
            self.hooks[hook_type].clear()
            logger.info(f"Cleared all hooks for {hook_type.value}")
        else:
            for hooks in self.hooks.values():
                hooks.clear()
            logger.info("Cleared all hooks")

    async def get_results(self) -> List[HookResult]:
        """Get all hook execution results."""
        results = []

        for hook_type, hooks in self.hooks.items():
            for hook in hooks:
                if hook.execution_count > 0:
                    result = HookResult(
                        hook_name=hook.name,
                        hook_type=hook_type,
                        execution_count=hook.execution_count,
                        total_execution_time=hook.total_execution_time,
                        last_execution=hook.last_execution,
                        success=True,  # Would need more detailed tracking
                    )
                    results.append(result)

        return results
