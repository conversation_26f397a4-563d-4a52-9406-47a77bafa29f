"""
IKEv2 protocol constants and enumerations.

This module contains all the constants, identifiers, and enumerations
used in the IKEv2 protocol as defined in RFC 7296.
"""

from enum import IntEnum


class ExchangeType(IntEnum):
    """IKEv2 Exchange Types (RFC 7296 Section 3.1)."""

    IKE_SA_INIT = 34
    IKE_AUTH = 35
    CREATE_CHILD_SA = 36
    INFORMATIONAL = 37


class PayloadType(IntEnum):
    """IKEv2 Payload Types (RFC 7296 Section 3.2)."""

    NONE = 0
    SA = 33
    KE = 34
    IDi = 35
    IDr = 36
    CERT = 37
    CERTREQ = 38
    AUTH = 39
    Ni = 40
    Nr = 40  # Same as Ni
    N = 41  # Notify
    D = 42  # Delete
    V = 43  # Vendor ID
    TSi = 44
    TSr = 45
    SK = 46  # Encrypted
    CP = 47  # Configuration
    EAP = 48


class TransformType(IntEnum):
    """Transform Types (RFC 7296 Section 3.3.2)."""

    ENCR = 1  # Encryption Algorithm
    PRF = 2  # Pseudo-random Function
    INTEG = 3  # Integrity Algorithm
    DH = 4  # Diffie-Hellman Group
    ESN = 5  # Extended Sequence Numbers


class EncryptionAlgorithm(IntEnum):
    """Encryption Algorithm Transform IDs."""

    ENCR_DES_IV64 = 1
    ENCR_DES = 2
    ENCR_3DES = 3
    ENCR_RC5 = 4
    ENCR_IDEA = 5
    ENCR_CAST = 6
    ENCR_BLOWFISH = 7
    ENCR_3IDEA = 8
    ENCR_DES_IV32 = 9
    ENCR_NULL = 11
    ENCR_AES_CBC = 12
    ENCR_AES_CTR = 13
    ENCR_AES_CCM_8 = 14
    ENCR_AES_CCM_12 = 15
    ENCR_AES_CCM_16 = 16
    ENCR_AES_GCM_8 = 18
    ENCR_AES_GCM_12 = 19
    ENCR_AES_GCM_16 = 20
    ENCR_NULL_AUTH_AES_GMAC = 21
    ENCR_CHACHA20_POLY1305 = 28


class IntegrityAlgorithm(IntEnum):
    """Integrity Algorithm Transform IDs."""

    AUTH_NONE = 0
    AUTH_HMAC_MD5_96 = 1
    AUTH_HMAC_SHA1_96 = 2
    AUTH_DES_MAC = 3
    AUTH_KPDK_MD5 = 4
    AUTH_AES_XCBC_96 = 5
    AUTH_HMAC_MD5_128 = 6
    AUTH_HMAC_SHA1_160 = 7
    AUTH_AES_CMAC_96 = 8
    AUTH_AES_128_GMAC = 9
    AUTH_AES_192_GMAC = 10
    AUTH_AES_256_GMAC = 11
    AUTH_HMAC_SHA2_256_128 = 12
    AUTH_HMAC_SHA2_384_192 = 13
    AUTH_HMAC_SHA2_512_256 = 14


class PRFAlgorithm(IntEnum):
    """PRF Algorithm Transform IDs."""

    PRF_HMAC_MD5 = 1
    PRF_HMAC_SHA1 = 2
    PRF_HMAC_TIGER = 3
    PRF_AES128_XCBC = 4
    PRF_HMAC_SHA2_256 = 5
    PRF_HMAC_SHA2_384 = 6
    PRF_HMAC_SHA2_512 = 7


class DHGroup(IntEnum):
    """Diffie-Hellman Group Transform IDs."""

    NONE = 0
    MODP_768 = 1
    MODP_1024 = 2
    MODP_1536 = 5
    MODP_2048 = 14
    MODP_3072 = 15
    MODP_4096 = 16
    MODP_6144 = 17
    MODP_8192 = 18
    ECP_256 = 19
    ECP_384 = 20
    ECP_521 = 21
    MODP_1024_160 = 22
    MODP_2048_224 = 23
    MODP_2048_256 = 24
    ECP_192 = 25
    ECP_224 = 26
    CURVE25519 = 31


class ESNTransform(IntEnum):
    """Extended Sequence Numbers Transform IDs."""

    NO_ESN = 0
    ESN = 1


class AuthMethod(IntEnum):
    """Authentication Method identifiers."""

    RSA_SIG = 1
    SHARED_KEY = 2
    DSS_SIG = 3
    ECDSA_256 = 9
    ECDSA_384 = 10
    ECDSA_521 = 11
    GENERIC_SECURE_PASSWORD = 12
    NULL_AUTH = 13
    DIGITAL_SIGNATURE = 14


class NotifyMessageType(IntEnum):
    """Notify Message Types (RFC 7296 Section 3.10.1)."""

    # Error types (1-16383)
    UNSUPPORTED_CRITICAL_PAYLOAD = 1
    INVALID_IKE_SPI = 4
    INVALID_MAJOR_VERSION = 5
    INVALID_SYNTAX = 7
    INVALID_MESSAGE_ID = 9
    INVALID_SPI = 11
    NO_PROPOSAL_CHOSEN = 14
    INVALID_KE_PAYLOAD = 17
    AUTHENTICATION_FAILED = 24
    SINGLE_PAIR_REQUIRED = 34
    NO_ADDITIONAL_SAS = 35
    INTERNAL_ADDRESS_FAILURE = 36
    FAILED_CP_REQUIRED = 37
    TS_UNACCEPTABLE = 38
    INVALID_SELECTORS = 39
    TEMPORARY_FAILURE = 43
    CHILD_SA_NOT_FOUND = 44

    # Status types (16384-65535)
    INITIAL_CONTACT = 16384
    SET_WINDOW_SIZE = 16385
    ADDITIONAL_TS_POSSIBLE = 16386
    IPCOMP_SUPPORTED = 16387
    NAT_DETECTION_SOURCE_IP = 16388
    NAT_DETECTION_DESTINATION_IP = 16389
    COOKIE = 16390
    USE_TRANSPORT_MODE = 16391
    HTTP_CERT_LOOKUP_SUPPORTED = 16392
    REKEY_SA = 16393
    ESP_TFC_PADDING_NOT_SUPPORTED = 16394
    NON_FIRST_FRAGMENTS_ALSO = 16395


class ProtocolID(IntEnum):
    """Protocol IDs for SA payloads."""

    IKE = 1
    AH = 2
    ESP = 3


class TSType(IntEnum):
    """Traffic Selector Types."""

    TS_IPV4_ADDR_RANGE = 7
    TS_IPV6_ADDR_RANGE = 8


# ANSSI-specific algorithm mappings for compliance checking
ANSSI_APPROVED_ALGORITHMS = {
    "encryption": [
        EncryptionAlgorithm.ENCR_AES_GCM_16,
        EncryptionAlgorithm.ENCR_AES_CBC,
        EncryptionAlgorithm.ENCR_CHACHA20_POLY1305,
    ],
    "integrity": [
        IntegrityAlgorithm.AUTH_HMAC_SHA2_256_128,
        IntegrityAlgorithm.AUTH_HMAC_SHA2_384_192,
        IntegrityAlgorithm.AUTH_HMAC_SHA2_512_256,
    ],
    "prf": [
        PRFAlgorithm.PRF_HMAC_SHA2_256,
        PRFAlgorithm.PRF_HMAC_SHA2_384,
        PRFAlgorithm.PRF_HMAC_SHA2_512,
    ],
    "dh_groups": [
        DHGroup.MODP_2048,
        DHGroup.MODP_3072,
        DHGroup.MODP_4096,
        DHGroup.ECP_256,
        DHGroup.ECP_384,
        DHGroup.ECP_521,
        DHGroup.CURVE25519,
    ],
}

# Key size mappings
ALGORITHM_KEY_SIZES = {
    EncryptionAlgorithm.ENCR_AES_CBC: [128, 192, 256],
    EncryptionAlgorithm.ENCR_AES_GCM_16: [128, 192, 256],
    EncryptionAlgorithm.ENCR_CHACHA20_POLY1305: [256],
}

# Default key sizes for algorithms
DEFAULT_KEY_SIZES = {
    EncryptionAlgorithm.ENCR_AES_CBC: 256,
    EncryptionAlgorithm.ENCR_AES_GCM_16: 256,
    EncryptionAlgorithm.ENCR_CHACHA20_POLY1305: 256,
}
