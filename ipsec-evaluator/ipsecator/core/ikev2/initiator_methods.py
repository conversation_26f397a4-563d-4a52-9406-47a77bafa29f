"""
IKEv2 initiator methods implementation.

This module provides methods for initiating IKEv2 exchanges as the initiator,
complementing the exchange handlers for a complete IKEv2 implementation.
"""

import secrets
from typing import Any
from scapy.contrib.ikev2 import IKEv2

from ...utils.logging import get_logger
from ..crypto.algorithms import EncryptionAlgorithm, PRFAlgorithm
from .state import IKEv2State, ExchangeType

logger = get_logger(__name__)


class IKEv2InitiatorMethods:
    """
    Mixin class providing IKEv2 initiator methods.

    This class contains methods for initiating various IKEv2 exchanges
    when acting as the initiator.
    """

    async def initiate_ike_sa_init(self, **kwargs) -> IKEv2:
        """
        Initiate an IKE_SA_INIT exchange as initiator.

        Args:
            **kwargs: Additional parameters for the exchange

        Returns:
            The IKE_SA_INIT packet to send
        """
        logger.info("Initiating IKE_SA_INIT exchange")

        # Generate SPIs and nonce
        spi_i = secrets.token_bytes(8)
        nonce_i = secrets.token_bytes(32)

        # Create security association
        from .protocol import IKEv2SecurityAssociation
        self.ike_sa = IKEv2SecurityAssociation(
            spi_i=spi_i,
            spi_r=b'\x00' * 8,  # Will be set from response
            encryption_algorithm=EncryptionAlgorithm.AES_GCM_256,
            integrity_algorithm=None,  # AEAD mode
            prf_algorithm=PRFAlgorithm.HMAC_SHA2_256,
            dh_group=19,  # secp256r1
        )

        # Start exchange
        exchange = await self.start_exchange(ExchangeType.IKE_SA_INIT, **kwargs)

        # Build packet
        packet = self.payload_builder.build_ike_sa_init_request(
            spi_i=spi_i,
            message_id=exchange.message_id,
            nonce=nonce_i,
            dh_group=19,
        )

        exchange.initiator_packet = packet
        self.state_machine.transition_to(IKEv2State.SA_INIT_SENT)

        logger.debug("IKE_SA_INIT packet generated")
        return packet

    async def initiate_ike_auth(self, **kwargs) -> IKEv2:
        """
        Initiate an IKE_AUTH exchange as initiator.

        Args:
            **kwargs: Additional parameters for the exchange

        Returns:
            The IKE_AUTH packet to send
        """
        logger.info("Initiating IKE_AUTH exchange")

        if not self.ike_sa:
            raise ValueError("No IKE SA established for AUTH exchange")

        # Start exchange
        exchange = await self.start_exchange(ExchangeType.IKE_AUTH, **kwargs)

        # Build packet
        packet = self.payload_builder.build_ike_auth_request(
            spi_i=self.ike_sa.spi_i,
            spi_r=self.ike_sa.spi_r,
            message_id=exchange.message_id,
            auth_method=9,  # ECDSA_SECP256R1_SHA256
        )

        exchange.initiator_packet = packet
        self.state_machine.transition_to(IKEv2State.AUTH_SENT)

        logger.debug("IKE_AUTH packet generated")
        return packet

    async def initiate_create_child_sa(self, **kwargs) -> IKEv2:
        """
        Initiate a CREATE_CHILD_SA exchange as initiator.

        Args:
            **kwargs: Additional parameters for the exchange

        Returns:
            The CREATE_CHILD_SA packet to send
        """
        logger.info("Initiating CREATE_CHILD_SA exchange")

        if not self.ike_sa:
            raise ValueError("No IKE SA established for CREATE_CHILD_SA exchange")

        # Start exchange
        exchange = await self.start_exchange(ExchangeType.CREATE_CHILD_SA, **kwargs)

        # Generate child SPI
        child_spi_i = secrets.token_bytes(4)

        # Build packet
        packet = self.payload_builder.build_create_child_sa_request(
            spi_i=self.ike_sa.spi_i,
            spi_r=self.ike_sa.spi_r,
            message_id=exchange.message_id,
            child_spi=child_spi_i,
        )

        exchange.initiator_packet = packet
        self.state_machine.transition_to(IKEv2State.CHILD_SA_CREATING)

        logger.debug("CREATE_CHILD_SA packet generated")
        return packet

    async def initiate_informational(self, **kwargs) -> IKEv2:
        """
        Initiate an INFORMATIONAL exchange as initiator.

        Args:
            **kwargs: Additional parameters for the exchange

        Returns:
            The INFORMATIONAL packet to send
        """
        logger.info("Initiating INFORMATIONAL exchange")

        if not self.ike_sa:
            raise ValueError("No IKE SA established for INFORMATIONAL exchange")

        # Start exchange
        exchange = await self.start_exchange(ExchangeType.INFORMATIONAL, **kwargs)

        # Build packet
        packet = self.payload_builder.build_informational_request(
            spi_i=self.ike_sa.spi_i,
            spi_r=self.ike_sa.spi_r,
            message_id=exchange.message_id,
        )

        exchange.initiator_packet = packet

        logger.debug("INFORMATIONAL packet generated")
        return packet

    async def initiate_full_ike_negotiation(self, **kwargs) -> bool:
        """
        Perform a complete IKE negotiation (INIT + AUTH).

        Args:
            **kwargs: Additional parameters for the negotiation

        Returns:
            True if negotiation was successful, False otherwise
        """
        logger.info("Starting full IKE negotiation")

        try:
            # Step 1: IKE_SA_INIT
            init_packet = await self.initiate_ike_sa_init(**kwargs)
            logger.debug("IKE_SA_INIT initiated")

            # Note: In a real implementation, you would send the packet
            # and wait for a response before proceeding to AUTH

            # Step 2: IKE_AUTH (would be called after receiving INIT response)
            # auth_packet = await self.initiate_ike_auth(**kwargs)
            # logger.debug("IKE_AUTH initiated")

            logger.info("Full IKE negotiation initiated successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initiate IKE negotiation: {e}")
            return False

    async def initiate_child_sa_negotiation(self, **kwargs) -> bool:
        """
        Initiate a Child SA negotiation.

        Args:
            **kwargs: Additional parameters for the negotiation

        Returns:
            True if initiation was successful, False otherwise
        """
        logger.info("Starting Child SA negotiation")

        try:
            if self.state_machine.current_state != IKEv2State.ESTABLISHED:
                raise ValueError("IKE SA must be established before creating Child SA")

            # Initiate CREATE_CHILD_SA
            child_packet = await self.initiate_create_child_sa(**kwargs)
            logger.debug("CREATE_CHILD_SA initiated")

            logger.info("Child SA negotiation initiated successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initiate Child SA negotiation: {e}")
            return False

    async def send_informational_message(self, notify_type: str = None, **kwargs) -> bool:
        """
        Send an informational message.

        Args:
            notify_type: Optional notify type to include
            **kwargs: Additional parameters

        Returns:
            True if message was sent successfully, False otherwise
        """
        logger.info(f"Sending informational message (notify: {notify_type})")

        try:
            if self.state_machine.current_state != IKEv2State.ESTABLISHED:
                raise ValueError("IKE SA must be established before sending informational")

            # Add notify type to kwargs if provided
            if notify_type:
                kwargs['notify_type'] = notify_type

            # Initiate INFORMATIONAL
            info_packet = await self.initiate_informational(**kwargs)
            logger.debug("INFORMATIONAL message initiated")

            logger.info("Informational message sent successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to send informational message: {e}")
            return False

    def can_initiate_exchange(self, exchange_type: ExchangeType) -> bool:
        """
        Check if an exchange type can be initiated in the current state.

        Args:
            exchange_type: The type of exchange to check

        Returns:
            True if the exchange can be initiated, False otherwise
        """
        current_state = self.state_machine.current_state

        if exchange_type == ExchangeType.IKE_SA_INIT:
            return current_state == IKEv2State.INITIAL
        elif exchange_type == ExchangeType.IKE_AUTH:
            return current_state == IKEv2State.SA_INIT_COMPLETED
        elif exchange_type == ExchangeType.CREATE_CHILD_SA:
            return current_state in [IKEv2State.ESTABLISHED, IKEv2State.CHILD_SA_ESTABLISHED]
        elif exchange_type == ExchangeType.INFORMATIONAL:
            return current_state in [IKEv2State.ESTABLISHED, IKEv2State.CHILD_SA_ESTABLISHED]

        return False

    def get_next_message_id(self) -> int:
        """
        Get the next message ID for an exchange.

        Returns:
            The next message ID to use
        """
        if self.ike_sa:
            return self.ike_sa.increment_message_id()
        return 0
