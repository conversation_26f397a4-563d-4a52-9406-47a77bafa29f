"""
Cryptographic operations for IPsec.

This module provides cryptographic primitives and algorithms used in IPsec:
- Encryption/decryption algorithms
- Integrity protection algorithms
- Key derivation functions
- Digital signatures
- Di<PERSON><PERSON>-<PERSON><PERSON> key exchange
"""

from .engine import CryptoEngine
from .algorithms import *
from .keys import KeyManager
from .dh import DHManager
from .ecsdsa import (
    Point,
    Signature,
    EllipticCurve,
    NotOnCurve,
    brainpoolP256r1,
    secp256r1,
    inverse_mod,
    is_on_curve,
    point_neg,
    point_add,
    scalar_mult,
    make_keypair,
    sign_message,
    verify_signature,
)

__all__ = [
    "CryptoEngine",
    "KeyManager",
    "DHManager",
    "EncryptionAlgorithm",
    "IntegrityAlgorithm",
    "PRFAlgorithm",
    # ECSDSA exports
    "Point",
    "Signature",
    "EllipticCurve",
    "NotOnCurve",
    "brainpoolP256r1",
    "secp256r1",
    "inverse_mod",
    "is_on_curve",
    "point_neg",
    "point_add",
    "scalar_mult",
    "make_keypair",
    "sign_message",
    "verify_signature",
]
