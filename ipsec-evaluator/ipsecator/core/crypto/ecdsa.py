"""
Elliptic Curve Digital Signature Algorithm (ECDSA) implementation.

This module provides a complete implementation of ECDSA with support for:
- secp256r1 (P-256) curve
- secp384r1 (P-384) curve
- secp521r1 (P-521) curve
- brainpoolP256r1 curve for ANSSI compliance
- Key pair generation
- Message signing and verification
- Integration with cryptography library

The implementation follows the ECDSA specification (FIPS 186-4) and provides
cryptographically secure operations for digital signatures.
"""

import hashlib
import secrets
from typing import <PERSON><PERSON>, Optional

from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.backends import default_backend
from cryptography.exceptions import InvalidSignature

from ...utils.logging import get_logger

logger = get_logger(__name__)


class ECDSAManager:
    """
    ECDSA signature manager with comprehensive curve support.

    This class provides support for:
    - Complete secp curve family (secp256r1, secp384r1, secp521r1)
    - Brainpool curves for ANSSI compliance (brainpoolP256r1, brainpoolP384r1, brainpoolP512r1)
    - Standard ECDSA operations (key generation, signing, verification)
    - Multiple hash algorithms (SHA-256, SHA-384, SHA-512)
    """

    def __init__(self):
        self._backend = default_backend()

        # Supported curves mapping
        self._supported_curves = {
            "secp256r1": ec.SECP256R1(),
            "secp384r1": ec.SECP384R1(),
            "secp521r1": ec.SECP521R1(),
            "brainpoolP256r1": ec.BrainpoolP256R1(),
            "brainpoolP384r1": ec.BrainpoolP384R1(),
            "brainpoolP512r1": ec.BrainpoolP512R1(),
        }

        # Supported hash algorithms
        self._supported_hashes = {
            "SHA256": hashes.SHA256(),
            "SHA384": hashes.SHA384(),
            "SHA512": hashes.SHA512(),
        }

        logger.info(f"ECDSA Manager initialized with {len(self._supported_curves)} curves")

    def get_supported_curves(self) -> list[str]:
        """Get list of supported curve names."""
        return list(self._supported_curves.keys())

    def get_supported_hashes(self) -> list[str]:
        """Get list of supported hash algorithm names."""
        return list(self._supported_hashes.keys())

    def generate_keypair(self, curve_name: str) -> Tuple[bytes, bytes]:
        """
        Generate ECDSA key pair for specified curve.

        Args:
            curve_name: Name of the elliptic curve

        Returns:
            Tuple of (private_key_bytes, public_key_bytes)
        """
        if curve_name not in self._supported_curves:
            raise ValueError(f"Unsupported curve: {curve_name}")

        curve = self._supported_curves[curve_name]

        logger.debug(f"Generating ECDSA keypair for {curve_name}")

        # Generate private key
        private_key = ec.generate_private_key(curve, self._backend)
        public_key = private_key.public_key()

        # Serialize keys
        private_bytes = private_key.private_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption(),
        )

        public_bytes = public_key.public_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PublicFormat.SubjectPublicKeyInfo,
        )

        logger.debug(f"Generated ECDSA keypair: private={len(private_bytes)} bytes, public={len(public_bytes)} bytes")

        return private_bytes, public_bytes

    def sign_message(
        self,
        private_key_bytes: bytes,
        message: bytes,
        hash_algorithm: str = "SHA256"
    ) -> bytes:
        """
        Sign a message using ECDSA.

        Args:
            private_key_bytes: Private key in DER format
            message: Message to sign
            hash_algorithm: Hash algorithm to use

        Returns:
            Signature bytes in DER format
        """
        if hash_algorithm not in self._supported_hashes:
            raise ValueError(f"Unsupported hash algorithm: {hash_algorithm}")

        # Load private key
        private_key = serialization.load_der_private_key(
            private_key_bytes, password=None, backend=self._backend
        )

        # Get hash algorithm
        hash_alg = self._supported_hashes[hash_algorithm]

        # Sign message
        signature = private_key.sign(message, ec.ECDSA(hash_alg))

        logger.debug(f"Signed message with ECDSA using {hash_algorithm}: {len(signature)} bytes")

        return signature

    def verify_signature(
        self,
        public_key_bytes: bytes,
        message: bytes,
        signature: bytes,
        hash_algorithm: str = "SHA256"
    ) -> bool:
        """
        Verify an ECDSA signature.

        Args:
            public_key_bytes: Public key in DER format
            message: Original message
            signature: Signature to verify
            hash_algorithm: Hash algorithm used for signing

        Returns:
            True if signature is valid, False otherwise
        """
        if hash_algorithm not in self._supported_hashes:
            raise ValueError(f"Unsupported hash algorithm: {hash_algorithm}")

        try:
            # Load public key
            public_key = serialization.load_der_public_key(
                public_key_bytes, backend=self._backend
            )

            # Get hash algorithm
            hash_alg = self._supported_hashes[hash_algorithm]

            # Verify signature
            public_key.verify(signature, message, ec.ECDSA(hash_alg))

            logger.debug(f"ECDSA signature verification successful")
            return True

        except InvalidSignature:
            logger.debug(f"ECDSA signature verification failed")
            return False
        except Exception as e:
            logger.error(f"ECDSA signature verification error: {e}")
            return False

    def get_curve_info(self, curve_name: str) -> dict:
        """
        Get information about a curve.

        Args:
            curve_name: Name of the curve

        Returns:
            Dictionary with curve information
        """
        if curve_name not in self._supported_curves:
            raise ValueError(f"Unsupported curve: {curve_name}")

        curve = self._supported_curves[curve_name]

        info = {
            "name": curve_name,
            "curve_class": curve.__class__.__name__,
            "key_size": curve.key_size,
        }

        # Add ANSSI approval status
        anssi_approved_curves = {
            "secp256r1", "secp384r1", "secp521r1",
            "brainpoolP256r1", "brainpoolP384r1", "brainpoolP512r1"
        }
        info["anssi_approved"] = curve_name in anssi_approved_curves

        return info

    def get_public_key_from_private(self, private_key_bytes: bytes) -> bytes:
        """
        Extract public key from private key.

        Args:
            private_key_bytes: Private key in DER format

        Returns:
            Public key bytes in DER format
        """
        # Load private key
        private_key = serialization.load_der_private_key(
            private_key_bytes, password=None, backend=self._backend
        )

        # Get public key
        public_key = private_key.public_key()

        # Serialize public key
        public_bytes = public_key.public_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PublicFormat.SubjectPublicKeyInfo,
        )

        return public_bytes

    def sign_hash(
        self,
        private_key_bytes: bytes,
        message_hash: bytes,
        hash_algorithm: str = "SHA256"
    ) -> bytes:
        """
        Sign a pre-computed hash using ECDSA.

        Note: This method computes the signature by re-hashing the provided hash.
        For true pre-hashed signing, use the message signing method.

        Args:
            private_key_bytes: Private key in DER format
            message_hash: Pre-computed hash to sign
            hash_algorithm: Hash algorithm that was used

        Returns:
            Signature bytes in DER format
        """
        # For simplicity, we'll treat the hash as a message and sign it
        return self.sign_message(private_key_bytes, message_hash, hash_algorithm)

    def verify_hash_signature(
        self,
        public_key_bytes: bytes,
        message_hash: bytes,
        signature: bytes,
        hash_algorithm: str = "SHA256"
    ) -> bool:
        """
        Verify an ECDSA signature of a pre-computed hash.

        Note: This method treats the hash as a message and verifies it.
        For true pre-hashed verification, use the message verification method.

        Args:
            public_key_bytes: Public key in DER format
            message_hash: Pre-computed hash
            signature: Signature to verify
            hash_algorithm: Hash algorithm that was used

        Returns:
            True if signature is valid, False otherwise
        """
        # For simplicity, we'll treat the hash as a message and verify it
        return self.verify_signature(public_key_bytes, message_hash, signature, hash_algorithm)
