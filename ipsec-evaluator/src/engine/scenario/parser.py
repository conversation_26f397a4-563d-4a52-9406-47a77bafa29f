"""
Modern Scenario Parser for IPsec Evaluator

This module provides a comprehensive scenario parser that builds upon the ipsecdr
design but modernizes it with Pydantic models, enhanced validation, and support
for the new hook system and configuration architecture.
"""

import yaml
import copy
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Tuple
from itertools import product

from models.scenario import TestScenario, ExchangeDefinition, PacketDefinition
from models.config import IPsecConfig, CryptoConfig, AuthConfig, TSConfig, IDConfig, NATConfig
from models.tests import Scenario, Test, TestResult
from models.base import ExchangeType, TestMode
from utils.logging import get_logger

logger = get_logger(__name__)


class ScenarioParsingError(Exception):
    """Exception raised when scenario parsing fails."""
    pass


class TestScenarioParser:
    """
    Modern test scenario parser for ipsec-evaluator.

    This parser handles both 'strict' and 'target' modes from ipsecdr,
    but with enhanced validation and modern Pydantic models.
    """

    def __init__(self, input_source: Union[str, Path, Dict[str, Any]]):
        """
        Initialize the TestScenarioParser.

        Args:
            input_source: Path to YAML file or configuration dictionary
        """
        logger.debug(f"Initializing TestScenarioParser with input: {input_source}")

        self.default_sequence = [
            "IKE_SA_INIT",
            "IKE_AUTH",
            "CREATE_CHILD_SA",
            "INFORMATIONAL",
        ]

        # Load configuration
        if isinstance(input_source, (str, Path)):
            self.config = self._load_from_file(Path(input_source))
        elif isinstance(input_source, dict):
            self.config = input_source
        else:
            raise ValueError("input_source must be a file path or dictionary")

        # Validate basic structure
        self._validate_config_structure()

    def _load_from_file(self, file_path: Path) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.debug(f"Loaded configuration from {file_path}")
            return config
        except Exception as e:
            raise ScenarioParsingError(f"Failed to load scenario file {file_path}: {e}")

    def _validate_config_structure(self) -> None:
        """Validate basic configuration structure."""
        if not isinstance(self.config, dict):
            raise ScenarioParsingError("Configuration must be a dictionary")

        required_fields = ["mode"]
        for field in required_fields:
            if field not in self.config:
                raise ScenarioParsingError(f"Missing required field: {field}")

    def get_mode(self) -> str:
        """Get the scenario mode."""
        return self.config.get("mode", "strict")

    def get_scenario(self) -> List[Scenario]:
        """
        Generate scenario data based on the mode.

        Returns:
            List of Scenario objects
        """
        mode = self.get_mode()
        logger.debug(f"Parsing scenario in {mode} mode")

        if mode == "strict":
            return self._parse_strict_mode()
        elif mode == "target":
            return self._parse_target_mode()
        else:
            raise ScenarioParsingError(f"Unknown mode '{mode}' in test configuration")

    def _parse_strict_mode(self) -> List[Scenario]:
        """
        Parse strict mode scenarios.

        In strict mode, packets are defined with specific numbers and exchanges.
        """
        packets = self.config.get("packets", [])
        exchanges = []
        overlay_configs = []
        check_functions = []

        previous_number = 0

        for packet in packets:
            if not isinstance(packet, dict):
                raise ScenarioParsingError("Each packet must be a dictionary")

            number = packet.get("number")
            if number is None:
                raise ScenarioParsingError("Packet number is required in strict mode")

            if number <= previous_number:
                logger.warning(f"Packet number {number} is not in ascending order")

            # Get exchange type (default based on sequence)
            exchange = packet.get("exchange")
            if exchange is None:
                # Use default sequence
                sequence_index = (number - 1) % len(self.default_sequence)
                exchange = self.default_sequence[sequence_index]

            # Get overlay configuration
            overlay_config = packet.get("overlay_config")

            # Get check function
            check_function = packet.get("check_function")

            exchanges.append(exchange)
            overlay_configs.append(overlay_config)
            check_functions.append(check_function)
            previous_number = number

        scenario_dict = {
            "exchanges": exchanges,
            "overlay_configs": overlay_configs,
            "check_functions": check_functions,
        }

        return [Scenario(**scenario_dict)]

    def _parse_target_mode(self) -> List[Scenario]:
        """
        Parse target mode scenarios.

        In target mode, targets define specific exchanges with actions and fields.
        """
        targets = self.config.get("targets", [])

        if not targets:
            raise ScenarioParsingError("Target mode requires 'targets' field")

        scenarios = []

        for target in targets:
            if not isinstance(target, dict):
                raise ScenarioParsingError("Each target must be a dictionary")

            exchange = target.get("exchange")
            if not exchange:
                raise ScenarioParsingError("Target must specify an exchange")

            action = target.get("action", "comply")
            check_function = target.get("check_function")
            fields = target.get("fields", [])
            overlay_config = target.get("overlay_config")

            if action == "enum" and fields:
                # Generate multiple scenarios for enumeration
                enum_scenarios = self._generate_enum_scenarios(
                    exchange, fields, check_function, overlay_config
                )
                scenarios.extend(enum_scenarios)
            else:
                # Single scenario for comply action
                scenario_dict = {
                    "exchanges": [exchange],
                    "overlay_configs": [overlay_config],
                    "check_functions": [check_function],
                }
                scenarios.append(Scenario(**scenario_dict))

        return scenarios

    def _generate_enum_scenarios(
        self,
        exchange: str,
        fields: List[Dict[str, Any]],
        check_function: Optional[str],
        base_overlay_config: Optional[Dict[str, Any]]
    ) -> List[Scenario]:
        """
        Generate multiple scenarios for field enumeration.

        Args:
            exchange: Exchange type
            fields: List of field specifications
            check_function: Check function name
            base_overlay_config: Base overlay configuration

        Returns:
            List of generated scenarios
        """
        scenarios = []

        # Parse field specifications
        field_combinations = self._parse_field_combinations(fields)

        for combination in field_combinations:
            # Create overlay config for this combination
            overlay_config = copy.deepcopy(base_overlay_config) if base_overlay_config else {}

            # Apply field values to overlay config
            for field_path, value in combination.items():
                self._set_nested_value(overlay_config, field_path, value)

            scenario_dict = {
                "exchanges": [exchange],
                "overlay_configs": [overlay_config],
                "check_functions": [check_function],
            }
            scenarios.append(Scenario(**scenario_dict))

        return scenarios

    def _parse_field_combinations(self, fields: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Parse field specifications and generate all combinations.

        Args:
            fields: List of field specifications

        Returns:
            List of field value combinations
        """
        field_specs = {}

        for field_spec in fields:
            if not isinstance(field_spec, dict):
                continue

            for field_path, values in field_spec.items():
                if values == "any":
                    # Get possible values for this field
                    possible_values = self._get_possible_values_for_field(field_path)
                    field_specs[field_path] = possible_values
                elif isinstance(values, list):
                    field_specs[field_path] = values
                else:
                    field_specs[field_path] = [values]

        # Generate all combinations
        if not field_specs:
            return [{}]

        field_names = list(field_specs.keys())
        value_lists = [field_specs[name] for name in field_names]

        combinations = []
        for value_combination in product(*value_lists):
            combination = dict(zip(field_names, value_combination))
            combinations.append(combination)

        return combinations

    def _get_possible_values_for_field(self, field_path: str) -> List[Any]:
        """
        Get possible values for a field path.

        Args:
            field_path: Dot-separated field path (e.g., "ipsec.crypto.encryption_algorithms")

        Returns:
            List of possible values
        """
        # This is a simplified version - in a full implementation,
        # you would query the actual crypto constants and capabilities

        field_mappings = {
            "ipsec.crypto.encryption_algorithms": [
                "AES-GCM-256", "AES-CTR", "AES-CBC", "ChaCha20-Poly1305"
            ],
            "ipsec.crypto.integrity_algorithms": [
                "HMAC-SHA2-256", "HMAC-SHA2-384", "HMAC-SHA2-512"
            ],
            "ipsec.crypto.dh_groups": [14, 15, 16, 17, 18, 19, 20, 21],
            "ipsec.crypto.prf_algorithms": [
                "HMAC-SHA2-256", "HMAC-SHA2-384", "HMAC-SHA2-512"
            ],
            "ipsec.auth.auth_method": [
                "ECDSA_SECP256R1_SHA256", "ECDSA_SECP384R1_SHA384", "RSA_PSS_SHA256"
            ],
        }

        return field_mappings.get(field_path, ["default_value"])

    def _set_nested_value(self, config: Dict[str, Any], field_path: str, value: Any) -> None:
        """
        Set a nested value in configuration dictionary.

        Args:
            config: Configuration dictionary
            field_path: Dot-separated field path
            value: Value to set
        """
        parts = field_path.split(".")
        current = config

        # Navigate to the parent of the target field
        for part in parts[:-1]:
            if part not in current:
                current[part] = {}
            current = current[part]

        # Set the final value
        current[parts[-1]] = value


class TestParser:
    """
    Parser for complete test files containing multiple scenarios.

    This parser handles the top-level test structure with initiator
    and responder scenarios.
    """

    def __init__(self, filepath: Union[str, Path]):
        """
        Initialize the TestParser.

        Args:
            filepath: Path to the test YAML file
        """
        self.filepath = Path(filepath)
        logger.debug(f"Initializing TestParser for file: {self.filepath}")

        self.config = self._parse_file()
        self.name = self.config.get("name", "Unnamed Test")
        self.description = self.config.get("description", "")
        self.version = self.config.get("version", "1.0")

        # Parse test scenarios
        self.test_scenarios = self.config.get("test_scenarios", {})
        self.initiator_scenarios: Dict[str, List[Scenario]] = {}
        self.responder_scenarios: Dict[str, List[Scenario]] = {}

        self._parse_scenarios()

    def _parse_file(self) -> Dict[str, Any]:
        """Parse the test file."""
        try:
            with open(self.filepath, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.debug(f"Loaded test configuration from {self.filepath}")
            return config
        except Exception as e:
            raise ScenarioParsingError(f"Failed to load test file {self.filepath}: {e}")

    def _parse_scenarios(self) -> None:
        """Parse test scenarios for both initiator and responder modes."""
        logger.debug("Parsing test scenarios from configuration")

        if not isinstance(self.test_scenarios, dict):
            raise ScenarioParsingError("'test_scenarios' must be a dictionary")

        # Parse initiator scenarios
        initiator_scenarios = self.test_scenarios.get("initiator", [])
        if initiator_scenarios:
            self._parse_role_scenarios(initiator_scenarios, "initiator")

        # Parse responder scenarios
        responder_scenarios = self.test_scenarios.get("responder", [])
        if responder_scenarios:
            self._parse_role_scenarios(responder_scenarios, "responder")

    def _parse_role_scenarios(self, scenarios: List[Dict[str, Any]], role: str) -> None:
        """
        Parse scenarios for a specific role.

        Args:
            scenarios: List of scenario definitions
            role: Role name ("initiator" or "responder")
        """
        if not isinstance(scenarios, list):
            raise ScenarioParsingError(f"'{role}' scenarios must be a list")

        for scenario_item in scenarios:
            if not isinstance(scenario_item, dict):
                raise ScenarioParsingError(f"Each {role} scenario must be a dictionary")

            for scenario_name, scenario_def in scenario_item.items():
                logger.debug(f"Parsing scenario '{scenario_name}' for role '{role}'")

                try:
                    # Parse the scenario definition
                    parser = TestScenarioParser(scenario_def)
                    scenario_objects = parser.get_scenario()

                    # Store scenarios by role
                    if role == "initiator":
                        self.initiator_scenarios[scenario_name] = scenario_objects
                    else:
                        self.responder_scenarios[scenario_name] = scenario_objects

                    logger.info(f"Loaded {len(scenario_objects)} scenario(s) for '{scenario_name}' under role '{role}'")

                except Exception as e:
                    logger.error(f"Failed to parse scenario '{scenario_name}' for role '{role}': {e}")
                    raise ScenarioParsingError(f"Error parsing scenario '{scenario_name}': {e}")

    def get_test_definition(self) -> Test:
        """
        Get the complete test definition.

        Returns:
            Test object with all scenarios
        """
        return Test(
            name=self.name,
            description=self.description,
            version=self.version,
            initiator_scenarios=self.initiator_scenarios,
            responder_scenarios=self.responder_scenarios,
        )

    def get_scenarios_for_mode(self, mode: TestMode) -> Dict[str, List[Scenario]]:
        """
        Get scenarios for a specific test mode.

        Args:
            mode: Test mode (initiator or responder)

        Returns:
            Dictionary of scenario name to scenario list
        """
        if mode == TestMode.INITIATOR:
            return self.initiator_scenarios
        elif mode == TestMode.RESPONDER:
            return self.responder_scenarios
        else:
            return {}

    def has_scenarios_for_mode(self, mode: TestMode) -> bool:
        """Check if test has scenarios for the given mode."""
        scenarios = self.get_scenarios_for_mode(mode)
        return bool(scenarios)

    def get_scenario_names(self, mode: Optional[TestMode] = None) -> List[str]:
        """
        Get list of scenario names.

        Args:
            mode: Optional test mode filter

        Returns:
            List of scenario names
        """
        if mode is None:
            return list(self.initiator_scenarios.keys()) + list(self.responder_scenarios.keys())
        elif mode == TestMode.INITIATOR:
            return list(self.initiator_scenarios.keys())
        elif mode == TestMode.RESPONDER:
            return list(self.responder_scenarios.keys())
        else:
            return []
