"""
IPsec Client Engine Implementation

This module provides the IPsec client (initiator) implementation that combines
IKEv2 and ESP protocols to create a complete IPsec initiator capable of
establishing secure tunnels and processing encrypted traffic.
"""

import asyncio
import secrets
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field

from scapy.contrib.ikev2 import IKEv2
from scapy.layers.ipsec import ESP
from scapy.layers.inet import IP, UDP

from core.ikev2.protocol import IKEv2Protocol, IKEv2Role, IKEv2SecurityAssociation
from core.esp.protocol import ESPProtocol, ESPMode
from core.ikev2.state import IKEv2State, ExchangeType
from core.ikev2.hooks import IKEv2HookManager, HookType as IKEv2HookType
from models.base import BaseModel, TimestampedModel
from utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class ClientConfiguration:
    """Configuration for IPsec client."""

    # Network configuration
    local_ip: str
    remote_ip: str
    local_port: int = 500
    remote_port: int = 500

    # IPsec configuration
    tunnel_mode: bool = True
    nat_traversal: bool = False

    # Authentication
    auth_method: str = "ECDSA_SECP256R1_SHA256"
    certificate_path: Optional[str] = None
    private_key_path: Optional[str] = None

    # Crypto preferences
    encryption_algorithms: List[str] = field(default_factory=lambda: ["AES-GCM-256"])
    integrity_algorithms: List[str] = field(default_factory=lambda: ["HMAC-SHA2-256"])
    dh_groups: List[int] = field(default_factory=lambda: [19])

    # Timeouts and retries
    negotiation_timeout: float = 30.0
    retry_count: int = 3
    keepalive_interval: float = 60.0


@dataclass
class ClientStatistics:
    """Statistics for IPsec client operations."""

    # Connection statistics
    connection_attempts: int = 0
    successful_connections: int = 0
    failed_connections: int = 0

    # IKE statistics
    ike_exchanges_initiated: int = 0
    ike_exchanges_completed: int = 0
    ike_exchanges_failed: int = 0

    # ESP statistics
    esp_packets_sent: int = 0
    esp_packets_received: int = 0
    esp_bytes_sent: int = 0
    esp_bytes_received: int = 0

    # Timing statistics
    average_connection_time: float = 0.0
    last_connection_time: Optional[datetime] = None

    # Error tracking
    last_error: Optional[str] = None
    error_count: int = 0


class IPsecClient(IKEv2Protocol, ESPProtocol):
    """
    IPsec Client (Initiator) Engine

    This class combines IKEv2 and ESP protocols to provide a complete IPsec
    initiator implementation. It can establish IKE SAs, negotiate Child SAs,
    and process ESP traffic.

    The client inherits from both IKEv2Protocol and ESPProtocol to provide:
    - Complete IKE negotiation capabilities (from IKEv2Protocol)
    - ESP packet processing capabilities (from ESPProtocol)
    - Integrated state management
    - Comprehensive hook system
    - Statistics and monitoring
    """

    def __init__(
        self,
        config: ClientConfiguration,
        ike_hook_manager: Optional[IKEv2HookManager] = None,
        esp_hook_manager: Optional[Any] = None,
    ):
        """
        Initialize the IPsec client.

        Args:
            config: Client configuration
            ike_hook_manager: Optional IKE hook manager
            esp_hook_manager: Optional ESP hook manager
        """
        self.config = config
        self.statistics = ClientStatistics()

        # Initialize parent protocols
        IKEv2Protocol.__init__(
            self,
            role=IKEv2Role.INITIATOR,
            local_ip=config.local_ip,
            remote_ip=config.remote_ip,
            local_port=config.local_port,
            remote_port=config.remote_port,
            hook_manager=ike_hook_manager,
        )

        ESPProtocol.__init__(
            self,
            mode=ESPMode.TUNNEL if config.tunnel_mode else ESPMode.TRANSPORT,
            hook_manager=esp_hook_manager,
        )

        # Client-specific state
        self.is_connected = False
        self.connection_start_time: Optional[datetime] = None
        self.last_keepalive: Optional[datetime] = None

        # Traffic tracking
        self.active_tunnels: Dict[str, Any] = {}
        self.pending_packets: List[Any] = []

        logger.info(f"IPsec Client initialized: {config.local_ip} -> {config.remote_ip}")

    async def connect(self) -> bool:
        """
        Establish IPsec connection with the remote peer.

        This method performs the complete IKE negotiation sequence:
        1. IKE_SA_INIT exchange
        2. IKE_AUTH exchange
        3. CREATE_CHILD_SA exchange (if needed)

        Returns:
            True if connection was established successfully, False otherwise
        """
        logger.info("Starting IPsec connection establishment")
        self.connection_start_time = datetime.now()
        self.statistics.connection_attempts += 1

        try:
            # Phase 1: IKE_SA_INIT
            logger.info("Phase 1: Starting IKE_SA_INIT exchange")
            if not await self._perform_ike_sa_init():
                raise Exception("IKE_SA_INIT exchange failed")

            # Phase 2: IKE_AUTH
            logger.info("Phase 2: Starting IKE_AUTH exchange")
            if not await self._perform_ike_auth():
                raise Exception("IKE_AUTH exchange failed")

            # Phase 3: CREATE_CHILD_SA (if not childless)
            if not self.config.tunnel_mode or True:  # Always create Child SA for now
                logger.info("Phase 3: Starting CREATE_CHILD_SA exchange")
                if not await self._perform_create_child_sa():
                    raise Exception("CREATE_CHILD_SA exchange failed")

            # Connection established
            self.is_connected = True
            self.statistics.successful_connections += 1
            self.statistics.last_connection_time = datetime.now()

            # Calculate connection time
            if self.connection_start_time:
                connection_time = (datetime.now() - self.connection_start_time).total_seconds()
                self.statistics.average_connection_time = (
                    (self.statistics.average_connection_time * (self.statistics.successful_connections - 1) + connection_time)
                    / self.statistics.successful_connections
                )

            logger.info("IPsec connection established successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to establish IPsec connection: {e}")
            self.statistics.failed_connections += 1
            self.statistics.last_error = str(e)
            self.statistics.error_count += 1
            return False

    async def _perform_ike_sa_init(self) -> bool:
        """Perform IKE_SA_INIT exchange."""
        try:
            self.statistics.ike_exchanges_initiated += 1

            # Initiate IKE_SA_INIT
            init_packet = await self.initiate_ike_sa_init()

            # In a real implementation, you would:
            # 1. Send the packet over the network
            # 2. Wait for response
            # 3. Process the response

            # For now, simulate successful exchange
            self.state_machine.transition_to(IKEv2State.SA_INIT_COMPLETED)
            self.statistics.ike_exchanges_completed += 1

            logger.debug("IKE_SA_INIT exchange completed")
            return True

        except Exception as e:
            logger.error(f"IKE_SA_INIT exchange failed: {e}")
            self.statistics.ike_exchanges_failed += 1
            return False

    async def _perform_ike_auth(self) -> bool:
        """Perform IKE_AUTH exchange."""
        try:
            self.statistics.ike_exchanges_initiated += 1

            # Initiate IKE_AUTH
            auth_packet = await self.initiate_ike_auth()

            # In a real implementation, you would:
            # 1. Send the packet over the network
            # 2. Wait for response
            # 3. Process the response and verify authentication

            # For now, simulate successful exchange
            self.state_machine.transition_to(IKEv2State.ESTABLISHED)
            self.statistics.ike_exchanges_completed += 1

            logger.debug("IKE_AUTH exchange completed")
            return True

        except Exception as e:
            logger.error(f"IKE_AUTH exchange failed: {e}")
            self.statistics.ike_exchanges_failed += 1
            return False

    async def _perform_create_child_sa(self) -> bool:
        """Perform CREATE_CHILD_SA exchange."""
        try:
            self.statistics.ike_exchanges_initiated += 1

            # Initiate CREATE_CHILD_SA
            child_packet = await self.initiate_create_child_sa()

            # In a real implementation, you would:
            # 1. Send the packet over the network
            # 2. Wait for response
            # 3. Process the response and establish ESP SA

            # For now, simulate successful exchange
            self.state_machine.transition_to(IKEv2State.CHILD_SA_ESTABLISHED)
            self.statistics.ike_exchanges_completed += 1

            logger.debug("CREATE_CHILD_SA exchange completed")
            return True

        except Exception as e:
            logger.error(f"CREATE_CHILD_SA exchange failed: {e}")
            self.statistics.ike_exchanges_failed += 1
            return False

    async def send_data(self, data: bytes, destination: Optional[str] = None) -> bool:
        """
        Send data through the IPsec tunnel.

        Args:
            data: Data to send
            destination: Optional destination override

        Returns:
            True if data was sent successfully, False otherwise
        """
        if not self.is_connected:
            logger.error("Cannot send data: IPsec connection not established")
            return False

        try:
            # Create ESP packet (simplified)
            esp_packet = self._create_esp_packet(data, destination)

            # In a real implementation, you would send this over the network
            self.statistics.esp_packets_sent += 1
            self.statistics.esp_bytes_sent += len(data)

            logger.debug(f"Sent {len(data)} bytes through IPsec tunnel")
            return True

        except Exception as e:
            logger.error(f"Failed to send data: {e}")
            return False

    def _create_esp_packet(self, data: bytes, destination: Optional[str] = None) -> ESP:
        """Create an ESP packet for the given data."""
        # This is a simplified implementation
        # In reality, you would use the ESP protocol methods
        spi = secrets.token_bytes(4)
        seq = 1  # Would be managed by ESP protocol

        return ESP(spi=int.from_bytes(spi, 'big'), seq=seq) / data

    async def disconnect(self) -> bool:
        """
        Disconnect from the remote peer.

        Returns:
            True if disconnection was successful, False otherwise
        """
        if not self.is_connected:
            logger.warning("Already disconnected")
            return True

        try:
            # Send INFORMATIONAL message to notify peer
            await self.send_informational_message("DELETE")

            # Reset state
            self.is_connected = False
            self.state_machine.reset()

            logger.info("IPsec connection disconnected")
            return True

        except Exception as e:
            logger.error(f"Error during disconnection: {e}")
            return False

    def get_connection_status(self) -> Dict[str, Any]:
        """Get current connection status."""
        return {
            "connected": self.is_connected,
            "ike_state": self.state_machine.current_state.value,
            "connection_time": (
                (datetime.now() - self.connection_start_time).total_seconds()
                if self.connection_start_time else None
            ),
            "ike_sa_established": self.ike_sa is not None,
            "child_sas_count": len(self.child_sas),
            "statistics": {
                "connection_attempts": self.statistics.connection_attempts,
                "successful_connections": self.statistics.successful_connections,
                "failed_connections": self.statistics.failed_connections,
                "esp_packets_sent": self.statistics.esp_packets_sent,
                "esp_packets_received": self.statistics.esp_packets_received,
            }
        }

    def get_detailed_statistics(self) -> ClientStatistics:
        """Get detailed client statistics."""
        return self.statistics
