"""
IPsec Engine Integration Module

This module provides high-level interfaces for creating and managing IPsec
clients and servers, demonstrating how to use the combined IKEv2 and ESP
protocol implementations.
"""

import asyncio
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass

from .client import IPsecClient, ClientConfiguration
from .server import IPsecServer, ServerConfiguration
from core.ikev2.hooks import IKEv2HookManager, HookType as IKEv2HookType
from utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class IPsecEngineConfiguration:
    """Configuration for the IPsec engine."""

    # Default client configuration
    default_client_config: Optional[ClientConfiguration] = None

    # Default server configuration
    default_server_config: Optional[ServerConfiguration] = None

    # Hook managers
    enable_hooks: bool = True
    hook_debug: bool = False


class IPsecEngine:
    """
    High-level IPsec Engine

    This class provides a unified interface for managing IPsec clients and servers,
    making it easy to create, configure, and manage IPsec connections.
    """

    def __init__(self, config: Optional[IPsecEngineConfiguration] = None):
        """
        Initialize the IPsec engine.

        Args:
            config: Optional engine configuration
        """
        self.config = config or IPsecEngineConfiguration()

        # Active instances
        self.clients: Dict[str, IPsecClient] = {}
        self.servers: Dict[str, IPsecServer] = {}

        # Hook managers
        self.ike_hook_manager = IKEv2HookManager() if self.config.enable_hooks else None

        # Statistics
        self.total_clients_created = 0
        self.total_servers_created = 0

        logger.info("IPsec Engine initialized")

    def create_client(
        self,
        name: str,
        config: Optional[ClientConfiguration] = None,
        **kwargs
    ) -> IPsecClient:
        """
        Create a new IPsec client.

        Args:
            name: Unique name for the client
            config: Client configuration (uses default if not provided)
            **kwargs: Additional configuration parameters

        Returns:
            The created IPsec client
        """
        if name in self.clients:
            raise ValueError(f"Client '{name}' already exists")

        # Use provided config or default
        client_config = config or self.config.default_client_config
        if not client_config:
            raise ValueError("No client configuration provided and no default available")

        # Apply kwargs overrides
        if kwargs:
            # Create a copy and update with kwargs
            import copy
            client_config = copy.deepcopy(client_config)
            for key, value in kwargs.items():
                if hasattr(client_config, key):
                    setattr(client_config, key, value)

        # Create client
        client = IPsecClient(
            config=client_config,
            ike_hook_manager=self.ike_hook_manager,
        )

        self.clients[name] = client
        self.total_clients_created += 1

        logger.info(f"Created IPsec client '{name}': {client_config.local_ip} -> {client_config.remote_ip}")
        return client

    def create_server(
        self,
        name: str,
        config: Optional[ServerConfiguration] = None,
        **kwargs
    ) -> IPsecServer:
        """
        Create a new IPsec server.

        Args:
            name: Unique name for the server
            config: Server configuration (uses default if not provided)
            **kwargs: Additional configuration parameters

        Returns:
            The created IPsec server
        """
        if name in self.servers:
            raise ValueError(f"Server '{name}' already exists")

        # Use provided config or default
        server_config = config or self.config.default_server_config
        if not server_config:
            raise ValueError("No server configuration provided and no default available")

        # Apply kwargs overrides
        if kwargs:
            # Create a copy and update with kwargs
            import copy
            server_config = copy.deepcopy(server_config)
            for key, value in kwargs.items():
                if hasattr(server_config, key):
                    setattr(server_config, key, value)

        # Create server
        server = IPsecServer(
            config=server_config,
            ike_hook_manager=self.ike_hook_manager,
        )

        self.servers[name] = server
        self.total_servers_created += 1

        logger.info(f"Created IPsec server '{name}' on {server_config.listen_ip}:{server_config.listen_port}")
        return server

    def get_client(self, name: str) -> Optional[IPsecClient]:
        """Get a client by name."""
        return self.clients.get(name)

    def get_server(self, name: str) -> Optional[IPsecServer]:
        """Get a server by name."""
        return self.servers.get(name)

    def remove_client(self, name: str) -> bool:
        """Remove a client."""
        if name not in self.clients:
            return False

        client = self.clients[name]
        # Disconnect if connected
        if client.is_connected:
            asyncio.create_task(client.disconnect())

        del self.clients[name]
        logger.info(f"Removed IPsec client '{name}'")
        return True

    def remove_server(self, name: str) -> bool:
        """Remove a server."""
        if name not in self.servers:
            return False

        server = self.servers[name]
        # Stop if running
        if server.is_running:
            asyncio.create_task(server.stop())

        del self.servers[name]
        logger.info(f"Removed IPsec server '{name}'")
        return True

    async def connect_client(self, name: str) -> bool:
        """Connect a client by name."""
        client = self.get_client(name)
        if not client:
            logger.error(f"Client '{name}' not found")
            return False

        return await client.connect()

    async def start_server(self, name: str) -> bool:
        """Start a server by name."""
        server = self.get_server(name)
        if not server:
            logger.error(f"Server '{name}' not found")
            return False

        return await server.start()

    def add_hook(self, hook_type: IKEv2HookType, hook_function: Callable) -> bool:
        """
        Add a hook function to the engine.

        Args:
            hook_type: Type of hook to add
            hook_function: The hook function to execute

        Returns:
            True if hook was added successfully
        """
        if not self.ike_hook_manager:
            logger.error("Hook manager not enabled")
            return False

        try:
            self.ike_hook_manager.register_hook(hook_type, hook_function)
            logger.info(f"Added hook for {hook_type}")
            return True
        except Exception as e:
            logger.error(f"Failed to add hook: {e}")
            return False

    def get_engine_status(self) -> Dict[str, Any]:
        """Get overall engine status."""
        return {
            "clients": {
                "total_created": self.total_clients_created,
                "active": len(self.clients),
                "connected": len([c for c in self.clients.values() if c.is_connected]),
                "names": list(self.clients.keys()),
            },
            "servers": {
                "total_created": self.total_servers_created,
                "active": len(self.servers),
                "running": len([s for s in self.servers.values() if s.is_running]),
                "names": list(self.servers.keys()),
            },
            "hooks_enabled": self.ike_hook_manager is not None,
        }

    def get_all_statistics(self) -> Dict[str, Any]:
        """Get statistics for all clients and servers."""
        stats = {
            "engine": self.get_engine_status(),
            "clients": {},
            "servers": {},
        }

        # Client statistics
        for name, client in self.clients.items():
            stats["clients"][name] = client.get_connection_status()

        # Server statistics
        for name, server in self.servers.items():
            stats["servers"][name] = server.get_server_status()

        return stats


# Convenience functions for quick setup
def create_simple_client(local_ip: str, remote_ip: str, **kwargs) -> IPsecClient:
    """
    Create a simple IPsec client with minimal configuration.

    Args:
        local_ip: Local IP address
        remote_ip: Remote IP address
        **kwargs: Additional configuration parameters

    Returns:
        Configured IPsec client
    """
    config = ClientConfiguration(
        local_ip=local_ip,
        remote_ip=remote_ip,
        **kwargs
    )

    return IPsecClient(config=config)


def create_simple_server(listen_ip: str = "0.0.0.0", listen_port: int = 500, **kwargs) -> IPsecServer:
    """
    Create a simple IPsec server with minimal configuration.

    Args:
        listen_ip: IP address to listen on
        listen_port: Port to listen on
        **kwargs: Additional configuration parameters

    Returns:
        Configured IPsec server
    """
    config = ServerConfiguration(
        listen_ip=listen_ip,
        listen_port=listen_port,
        **kwargs
    )

    return IPsecServer(config=config)


async def run_client_server_demo():
    """
    Demonstration of client-server interaction.
    """
    logger.info("=== IPsec Client-Server Demo ===")

    # Create engine
    engine = IPsecEngine()

    # Create server
    server_config = ServerConfiguration(
        listen_ip="127.0.0.1",
        listen_port=500,
    )
    server = engine.create_server("demo_server", server_config)

    # Create client
    client_config = ClientConfiguration(
        local_ip="127.0.0.1",
        remote_ip="127.0.0.1",
        local_port=4500,
        remote_port=500,
    )
    client = engine.create_client("demo_client", client_config)

    try:
        # Start server
        logger.info("Starting server...")
        await engine.start_server("demo_server")

        # Connect client
        logger.info("Connecting client...")
        await engine.connect_client("demo_client")

        # Show status
        status = engine.get_engine_status()
        logger.info(f"Engine status: {status}")

        # Simulate some data transfer
        if client.is_connected:
            await client.send_data(b"Hello from client!")

        # Show statistics
        stats = engine.get_all_statistics()
        logger.info("=== Final Statistics ===")
        for category, data in stats.items():
            logger.info(f"{category}: {data}")

    except Exception as e:
        logger.error(f"Demo failed: {e}")

    finally:
        # Cleanup
        if client.is_connected:
            await client.disconnect()
        if server.is_running:
            await server.stop()


if __name__ == "__main__":
    # Run the demo
    asyncio.run(run_client_server_demo())
