"""
Enhanced compliance checker for IPsec test results.

This module provides comprehensive compliance validation with:
- ANSSI compliance checking for French security requirements
- Algorithm validation and approval verification
- Security parameter validation
- Performance and timing analysis
- Detailed reporting and recommendations
"""

from typing import Any, Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

from models.tests import ComplianceResult
from models.base import ComplianceLevel
from models.config import TestConfiguration
from models.scenario import TestScenario
from core.crypto.dh import EnhancedDHManager
from utils.logging import get_logger
from .tester import TestData

logger = get_logger(__name__)


class ComplianceStandard(Enum):
    """Supported compliance standards."""

    ANSSI = "anssi"
    NIST = "nist"
    COMMON_CRITERIA = "common_criteria"
    FIPS_140 = "fips_140"


@dataclass
class ComplianceRule:
    """Definition of a compliance rule."""

    rule_id: str
    name: str
    description: str
    standard: ComplianceStandard
    severity: str  # "critical", "major", "minor", "info"
    category: str  # "algorithm", "key_size", "protocol", "timing"


class EnhancedChecker:
    """
    Enhanced compliance checker for IPsec test results.

    Provides comprehensive compliance validation with:
    - ANSSI compliance checking for French security requirements
    - Algorithm validation and approval verification
    - Security parameter validation
    - Performance and timing analysis
    - Detailed reporting and recommendations
    """

    # ANSSI compliance rules
    ANSSI_RULES = {
        "ANSSI-001": ComplianceRule(
            "ANSSI-001",
            "Approved Encryption Algorithms",
            "Only ANSSI-approved encryption algorithms must be used",
            ComplianceStandard.ANSSI,
            "critical",
            "algorithm",
        ),
        "ANSSI-002": ComplianceRule(
            "ANSSI-002",
            "Minimum Key Sizes",
            "Encryption keys must be at least 256 bits",
            ComplianceStandard.ANSSI,
            "critical",
            "key_size",
        ),
        "ANSSI-003": ComplianceRule(
            "ANSSI-003",
            "Approved DH Groups",
            "Only ANSSI-approved DH groups must be used",
            ComplianceStandard.ANSSI,
            "critical",
            "algorithm",
        ),
        "ANSSI-004": ComplianceRule(
            "ANSSI-004",
            "Integrity Protection",
            "Integrity protection must use approved algorithms",
            ComplianceStandard.ANSSI,
            "major",
            "algorithm",
        ),
        "ANSSI-005": ComplianceRule(
            "ANSSI-005",
            "Perfect Forward Secrecy",
            "Perfect Forward Secrecy must be ensured",
            ComplianceStandard.ANSSI,
            "major",
            "protocol",
        ),
        "ANSSI-006": ComplianceRule(
            "ANSSI-006",
            "Exchange Timing",
            "IKE exchanges must complete within reasonable time",
            ComplianceStandard.ANSSI,
            "minor",
            "timing",
        ),
    }

    # ANSSI-approved algorithms
    ANSSI_APPROVED = {
        "encryption": ["AES_GCM_16", "AES_CTR", "CHACHA20_POLY1305"],
        "integrity": ["HMAC_SHA2_256", "HMAC_SHA2_384", "HMAC_SHA2_512"],
        "prf": ["PRF_HMAC_SHA2_256", "PRF_HMAC_SHA2_384", "PRF_HMAC_SHA2_512"],
        "dh_groups": [19, 20, 21, 27, 28, 29, 30],  # secp and brainpool curves
    }

    def __init__(
        self,
        test_id: str,
        config: TestConfiguration,
        scenario: TestScenario,
        standards: List[ComplianceStandard] = None,
    ):
        """
        Initialize the enhanced checker.

        Args:
            test_id: Unique test identifier
            config: Test configuration
            scenario: Test scenario
            standards: List of compliance standards to check
        """
        self.test_id = test_id
        self.config = config
        self.scenario = scenario
        self.standards = standards or [ComplianceStandard.ANSSI]

        # Initialize DH manager for curve validation
        self.dh_manager = EnhancedDHManager()

        logger.info(
            f"Enhanced Checker initialized for {test_id} with standards: {[s.value for s in self.standards]}"
        )

    async def check_compliance(self, test_data: TestData) -> ComplianceResult:
        """
        Check comprehensive compliance of test results.

        Args:
            test_data: Complete test execution data

        Returns:
            Detailed compliance result
        """
        logger.info(f"Starting compliance check for {self.test_id}")

        issues = []
        warnings = []
        recommendations = []
        score = 100.0

        # Check each compliance standard
        for standard in self.standards:
            if standard == ComplianceStandard.ANSSI:
                standard_result = await self._check_anssi_compliance(test_data)
                issues.extend(standard_result["issues"])
                warnings.extend(standard_result["warnings"])
                recommendations.extend(standard_result["recommendations"])
                score = min(score, standard_result["score"])

        # Determine overall compliance level
        if score >= 95.0 and not issues:
            level = ComplianceLevel.COMPLIANT
        elif score >= 80.0 and len([i for i in issues if "critical" in i]) == 0:
            level = ComplianceLevel.PARTIAL
        else:
            level = ComplianceLevel.NON_COMPLIANT

        result = ComplianceResult(
            level=level,
            score=score,
            issues=issues,
            warnings=warnings,
            recommendations=recommendations,
        )

        logger.info(f"Compliance check completed: {level.value} (score: {score:.1f}%)")
        return result

    async def _check_anssi_compliance(self, test_data: TestData) -> Dict[str, Any]:
        """Check ANSSI compliance requirements."""
        logger.debug("Checking ANSSI compliance")

        issues = []
        warnings = []
        recommendations = []
        score = 100.0

        # Check encryption algorithms
        encryption_result = self._check_encryption_algorithms(test_data)
        issues.extend(encryption_result["issues"])
        warnings.extend(encryption_result["warnings"])
        score -= encryption_result["penalty"]

        # Check DH groups
        dh_result = self._check_dh_groups(test_data)
        issues.extend(dh_result["issues"])
        warnings.extend(dh_result["warnings"])
        score -= dh_result["penalty"]

        # Check key sizes
        key_result = self._check_key_sizes(test_data)
        issues.extend(key_result["issues"])
        warnings.extend(key_result["warnings"])
        score -= key_result["penalty"]

        # Check protocol compliance
        protocol_result = self._check_protocol_compliance(test_data)
        issues.extend(protocol_result["issues"])
        warnings.extend(protocol_result["warnings"])
        score -= protocol_result["penalty"]

        # Check timing requirements
        timing_result = self._check_timing_requirements(test_data)
        warnings.extend(timing_result["warnings"])
        recommendations.extend(timing_result["recommendations"])
        score -= timing_result["penalty"]

        # Add general recommendations
        recommendations.extend(
            [
                "Consider using brainpool curves for enhanced ANSSI compliance",
                "Implement certificate-based authentication for production use",
                "Enable Perfect Forward Secrecy for all connections",
                "Regular security audits and compliance validation recommended",
            ]
        )

        return {
            "issues": issues,
            "warnings": warnings,
            "recommendations": recommendations,
            "score": max(0.0, score),
        }

    def _check_encryption_algorithms(self, test_data: TestData) -> Dict[str, Any]:
        """Check encryption algorithm compliance."""
        issues = []
        warnings = []
        penalty = 0.0

        # Check IKEv2 encryption algorithms
        for exchange in test_data.ikev2_exchanges:
            if "algorithms" in exchange:
                encryption_algs = exchange["algorithms"].get("encryption", [])
                for alg in encryption_algs:
                    if alg not in self.ANSSI_APPROVED["encryption"]:
                        issues.append(
                            f"ANSSI-001: Non-approved encryption algorithm: {alg}"
                        )
                        penalty += 20.0

        # Check ESP encryption algorithms
        for packet in test_data.esp_packets:
            alg = packet.get("encryption_algorithm")
            if alg and alg not in self.ANSSI_APPROVED["encryption"]:
                issues.append(
                    f"ANSSI-001: Non-approved ESP encryption algorithm: {alg}"
                )
                penalty += 15.0

        return {"issues": issues, "warnings": warnings, "penalty": penalty}

    def _check_dh_groups(self, test_data: TestData) -> Dict[str, Any]:
        """Check DH group compliance."""
        issues = []
        warnings = []
        penalty = 0.0

        # Check DH groups used in exchanges
        for exchange in test_data.ikev2_exchanges:
            if "algorithms" in exchange:
                dh_groups = exchange["algorithms"].get("dh_groups", [])
                for group in dh_groups:
                    if group not in self.ANSSI_APPROVED["dh_groups"]:
                        issues.append(f"ANSSI-003: Non-approved DH group: {group}")
                        penalty += 25.0
                    elif not self.dh_manager.is_anssi_approved(group):
                        warnings.append(
                            f"DH group {group} may not be optimal for ANSSI compliance"
                        )

        return {"issues": issues, "warnings": warnings, "penalty": penalty}

    def _check_key_sizes(self, test_data: TestData) -> Dict[str, Any]:
        """Check key size compliance."""
        issues = []
        warnings = []
        penalty = 0.0

        # Check configured key sizes
        if hasattr(self.config.crypto, "encryption_key_sizes"):
            for key_size in self.config.crypto.encryption_key_sizes:
                if key_size < 256:
                    issues.append(
                        f"ANSSI-002: Insufficient key size: {key_size} bits (minimum 256)"
                    )
                    penalty += 30.0
                elif key_size < 384:
                    warnings.append(
                        f"Key size {key_size} bits meets minimum but 384+ recommended"
                    )

        return {"issues": issues, "warnings": warnings, "penalty": penalty}

    def _check_protocol_compliance(self, test_data: TestData) -> Dict[str, Any]:
        """Check protocol-level compliance."""
        issues = []
        warnings = []
        penalty = 0.0

        # Check for required exchanges
        exchange_types = [ex.get("exchange_type") for ex in test_data.ikev2_exchanges]

        if "IKE_SA_INIT" not in exchange_types:
            issues.append("Missing required IKE_SA_INIT exchange")
            penalty += 40.0

        if "IKE_AUTH" not in exchange_types:
            issues.append("Missing required IKE_AUTH exchange")
            penalty += 40.0

        # Check authentication method
        for exchange in test_data.ikev2_exchanges:
            if exchange.get("exchange_type") == "IKE_AUTH":
                auth_method = exchange.get("authentication_method")
                if auth_method and "SIGNATURE" not in auth_method:
                    warnings.append(
                        "Certificate-based authentication recommended for ANSSI compliance"
                    )

        return {"issues": issues, "warnings": warnings, "penalty": penalty}

    def _check_timing_requirements(self, test_data: TestData) -> Dict[str, Any]:
        """Check timing and performance requirements."""
        warnings = []
        recommendations = []
        penalty = 0.0

        # Check exchange timings
        for exchange_type, timing in test_data.exchange_timings.items():
            if timing > 5.0:  # 5 second threshold
                warnings.append(
                    f"ANSSI-006: {exchange_type} exchange took {timing:.2f}s (>5s threshold)"
                )
                penalty += 5.0
            elif timing > 2.0:  # 2 second recommendation
                recommendations.append(
                    f"{exchange_type} timing ({timing:.2f}s) could be optimized"
                )

        # Check ESP packet processing
        esp_timings = [p.get("processing_time", 0) for p in test_data.esp_packets]
        if esp_timings:
            avg_esp_time = sum(esp_timings) / len(esp_timings)
            if avg_esp_time > 0.1:  # 100ms threshold
                warnings.append(
                    f"ESP packet processing averaging {avg_esp_time:.3f}s (>0.1s threshold)"
                )
                penalty += 3.0

        return {
            "warnings": warnings,
            "recommendations": recommendations,
            "penalty": penalty,
        }

    def get_compliance_summary(self, result: ComplianceResult) -> Dict[str, Any]:
        """Get a summary of compliance results."""
        return {
            "test_id": self.test_id,
            "scenario": self.scenario.name,
            "compliance_level": result.level.value,
            "score": result.score,
            "standards_checked": [s.value for s in self.standards],
            "critical_issues": len(
                [i for i in result.issues if "critical" in i.lower()]
            ),
            "total_issues": len(result.issues),
            "warnings": len(result.warnings),
            "recommendations": len(result.recommendations),
        }


# Maintain backward compatibility
Checker = EnhancedChecker
