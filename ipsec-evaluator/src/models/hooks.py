"""
Hook system models for packet and exchange callbacks.
"""

from typing import Any, Dict, List, Optional
from pydantic import Field, field_validator

from .base import BaseModel, IdentifiedModel, HookType


class HookDefinition(IdentifiedModel):
    """Definition of a hook callback."""

    hook_type: HookType = Field(description="Type of hook")
    trigger_condition: str = Field(description="Condition that triggers the hook")
    callback_function: str = Field(description="Name of the callback function")
    priority: int = Field(default=100, description="Hook execution priority (lower = higher priority)")
    enabled: bool = Field(default=True, description="Whether the hook is enabled")

    # Hook-specific parameters
    packet_number: Optional[int] = Field(default=None, description="Packet number for packet_number hooks")
    exchange_step: Optional[str] = Field(default=None, description="Exchange step for exchange_step hooks")

    @field_validator("priority")
    @classmethod
    def validate_priority(cls, v: int) -> int:
        """Validate hook priority."""
        if v < 0:
            raise ValueError("Priority must be non-negative")
        return v


class CallbackConfig(BaseModel):
    """Configuration for callback execution."""

    timeout: float = Field(default=5.0, description="Callback timeout in seconds")
    retry_count: int = Field(default=0, description="Number of retries on failure")
    fail_on_error: bool = Field(default=False, description="Whether to fail the test on callback error")
    capture_output: bool = Field(default=True, description="Whether to capture callback output")

    @field_validator("timeout")
    @classmethod
    def validate_timeout(cls, v: float) -> float:
        """Validate timeout value."""
        if v <= 0:
            raise ValueError("Timeout must be positive")
        return v


class HookResult(BaseModel):
    """Result of hook execution."""

    hook_id: str = Field(description="ID of the executed hook")
    success: bool = Field(description="Whether the hook executed successfully")
    execution_time: float = Field(description="Hook execution time in seconds")
    output: Optional[str] = Field(default=None, description="Hook output/result")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class PacketHook(HookDefinition):
    """Hook that triggers on specific packet numbers."""

    hook_type: HookType = Field(default=HookType.PACKET_NUMBER, frozen=True)
    packet_number: int = Field(description="Packet number to trigger on")

    @field_validator("packet_number")
    @classmethod
    def validate_packet_number(cls, v: int) -> int:
        """Validate packet number."""
        if v < 1:
            raise ValueError("Packet number must be positive")
        return v


class ExchangeHook(HookDefinition):
    """Hook that triggers on exchange steps."""

    hook_type: HookType = Field(default=HookType.EXCHANGE_STEP, frozen=True)
    exchange_step: str = Field(description="Exchange step to trigger on")

    @field_validator("exchange_step")
    @classmethod
    def validate_exchange_step(cls, v: str) -> str:
        """Validate exchange step."""
        valid_steps = [
            "IKE_SA_INIT", "IKE_AUTH", "CREATE_CHILD_SA",
            "INFORMATIONAL", "ESP", "pre_exchange", "post_exchange"
        ]
        if v not in valid_steps:
            raise ValueError(f"Invalid exchange step: {v}. Must be one of {valid_steps}")
        return v


class UniversalHook(HookDefinition):
    """Hook that can trigger on any condition."""

    hook_type: HookType = Field(default=HookType.UNIVERSAL, frozen=True)
    custom_condition: str = Field(description="Custom trigger condition")


class HookRegistry(BaseModel):
    """Registry for managing hooks."""

    hooks: List[HookDefinition] = Field(default_factory=list)
    global_config: CallbackConfig = Field(default_factory=CallbackConfig)

    def add_hook(self, hook: HookDefinition) -> None:
        """Add a hook to the registry."""
        self.hooks.append(hook)

    def remove_hook(self, hook_id: str) -> bool:
        """Remove a hook by ID."""
        for i, hook in enumerate(self.hooks):
            if str(hook.id) == hook_id:
                del self.hooks[i]
                return True
        return False

    def get_hooks_by_type(self, hook_type: HookType) -> List[HookDefinition]:
        """Get all hooks of a specific type."""
        return [hook for hook in self.hooks if hook.hook_type == hook_type and hook.enabled]

    def get_packet_hooks(self, packet_number: int) -> List[PacketHook]:
        """Get hooks for a specific packet number."""
        return [
            hook for hook in self.hooks
            if (isinstance(hook, PacketHook) and
                hook.packet_number == packet_number and
                hook.enabled)
        ]

    def get_exchange_hooks(self, exchange_step: str) -> List[ExchangeHook]:
        """Get hooks for a specific exchange step."""
        return [
            hook for hook in self.hooks
            if (isinstance(hook, ExchangeHook) and
                hook.exchange_step == exchange_step and
                hook.enabled)
        ]