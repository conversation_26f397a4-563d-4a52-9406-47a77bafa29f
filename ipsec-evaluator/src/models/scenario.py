"""
Test scenario models and definitions.
"""

from pathlib import Path
from typing import Dict, List, Optional, Any
import yaml
from pydantic import Field, field_validator

from .base import BaseModel, IdentifiedModel, ExchangeType, TestMode


class ExchangeDefinition(BaseModel):
    """Definition of a single exchange in a scenario."""

    exchange_type: ExchangeType = Field(description="Type of exchange")
    sequence_number: int = Field(description="Order in the scenario")
    configuration: Dict[str, Any] = Field(default_factory=dict, description="Exchange-specific configuration")
    expected_outcome: str = Field(default="success", description="Expected outcome")
    timeout: float = Field(default=30.0, description="Exchange timeout in seconds")

    @field_validator("sequence_number")
    @classmethod
    def validate_sequence_number(cls, v: int) -> int:
        """Validate sequence number."""
        if v < 1:
            raise ValueError("Sequence number must be positive")
        return v


class PacketDefinition(BaseModel):
    """Definition of a packet within an exchange."""

    packet_type: str = Field(description="Type of packet")
    direction: str = Field(description="Packet direction: inbound/outbound")
    payload_data: Optional[bytes] = Field(default=None, description="Raw packet payload")
    modifications: Dict[str, Any] = Field(default_factory=dict, description="Packet modifications")

    @field_validator("direction")
    @classmethod
    def validate_direction(cls, v: str) -> str:
        """Validate packet direction."""
        if v.lower() not in ["inbound", "outbound"]:
            raise ValueError("Direction must be 'inbound' or 'outbound'")
        return v.lower()


class TestScenario(IdentifiedModel):
    """Complete test scenario definition."""

    name: str = Field(description="Scenario name")
    description: str = Field(description="Scenario description")
    version: str = Field(default="1.0", description="Scenario version")

    # Test configuration
    initiator_mode: bool = Field(default=True, description="Can run as initiator")
    responder_mode: bool = Field(default=True, description="Can run as responder")

    # Exchanges and packets
    exchanges: List[ExchangeDefinition] = Field(default_factory=list, description="List of exchanges")
    packets: List[PacketDefinition] = Field(default_factory=list, description="List of packets")

    # Scenario metadata
    tags: List[str] = Field(default_factory=list, description="Scenario tags")
    compliance_level: str = Field(default="standard", description="Required compliance level")

    @classmethod
    def from_file(cls, file_path: Path) -> "TestScenario":
        """Load test scenario from YAML file."""
        try:
            with open(file_path, 'r') as f:
                data = yaml.safe_load(f)
            return cls(**data)
        except Exception as e:
            raise ValueError(f"Failed to load scenario from {file_path}: {e}")

    def has_initiator_tests(self) -> bool:
        """Check if scenario has initiator tests."""
        return self.initiator_mode

    def has_responder_tests(self) -> bool:
        """Check if scenario has responder tests."""
        return self.responder_mode

    def get_test_modes(self) -> List[TestMode]:
        """Get list of available test modes."""
        modes = []
        if self.initiator_mode:
            modes.append(TestMode.INITIATOR)
        if self.responder_mode:
            modes.append(TestMode.RESPONDER)
        return modes

    def get_exchanges_for_mode(self, mode: TestMode) -> List[ExchangeDefinition]:
        """Get exchanges for a specific test mode."""
        if mode == TestMode.INITIATOR and not self.initiator_mode:
            return []
        if mode == TestMode.RESPONDER and not self.responder_mode:
            return []
        return self.exchanges

    def get_exchange_by_type(self, exchange_type: ExchangeType) -> Optional[ExchangeDefinition]:
        """Get first exchange of a specific type."""
        for exchange in self.exchanges:
            if exchange.exchange_type == exchange_type:
                return exchange
        return None

    def validate_scenario(self) -> List[str]:
        """Validate scenario configuration and return any issues."""
        issues = []

        if not self.exchanges:
            issues.append("Scenario must have at least one exchange")

        if not (self.initiator_mode or self.responder_mode):
            issues.append("Scenario must support at least one test mode")

        # Check exchange sequence numbers are unique and sequential
        sequence_numbers = [ex.sequence_number for ex in self.exchanges]
        if len(set(sequence_numbers)) != len(sequence_numbers):
            issues.append("Exchange sequence numbers must be unique")

        return issues
