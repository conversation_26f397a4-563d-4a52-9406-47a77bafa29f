"""
Data models and schemas for IPsec Evaluator.

This module contains Pydantic models for configuration, test scenarios,
results, and all data structures used throughout the application.
"""

from .base import *
from .config import *
from .scenario import *
from .tests import *
from .hooks import *

__all__ = [
    # Base models
    "BaseModel",
    "ConfigModel",
    "TimestampedModel",
    "IdentifiedModel",
    "MetadataModel",

    # Enums
    "ExchangeType",
    "TestMode",
    "TestStatus",
    "ComplianceLevel",
    "HookType",

    # Configuration models
    "GlobalConfig",
    "NetworkConfig",
    "CryptoConfig",
    "PKIConfig",
    "AuthConfig",
    "TSConfig",
    "IDConfig",
    "NATConfig",
    "NotifyPayload",
    "IPsecConfig",
    "ESPTests",
    "ESPConfig",

    # Scenario models
    "TestScenario",
    "ExchangeDefinition",
    "PacketDefinition",

    # Test and results models
    "Scenario",
    "Outcome",
    "Summary",
    "TestResult",
    "ComplianceResult",
    "ComplianceReport",
    "Test",
    "TestPoolEntry",

    # Hook models
    "HookDefinition",
    "CallbackConfig",
    "HookResult",
    "PacketHook",
    "ExchangeHook",
    "UniversalHook",
    "HookRegistry",
]
