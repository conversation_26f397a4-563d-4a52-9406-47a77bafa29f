"""
IKEv2 protocol implementation module.

This module provides a comprehensive IKEv2 implementation with:
- Modern hook system for extensibility
- State machine management
- ANSSI compliance validation
- Enhanced payload building
- Rich metadata collection
"""

from .protocol import IKEv2Protocol, IKEv2Role, IKEv2SecurityAssociation, IKEv2Exchange
from .state import IKEv2StateMachine, IKEv2State, ExchangeType
from .hooks import IKEv2HookManager, HookType, HookContext, HookPriority
from .payloads import IKEv2PayloadBuilder, ProposalConfig, PayloadType

__all__ = [
    # Core protocol
    "IKEv2Protocol",
    "IKEv2Role",
    "IKEv2SecurityAssociation",
    "IKEv2Exchange",
    # State management
    "IKEv2StateMachine",
    "IKEv2State",
    "ExchangeType",
    # Hook system
    "IKEv2HookManager",
    "HookType",
    "HookContext",
    "HookPriority",
    # Payload building
    "IKEv2PayloadBuilder",
    "ProposalConfig",
    "PayloadType",
]
