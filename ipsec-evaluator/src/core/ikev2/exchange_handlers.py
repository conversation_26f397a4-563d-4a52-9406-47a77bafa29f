"""
IKEv2 exchange handlers implementation.

This module provides the missing handle_exchange* functions for the IKEv2 protocol
implementation, based on the patterns from ipsecdr but modernized for the
ipsec-evaluator architecture.
"""

import secrets
from datetime import datetime
from typing import Optional
from scapy.contrib.ikev2 import IKEv2

from utils.logging import get_logger
from ..crypto.algorithms import EncryptionAlgorithm, IntegrityAlgorithm, PRFAlgorithm
from .state import IKEv2State, ExchangeType
from .hooks import HookType, HookContext

logger = get_logger(__name__)


class IKEv2ExchangeHandlers:
    """
    Mixin class providing IKEv2 exchange handling methods.

    This class contains the implementation of all handle_exchange* functions
    that were missing from the main IKEv2Protocol class.
    """

    async def _handle_ike_sa_init(
        self, packet: IKEv2, context: HookContext
    ) -> Optional[IKEv2]:
        """
        Handle IKE_SA_INIT exchange.

        Args:
            packet: The received IKE_SA_INIT packet
            context: Hook context for the exchange

        Returns:
            Response packet if one should be generated
        """
        logger.debug(f"Handling IKE_SA_INIT as {self.role.value}")

        try:
            # Execute exchange-specific hooks
            await self.hook_manager.execute_hooks(HookType.EXCHANGE_STEP, context)

            if self.role.value == "responder":
                return await self._handle_ike_sa_init_responder(packet, context)
            else:
                return await self._handle_ike_sa_init_initiator(packet, context)

        except Exception as e:
            logger.error(f"Error in IKE_SA_INIT handler: {e}")
            if self.current_exchange:
                self.current_exchange.mark_completed(False, str(e))
            raise

    async def _handle_ike_sa_init_responder(
        self, packet: IKEv2, context: HookContext
    ) -> Optional[IKEv2]:
        """Handle IKE_SA_INIT as responder."""
        logger.debug("Processing IKE_SA_INIT as responder")

        # Extract SPIs
        spi_i = packet.init_SPI
        spi_r = secrets.token_bytes(8)

        # Generate responder nonce and key exchange material
        nonce_r = secrets.token_bytes(32)

        # Create security association
        from .protocol import IKEv2SecurityAssociation
        self.ike_sa = IKEv2SecurityAssociation(
            spi_i=spi_i,
            spi_r=spi_r,
            encryption_algorithm=EncryptionAlgorithm.AES_GCM_256,
            integrity_algorithm=None,  # AEAD mode
            prf_algorithm=PRFAlgorithm.HMAC_SHA2_256,
            dh_group=19,  # secp256r1
        )

        # Build response packet
        response = self.payload_builder.build_ike_sa_init_response(
            spi_i=spi_i,
            spi_r=spi_r,
            message_id=packet.id,
            nonce=nonce_r,
            dh_group=19,
        )

        # Update state
        self.state_machine.transition_to(IKEv2State.SA_INIT_COMPLETED)

        if self.current_exchange:
            self.current_exchange.responder_packet = response
            self.current_exchange.mark_completed(True)

        logger.debug("IKE_SA_INIT response generated")
        return response

    async def _handle_ike_sa_init_initiator(
        self, packet: IKEv2, context: HookContext
    ) -> Optional[IKEv2]:
        """Handle IKE_SA_INIT as initiator (processing response)."""
        logger.debug("Processing IKE_SA_INIT response as initiator")

        # Extract responder SPI
        spi_r = packet.resp_SPI

        if self.ike_sa:
            self.ike_sa.spi_r = spi_r

        # Update state
        self.state_machine.transition_to(IKEv2State.SA_INIT_COMPLETED)

        if self.current_exchange:
            self.current_exchange.responder_packet = packet
            self.current_exchange.mark_completed(True)

        logger.debug("IKE_SA_INIT response processed")
        return None  # No response needed for initiator receiving response

    async def _handle_ike_auth(
        self, packet: IKEv2, context: HookContext
    ) -> Optional[IKEv2]:
        """
        Handle IKE_AUTH exchange.

        Args:
            packet: The received IKE_AUTH packet
            context: Hook context for the exchange

        Returns:
            Response packet if one should be generated
        """
        logger.debug(f"Handling IKE_AUTH as {self.role.value}")

        try:
            # Execute exchange-specific hooks
            await self.hook_manager.execute_hooks(HookType.EXCHANGE_STEP, context)

            if self.role.value == "responder":
                return await self._handle_ike_auth_responder(packet, context)
            else:
                return await self._handle_ike_auth_initiator(packet, context)

        except Exception as e:
            logger.error(f"Error in IKE_AUTH handler: {e}")
            if self.current_exchange:
                self.current_exchange.mark_completed(False, str(e))
            raise

    async def _handle_ike_auth_responder(
        self, packet: IKEv2, context: HookContext
    ) -> Optional[IKEv2]:
        """Handle IKE_AUTH as responder."""
        logger.debug("Processing IKE_AUTH as responder")

        if not self.ike_sa:
            raise ValueError("No IKE SA established for AUTH exchange")

        # Build AUTH response
        response = self.payload_builder.build_ike_auth_response(
            spi_i=self.ike_sa.spi_i,
            spi_r=self.ike_sa.spi_r,
            message_id=packet.id,
            auth_method=9,  # ECDSA_SECP256R1_SHA256
        )

        # Update state
        self.state_machine.transition_to(IKEv2State.ESTABLISHED)

        if self.current_exchange:
            self.current_exchange.responder_packet = response
            self.current_exchange.mark_completed(True)

        logger.debug("IKE_AUTH response generated - IKE SA established")
        return response

    async def _handle_ike_auth_initiator(
        self, packet: IKEv2, context: HookContext
    ) -> Optional[IKEv2]:
        """Handle IKE_AUTH as initiator (processing response)."""
        logger.debug("Processing IKE_AUTH response as initiator")

        # Update state
        self.state_machine.transition_to(IKEv2State.ESTABLISHED)

        if self.current_exchange:
            self.current_exchange.responder_packet = packet
            self.current_exchange.mark_completed(True)

        logger.debug("IKE_AUTH response processed - IKE SA established")
        return None  # No response needed for initiator receiving response

    async def _handle_create_child_sa(
        self, packet: IKEv2, context: HookContext
    ) -> Optional[IKEv2]:
        """
        Handle CREATE_CHILD_SA exchange.

        Args:
            packet: The received CREATE_CHILD_SA packet
            context: Hook context for the exchange

        Returns:
            Response packet if one should be generated
        """
        logger.debug(f"Handling CREATE_CHILD_SA as {self.role.value}")

        try:
            # Execute exchange-specific hooks
            await self.hook_manager.execute_hooks(HookType.EXCHANGE_STEP, context)

            if self.role.value == "responder":
                return await self._handle_create_child_sa_responder(packet, context)
            else:
                return await self._handle_create_child_sa_initiator(packet, context)

        except Exception as e:
            logger.error(f"Error in CREATE_CHILD_SA handler: {e}")
            if self.current_exchange:
                self.current_exchange.mark_completed(False, str(e))
            raise

    async def _handle_create_child_sa_responder(
        self, packet: IKEv2, context: HookContext
    ) -> Optional[IKEv2]:
        """Handle CREATE_CHILD_SA as responder."""
        logger.debug("Processing CREATE_CHILD_SA as responder")

        if not self.ike_sa:
            raise ValueError("No IKE SA established for CREATE_CHILD_SA exchange")

        # Generate child SPI
        child_spi_r = secrets.token_bytes(4)

        # Build CREATE_CHILD_SA response
        response = self.payload_builder.build_create_child_sa_response(
            spi_i=self.ike_sa.spi_i,
            spi_r=self.ike_sa.spi_r,
            message_id=packet.id,
            child_spi=child_spi_r,
        )

        # Store child SA
        self.child_sas[child_spi_r] = {
            "spi": child_spi_r,
            "created_at": datetime.now(),
            "state": "established",
        }

        # Update state
        self.state_machine.transition_to(IKEv2State.CHILD_SA_ESTABLISHED)

        if self.current_exchange:
            self.current_exchange.responder_packet = response
            self.current_exchange.mark_completed(True)

        logger.debug("CREATE_CHILD_SA response generated")
        return response

    async def _handle_create_child_sa_initiator(
        self, packet: IKEv2, context: HookContext
    ) -> Optional[IKEv2]:
        """Handle CREATE_CHILD_SA as initiator (processing response)."""
        logger.debug("Processing CREATE_CHILD_SA response as initiator")

        # Update state
        self.state_machine.transition_to(IKEv2State.CHILD_SA_ESTABLISHED)

        if self.current_exchange:
            self.current_exchange.responder_packet = packet
            self.current_exchange.mark_completed(True)

        logger.debug("CREATE_CHILD_SA response processed")
        return None  # No response needed for initiator receiving response

    async def _handle_informational(
        self, packet: IKEv2, context: HookContext
    ) -> Optional[IKEv2]:
        """
        Handle INFORMATIONAL exchange.

        Args:
            packet: The received INFORMATIONAL packet
            context: Hook context for the exchange

        Returns:
            Response packet if one should be generated
        """
        logger.debug(f"Handling INFORMATIONAL as {self.role.value}")

        try:
            # Execute exchange-specific hooks
            await self.hook_manager.execute_hooks(HookType.EXCHANGE_STEP, context)

            if self.role.value == "responder":
                return await self._handle_informational_responder(packet, context)
            else:
                return await self._handle_informational_initiator(packet, context)

        except Exception as e:
            logger.error(f"Error in INFORMATIONAL handler: {e}")
            if self.current_exchange:
                self.current_exchange.mark_completed(False, str(e))
            raise

    async def _handle_informational_responder(
        self, packet: IKEv2, context: HookContext
    ) -> Optional[IKEv2]:
        """Handle INFORMATIONAL as responder."""
        logger.debug("Processing INFORMATIONAL as responder")

        if not self.ike_sa:
            raise ValueError("No IKE SA established for INFORMATIONAL exchange")

        # Build INFORMATIONAL response (usually empty)
        response = self.payload_builder.build_informational_response(
            spi_i=self.ike_sa.spi_i,
            spi_r=self.ike_sa.spi_r,
            message_id=packet.id,
        )

        if self.current_exchange:
            self.current_exchange.responder_packet = response
            self.current_exchange.mark_completed(True)

        logger.debug("INFORMATIONAL response generated")
        return response

    async def _handle_informational_initiator(
        self, packet: IKEv2, context: HookContext
    ) -> Optional[IKEv2]:
        """Handle INFORMATIONAL as initiator (processing response)."""
        logger.debug("Processing INFORMATIONAL response as initiator")

        if self.current_exchange:
            self.current_exchange.responder_packet = packet
            self.current_exchange.mark_completed(True)

        logger.debug("INFORMATIONAL response processed")
        return None  # No response needed for initiator receiving response
