"""
Enhanced ESP protocol implementation with comprehensive hook support.

This module provides a modern, extensible ESP implementation that improves upon
the original ipsecdr design with better separation of concerns, comprehensive
hook system, and enhanced security features.
"""

import secrets
import struct
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Callable, Any, Union, Tuple
from dataclasses import dataclass, field

from scapy.layers.ipsec import ESP
from scapy.layers.inet import IP

from models.base import BaseModel, TimestampedModel
from utils.logging import get_logger
from ..crypto.algorithms import EncryptionAlgorithm, IntegrityAlgorithm

# Note: These imports will be created next
# from .hooks import <PERSON>SPHookManager, ESPHookType, ESPHookContext
# from .sa import ESPSecurityAssociation


# Temporary placeholders
class ESPHookManager:
    def __init__(self):
        pass

    async def execute_hooks(self, hook_type, context):
        return {}


class ESPHookType:
    PRE_ENCRYPT = "pre_encrypt"
    POST_ENCRYPT = "post_encrypt"
    PRE_DECRYPT = "pre_decrypt"
    POST_DECRYPT = "post_decrypt"
    ERROR = "error"


class ESPHookContext:
    def __init__(self, **kwargs):
        for k, v in kwargs.items():
            setattr(self, k, v)


class ESPSecurityAssociation:
    def __init__(self, spi, encryption_algorithm, integrity_algorithm=None):
        self.spi = spi
        self.encryption_algorithm = encryption_algorithm
        self.integrity_algorithm = integrity_algorithm
        self.encryption_key = b"test_key_32_bytes_long_for_aes256"
        self.integrity_key = b"test_integrity_key_32_bytes_long"
        self._sequence_number = 0
        self._replay_window = set()

    def get_next_sequence_number(self):
        self._sequence_number += 1
        return self._sequence_number

    def check_replay(self, seq_num):
        if seq_num in self._replay_window:
            return False
        self._replay_window.add(seq_num)
        return True


logger = get_logger(__name__)


class ESPMode(Enum):
    """ESP operation modes."""

    TUNNEL = "tunnel"
    TRANSPORT = "transport"


class ESPDirection(Enum):
    """ESP traffic direction."""

    INBOUND = "inbound"
    OUTBOUND = "outbound"


@dataclass
class ESPPacketMetadata:
    """Metadata for ESP packet processing."""

    spi: int
    sequence_number: int
    direction: ESPDirection
    mode: ESPMode

    # Timing information
    processed_at: datetime = field(default_factory=datetime.now)
    processing_time: float = 0.0

    # Cryptographic information
    encryption_algorithm: Optional[str] = None
    integrity_algorithm: Optional[str] = None

    # Size information
    original_size: int = 0
    encrypted_size: int = 0
    padding_size: int = 0

    # Hook execution results
    hook_results: Dict[str, Any] = field(default_factory=dict)

    # Error information
    error: Optional[str] = None


@dataclass
class ESPStatistics:
    """ESP processing statistics."""

    packets_encrypted: int = 0
    packets_decrypted: int = 0
    packets_failed: int = 0

    bytes_encrypted: int = 0
    bytes_decrypted: int = 0

    total_processing_time: float = 0.0
    average_processing_time: float = 0.0

    # Security statistics
    replay_attacks_detected: int = 0
    integrity_failures: int = 0

    # Hook statistics
    hook_executions: int = 0
    hook_failures: int = 0

    def update_encryption_stats(self, packet_size: int, processing_time: float):
        """Update encryption statistics."""
        self.packets_encrypted += 1
        self.bytes_encrypted += packet_size
        self._update_timing_stats(processing_time)

    def update_decryption_stats(self, packet_size: int, processing_time: float):
        """Update decryption statistics."""
        self.packets_decrypted += 1
        self.bytes_decrypted += packet_size
        self._update_timing_stats(processing_time)

    def update_failure_stats(self):
        """Update failure statistics."""
        self.packets_failed += 1

    def _update_timing_stats(self, processing_time: float):
        """Update timing statistics."""
        self.total_processing_time += processing_time
        total_packets = self.packets_encrypted + self.packets_decrypted
        if total_packets > 0:
            self.average_processing_time = self.total_processing_time / total_packets


class ESPProtocol:
    """
    Enhanced ESP protocol implementation with comprehensive hook support.

    This class provides a complete ESP implementation that supports:
    - Comprehensive hook system for packet processing callbacks
    - Security association management
    - Anti-replay protection
    - Extensible cryptographic operations
    - Rich metadata collection and statistics
    """

    def __init__(
        self,
        mode: ESPMode = ESPMode.TUNNEL,
        hook_manager: Optional[ESPHookManager] = None,
        enable_anti_replay: bool = True,
        replay_window_size: int = 64,
    ):
        """
        Initialize the ESP protocol handler.

        Args:
            mode: ESP operation mode (tunnel or transport)
            hook_manager: Optional hook manager for callbacks
            enable_anti_replay: Whether to enable anti-replay protection
            replay_window_size: Size of the replay detection window
        """
        self.mode = mode
        self.hook_manager = hook_manager or ESPHookManager()
        self.enable_anti_replay = enable_anti_replay
        self.replay_window_size = replay_window_size

        # Security associations
        self.inbound_sas: Dict[int, ESPSecurityAssociation] = {}  # SPI -> SA
        self.outbound_sas: Dict[int, ESPSecurityAssociation] = {}  # SPI -> SA

        # Statistics
        self.statistics = ESPStatistics()

        # Packet tracking
        self.processed_packets: List[ESPPacketMetadata] = []
        self.packet_count = 0

        logger.info(
            f"ESPProtocol initialized (mode: {mode.value}, anti-replay: {enable_anti_replay})"
        )

    def add_security_association(
        self, sa: ESPSecurityAssociation, direction: ESPDirection
    ) -> None:
        """
        Add a security association.

        Args:
            sa: The security association to add
            direction: Direction (inbound or outbound)
        """
        if direction == ESPDirection.INBOUND:
            self.inbound_sas[sa.spi] = sa
        else:
            self.outbound_sas[sa.spi] = sa

        logger.info(f"Added {direction.value} SA with SPI {sa.spi:08x}")

    def remove_security_association(self, spi: int, direction: ESPDirection) -> bool:
        """
        Remove a security association.

        Args:
            spi: Security Parameter Index
            direction: Direction (inbound or outbound)

        Returns:
            True if SA was found and removed
        """
        if direction == ESPDirection.INBOUND:
            if spi in self.inbound_sas:
                del self.inbound_sas[spi]
                logger.info(f"Removed inbound SA with SPI {spi:08x}")
                return True
        else:
            if spi in self.outbound_sas:
                del self.outbound_sas[spi]
                logger.info(f"Removed outbound SA with SPI {spi:08x}")
                return True

        logger.warning(f"SA with SPI {spi:08x} not found for removal")
        return False

    async def encrypt_packet(
        self, packet: IP, spi: int, **kwargs
    ) -> Tuple[ESP, ESPPacketMetadata]:
        """
        Encrypt a packet using ESP.

        Args:
            packet: The IP packet to encrypt
            spi: Security Parameter Index
            **kwargs: Additional parameters

        Returns:
            Tuple of (encrypted ESP packet, metadata)
        """
        start_time = datetime.now()
        self.packet_count += 1

        # Get outbound SA
        if spi not in self.outbound_sas:
            raise ValueError(f"No outbound SA found for SPI {spi:08x}")

        sa = self.outbound_sas[spi]

        # Create metadata
        metadata = ESPPacketMetadata(
            spi=spi,
            sequence_number=sa.get_next_sequence_number(),
            direction=ESPDirection.OUTBOUND,
            mode=self.mode,
            original_size=len(packet),
            encryption_algorithm=sa.encryption_algorithm.name,
            integrity_algorithm=(
                sa.integrity_algorithm.name if sa.integrity_algorithm else None
            ),
        )

        # Create hook context
        hook_context = ESPHookContext(
            packet=packet,
            spi=spi,
            direction=ESPDirection.OUTBOUND,
            mode=self.mode,
            sa=sa,
            metadata=metadata,
            packet_number=self.packet_count,
        )

        try:
            # Execute pre-encryption hooks
            await self.hook_manager.execute_hooks(ESPHookType.PRE_ENCRYPT, hook_context)

            # Perform encryption
            esp_packet = await self._perform_encryption(packet, sa, metadata)

            # Update hook context with result
            hook_context.esp_packet = esp_packet
            hook_context.metadata = metadata

            # Execute post-encryption hooks
            await self.hook_manager.execute_hooks(
                ESPHookType.POST_ENCRYPT, hook_context
            )

            # Update statistics
            processing_time = (datetime.now() - start_time).total_seconds()
            metadata.processing_time = processing_time
            self.statistics.update_encryption_stats(len(packet), processing_time)

            # Store metadata
            self.processed_packets.append(metadata)

            logger.debug(
                f"Encrypted packet with SPI {spi:08x}, seq {metadata.sequence_number}"
            )

            return esp_packet, metadata

        except Exception as e:
            logger.error(f"Encryption failed for SPI {spi:08x}: {e}")
            metadata.error = str(e)
            self.statistics.update_failure_stats()

            # Execute error hooks
            hook_context.error = e
            await self.hook_manager.execute_hooks(ESPHookType.ERROR, hook_context)

            raise

    async def decrypt_packet(
        self, esp_packet: ESP, **kwargs
    ) -> Tuple[IP, ESPPacketMetadata]:
        """
        Decrypt an ESP packet.

        Args:
            esp_packet: The ESP packet to decrypt
            **kwargs: Additional parameters

        Returns:
            Tuple of (decrypted IP packet, metadata)
        """
        start_time = datetime.now()
        self.packet_count += 1

        spi = esp_packet.spi

        # Get inbound SA
        if spi not in self.inbound_sas:
            raise ValueError(f"No inbound SA found for SPI {spi:08x}")

        sa = self.inbound_sas[spi]

        # Create metadata
        metadata = ESPPacketMetadata(
            spi=spi,
            sequence_number=esp_packet.seq,
            direction=ESPDirection.INBOUND,
            mode=self.mode,
            encrypted_size=len(esp_packet),
            encryption_algorithm=sa.encryption_algorithm.name,
            integrity_algorithm=(
                sa.integrity_algorithm.name if sa.integrity_algorithm else None
            ),
        )

        # Create hook context
        hook_context = ESPHookContext(
            esp_packet=esp_packet,
            spi=spi,
            direction=ESPDirection.INBOUND,
            mode=self.mode,
            sa=sa,
            metadata=metadata,
            packet_number=self.packet_count,
        )

        try:
            # Execute pre-decryption hooks
            await self.hook_manager.execute_hooks(ESPHookType.PRE_DECRYPT, hook_context)

            # Check anti-replay
            if self.enable_anti_replay:
                if not sa.check_replay(esp_packet.seq):
                    self.statistics.replay_attacks_detected += 1
                    raise ValueError(
                        f"Replay attack detected for sequence {esp_packet.seq}"
                    )

            # Perform decryption
            decrypted_packet = await self._perform_decryption(esp_packet, sa, metadata)

            # Update hook context with result
            hook_context.packet = decrypted_packet
            hook_context.metadata = metadata

            # Execute post-decryption hooks
            await self.hook_manager.execute_hooks(
                ESPHookType.POST_DECRYPT, hook_context
            )

            # Update statistics
            processing_time = (datetime.now() - start_time).total_seconds()
            metadata.processing_time = processing_time
            metadata.original_size = len(decrypted_packet)
            self.statistics.update_decryption_stats(
                len(decrypted_packet), processing_time
            )

            # Store metadata
            self.processed_packets.append(metadata)

            logger.debug(
                f"Decrypted packet with SPI {spi:08x}, seq {metadata.sequence_number}"
            )

            return decrypted_packet, metadata

        except Exception as e:
            logger.error(f"Decryption failed for SPI {spi:08x}: {e}")
            metadata.error = str(e)
            self.statistics.update_failure_stats()

            # Execute error hooks
            hook_context.error = e
            await self.hook_manager.execute_hooks(ESPHookType.ERROR, hook_context)

            raise

    async def _perform_encryption(
        self, packet: IP, sa: ESPSecurityAssociation, metadata: ESPPacketMetadata
    ) -> ESP:
        """Perform the actual encryption operation."""

        # Prepare payload based on mode
        if self.mode == ESPMode.TUNNEL:
            # In tunnel mode, encrypt the entire IP packet
            payload = bytes(packet)
        else:
            # In transport mode, encrypt only the payload
            payload = bytes(packet.payload)

        # Add padding
        padded_payload, pad_length = self._add_padding(
            payload, sa.encryption_algorithm.block_size
        )
        metadata.padding_size = pad_length

        # Create ESP header
        esp_header = struct.pack("!II", sa.spi, sa.get_next_sequence_number())

        # Encrypt payload
        iv = secrets.token_bytes(sa.encryption_algorithm.iv_size)
        encrypted_payload = sa.encryption_algorithm.encrypt(
            sa.encryption_key, padded_payload, iv
        )

        # Create ESP packet
        esp_packet = ESP(
            spi=sa.spi, seq=metadata.sequence_number, data=encrypted_payload
        )

        # Add integrity check if required
        if sa.integrity_algorithm and not sa.encryption_algorithm.is_aead:
            icv = sa.integrity_algorithm.compute(
                sa.integrity_key, esp_header + encrypted_payload
            )
            # Note: In a real implementation, this would be properly attached

        metadata.encrypted_size = len(esp_packet)

        return esp_packet

    async def _perform_decryption(
        self, esp_packet: ESP, sa: ESPSecurityAssociation, metadata: ESPPacketMetadata
    ) -> IP:
        """Perform the actual decryption operation."""

        # Verify integrity if required
        if sa.integrity_algorithm and not sa.encryption_algorithm.is_aead:
            # Note: In a real implementation, this would verify the ICV
            pass

        # Extract IV and encrypted data
        encrypted_data = esp_packet.data

        # Decrypt payload
        decrypted_payload = sa.encryption_algorithm.decrypt(
            sa.encryption_key,
            encrypted_data,
            b"\x00" * sa.encryption_algorithm.iv_size,  # Simplified IV handling
        )

        # Remove padding
        unpadded_payload, pad_length = self._remove_padding(decrypted_payload)
        metadata.padding_size = pad_length

        # Reconstruct packet based on mode
        if self.mode == ESPMode.TUNNEL:
            # In tunnel mode, the payload is the complete IP packet
            decrypted_packet = IP(unpadded_payload)
        else:
            # In transport mode, reconstruct with original IP header
            # Note: This is simplified - real implementation would preserve original header
            decrypted_packet = IP(unpadded_payload)

        return decrypted_packet

    def _add_padding(self, payload: bytes, block_size: int) -> Tuple[bytes, int]:
        """Add ESP padding to payload."""
        if block_size <= 1:
            return payload, 0

        # Calculate padding needed
        payload_len = len(payload)
        pad_length = (block_size - ((payload_len + 2) % block_size)) % block_size

        # Create padding
        padding = bytes(range(1, pad_length + 1))

        # Add padding and pad length
        padded = payload + padding + bytes([pad_length, 4])  # 4 = IP protocol

        return padded, pad_length

    def _remove_padding(self, padded_payload: bytes) -> Tuple[bytes, int]:
        """Remove ESP padding from payload."""
        if len(padded_payload) < 2:
            return padded_payload, 0

        # Extract pad length and next header
        pad_length = padded_payload[-2]
        next_header = padded_payload[-1]

        # Remove padding
        if pad_length > 0:
            payload = padded_payload[: -(pad_length + 2)]
        else:
            payload = padded_payload[:-2]

        return payload, pad_length

    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive ESP statistics."""
        return {
            "mode": self.mode.value,
            "packets_processed": self.packet_count,
            "inbound_sas": len(self.inbound_sas),
            "outbound_sas": len(self.outbound_sas),
            "encryption_stats": {
                "packets_encrypted": self.statistics.packets_encrypted,
                "bytes_encrypted": self.statistics.bytes_encrypted,
            },
            "decryption_stats": {
                "packets_decrypted": self.statistics.packets_decrypted,
                "bytes_decrypted": self.statistics.bytes_decrypted,
            },
            "security_stats": {
                "replay_attacks_detected": self.statistics.replay_attacks_detected,
                "integrity_failures": self.statistics.integrity_failures,
                "packets_failed": self.statistics.packets_failed,
            },
            "performance_stats": {
                "total_processing_time": self.statistics.total_processing_time,
                "average_processing_time": self.statistics.average_processing_time,
            },
            "hook_stats": {
                "hook_executions": self.statistics.hook_executions,
                "hook_failures": self.statistics.hook_failures,
            },
        }
