#!/usr/bin/env python3
"""
Simple test to verify that configuration validation errors are properly raised.
"""

import sys
import tempfile
import toml
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from ipsecator.utils.configuration import IPsecEvaluatorConfigParser, ConfigurationError
    print("✓ Successfully imported configuration parser")
except ImportError as e:
    print(f"✗ Failed to import configuration parser: {e}")
    sys.exit(1)

def create_invalid_config():
    """Create an invalid configuration for testing."""
    return {
        'global_config': {
            'timeout': -10,  # Invalid: negative timeout
            'verbose': 'invalid_boolean',  # Invalid: not a boolean
            'log_level': 'INVALID_LEVEL',  # Invalid: unknown log level
            'max_concurrent_tests': 0  # Invalid: must be >= 1
        },
        'network': {
            'port_src': 70000,  # Invalid: port out of range
            'port_dst': -1,  # Invalid: negative port
            'ipsec_src': 'invalid_ip',  # Invalid: not an IP address
            'nat_traversal': 'not_a_boolean'  # Invalid: not a boolean
        },
        'crypto': {
            'dh_groups': [999],  # Invalid: unsupported DH group
            'encryption_algorithms': [],  # Invalid: empty list
        }
    }

def test_validation_failure():
    """Test that configuration validation failure raises ConfigurationError."""
    print("\nTesting configuration validation failure...")
    
    # Create invalid configuration
    invalid_config = create_invalid_config()
    
    # Write to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
        toml.dump(invalid_config, f)
        config_file = f.name
    
    try:
        # This should raise ConfigurationError
        parser = IPsecEvaluatorConfigParser(
            config_files=[config_file],
            validate_config=True
        )
        print("✗ Expected ConfigurationError was not raised!")
        return False
    except ConfigurationError as e:
        print(f"✓ ConfigurationError properly raised: {e}")
        return True
    except Exception as e:
        print(f"✗ Unexpected exception raised: {type(e).__name__}: {e}")
        return False
    finally:
        # Clean up
        try:
            Path(config_file).unlink()
        except:
            pass

def test_validation_success():
    """Test that valid configuration loads successfully."""
    print("\nTesting valid configuration loading...")
    
    # Create valid configuration
    valid_config = {
        'global_config': {
            'timeout': 30,
            'verbose': True,
            'log_level': 'DEBUG',
            'max_concurrent_tests': 2
        },
        'network': {
            'interface': 'eth0',
            'ipsec_src': '************',
            'ipsec_dst': '************',
            'ip_src': '************',
            'ip_dst': '************',
            'port_src': 500,
            'port_dst': 500,
            'nat_traversal': False,
            'test_network': '***********/24'
        },
        'crypto': {
            'encryption_algorithms': ['AES-GCM-256'],
            'integrity_algorithms': ['HMAC-SHA2-256'],
            'dh_groups': [19],
            'prf_algorithms': ['HMAC-SHA2-256']
        }
    }
    
    # Write to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
        toml.dump(valid_config, f)
        config_file = f.name
    
    try:
        # This should work fine
        parser = IPsecEvaluatorConfigParser(
            config_files=[config_file],
            validate_config=True
        )
        print("✓ Valid configuration loaded successfully")
        return True
    except Exception as e:
        print(f"✗ Unexpected exception with valid config: {type(e).__name__}: {e}")
        return False
    finally:
        # Clean up
        try:
            Path(config_file).unlink()
        except:
            pass

def main():
    """Main test function."""
    print("Testing Configuration Validation Fix")
    print("=" * 40)
    
    success = True
    
    # Test validation failure
    if not test_validation_failure():
        success = False
    
    # Test validation success
    if not test_validation_success():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("✓ All tests passed!")
        return 0
    else:
        print("✗ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
