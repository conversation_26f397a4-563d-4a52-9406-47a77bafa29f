#!/usr/bin/env python3
"""
Simple test runner to validate test structure without requiring all dependencies.
"""

import sys
import os
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def check_imports():
    """Check if basic imports work."""
    print("Checking imports...")
    
    try:
        from models.base import BaseModel, ConfigModel, TestMode
        print("✓ Base models import successful")
    except ImportError as e:
        print(f"✗ Base models import failed: {e}")
        return False
    
    try:
        from models.config import GlobalConfig, NetworkConfig, CryptoConfig
        print("✓ Config models import successful")
    except ImportError as e:
        print(f"✗ Config models import failed: {e}")
        return False
    
    try:
        from utils.configuration import IPsecEvaluatorConfigParser, ConfigurationError
        print("✓ Configuration parser import successful")
    except ImportError as e:
        print(f"✗ Configuration parser import failed: {e}")
        return False
    
    return True

def check_test_structure():
    """Check test file structure."""
    print("\nChecking test structure...")
    
    test_files = [
        "tests/unit/utils/__init__.py",
        "tests/unit/utils/test_configuration.py",
        "tests/support/config_helpers.py",
        "tests/conftest.py"
    ]
    
    for test_file in test_files:
        file_path = Path(__file__).parent / test_file
        if file_path.exists():
            print(f"✓ {test_file} exists")
        else:
            print(f"✗ {test_file} missing")
            return False
    
    return True

def validate_test_helpers():
    """Validate test helper functions."""
    print("\nValidating test helpers...")
    
    try:
        sys.path.insert(0, str(Path(__file__).parent / "tests"))
        from support.config_helpers import (
            write_config_file,
            create_basic_toml_config,
            create_basic_yaml_config,
            create_basic_ini_config
        )
        print("✓ Test helper imports successful")
        
        # Test basic functionality
        config_data = create_basic_toml_config()
        assert isinstance(config_data, dict)
        assert 'global_config' in config_data
        print("✓ Test helper functions work")
        
        return True
    except Exception as e:
        print(f"✗ Test helper validation failed: {e}")
        return False

def main():
    """Main test runner."""
    print("IPsec Evaluator Configuration Test Validation")
    print("=" * 50)
    
    success = True
    
    # Check imports
    if not check_imports():
        success = False
    
    # Check test structure
    if not check_test_structure():
        success = False
    
    # Validate test helpers
    if not validate_test_helpers():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✓ All validation checks passed!")
        print("\nTo run the actual tests, install dependencies with:")
        print("  poetry install")
        print("  poetry run pytest tests/unit/utils/test_configuration.py -v")
    else:
        print("✗ Some validation checks failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
