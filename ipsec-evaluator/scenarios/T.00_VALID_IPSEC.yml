name: T.00_VALID_IPSEC
description: "Basic IPsec compliance validation test"
version: "1.0"
anssi_requirement: "Basic IPsec functionality validation"

test_scenarios:
  initiator:
    - valid_client:
        description: "Standard compliant initiator behavior"
        mode: strict
        hooks:
          packet_callbacks:
            - packet: 1
              exchange: IKE_SA_INIT
              callback: validate_init_proposal
              description: "Validate initial proposal contains ANSSI-approved algorithms"
            - packet: 2
              exchange: IKE_AUTH
              callback: validate_auth_method
              description: "Validate authentication method compliance"
          exchange_callbacks:
            - exchange: IKE_SA_INIT
              callback: check_ike_sa_algorithms
              description: "Verify IKE SA uses approved algorithms"
            - exchange: IKE_AUTH
              callback: check_authentication
              description: "Verify authentication process"
            - exchange: CREATE_CHILD_SA
              callback: check_child_sa_algorithms
              description: "Verify Child SA uses approved algorithms"
          universal_callback: log_all_packets
        configuration:
          crypto:
            ike_encryption: ["AES_GCM_16"]
            ike_integrity: ["HMAC_SHA2_256"]
            ike_prf: ["PRF_HMAC_SHA2_256"]
            ike_dh_groups: [19]  # ECP-256
            esp_encryption: ["AES_GCM_16"]
            esp_integrity: ["HMAC_SHA2_256"]
          network:
            initiator_ip: "************"
            responder_ip: "************"
            nat_traversal: true
        exchanges:
          - type: IKE_SA_INIT
            expected_packets: 2
            timeout: 30
          - type: IKE_AUTH
            expected_packets: 2
            timeout: 30
          - type: CREATE_CHILD_SA
            expected_packets: 2
            timeout: 30
        compliance_checks:
          - check_anssi_algorithms
          - check_key_sizes
          - check_certificate_validation
          - check_perfect_forward_secrecy

  responder:
    - valid_server:
        description: "Standard compliant responder behavior"
        mode: strict
        hooks:
          packet_callbacks:
            - packet: 1
              exchange: IKE_SA_INIT
              callback: validate_received_proposal
              description: "Validate received proposal"
            - packet: 2
              exchange: IKE_AUTH
              callback: validate_auth_payload
              description: "Validate authentication payload"
          exchange_callbacks:
            - exchange: IKE_SA_INIT
              callback: respond_with_compliant_proposal
              description: "Respond with ANSSI-compliant proposal"
            - exchange: IKE_AUTH
              callback: perform_authentication
              description: "Perform authentication"
          universal_callback: monitor_all_traffic
        configuration:
          crypto:
            ike_encryption: ["AES_GCM_16"]
            ike_integrity: ["HMAC_SHA2_256"]
            ike_prf: ["PRF_HMAC_SHA2_256"]
            ike_dh_groups: [19]  # ECP-256
            esp_encryption: ["AES_GCM_16"]
            esp_integrity: ["HMAC_SHA2_256"]
          network:
            initiator_ip: "************"
            responder_ip: "************"
            nat_traversal: true
        exchanges:
          - type: IKE_SA_INIT
            expected_packets: 2
            timeout: 30
          - type: IKE_AUTH
            expected_packets: 2
            timeout: 30
          - type: CREATE_CHILD_SA
            expected_packets: 2
            timeout: 30
        compliance_checks:
          - check_proposal_selection
          - check_algorithm_negotiation
          - check_certificate_chain
          - check_responder_behavior

expected_outcomes:
  initiator:
    - exchange: IKE_SA_INIT
      status: success
      compliance: compliant
      checks:
        - algorithm_compliance: true
        - key_size_compliance: true
    - exchange: IKE_AUTH
      status: success
      compliance: compliant
      checks:
        - authentication_success: true
        - certificate_valid: true
    - exchange: CREATE_CHILD_SA
      status: success
      compliance: compliant
      checks:
        - child_sa_established: true
        - esp_algorithms_compliant: true

  responder:
    - exchange: IKE_SA_INIT
      status: success
      compliance: compliant
      checks:
        - proposal_accepted: true
        - algorithms_selected: true
    - exchange: IKE_AUTH
      status: success
      compliance: compliant
      checks:
        - authentication_verified: true
        - certificates_validated: true
    - exchange: CREATE_CHILD_SA
      status: success
      compliance: compliant
      checks:
        - child_sa_created: true
        - traffic_selectors_valid: true

metadata:
  tags: ["basic", "compliance", "anssi"]
  priority: high
  estimated_duration: 60  # seconds
  requirements:
    - strongswan_version: ">=5.9.0"
    - certificates: true
    - network_isolation: true
