name: T.01_ENUM_CRYPTO_ALGORITHMS
description: "Enumerate and test various cryptographic algorithms for IKE SA"
version: "1.0"
anssi_requirement: "Algorithm enumeration and validation"
tags: ["enumeration", "crypto", "algorithms", "ike"]
compliance_level: "standard"

test_scenarios:
  initiator:
    - enum_encryption_algorithms:
        description: "Enumerate encryption algorithms in IKE_SA_INIT"
        mode: target
        targets:
          - exchange: IKE_SA_INIT
            action: enum
            check_function: check_encryption_algorithm_support
            fields:
              - ipsec.crypto.encryption_algorithms:
                  - AES-GCM-256
                  - AES-CTR
                  - AES-CBC
                  - ChaCha20-Poly1305
              - ipsec.crypto.integrity_algorithms: any
              - ipsec.crypto.dh_groups: [19]  # Fixed to ECP-256
              - ipsec.crypto.prf_algorithms: ["HMAC-SHA2-256"]
            overlay_config:
              crypto:
                encryption_key_sizes: [256]
              network:
                nat_traversal: false
    
    - enum_integrity_algorithms:
        description: "Enumerate integrity algorithms in IKE_SA_INIT"
        mode: target
        targets:
          - exchange: IKE_SA_INIT
            action: enum
            check_function: check_integrity_algorithm_support
            fields:
              - ipsec.crypto.encryption_algorithms: ["AES-GCM-256"]  # Fixed
              - ipsec.crypto.integrity_algorithms:
                  - HMAC-SHA2-256
                  - HMAC-SHA2-384
                  - HMAC-SHA2-512
                  - HMAC-SHA1
              - ipsec.crypto.dh_groups: [19]
              - ipsec.crypto.prf_algorithms: any
    
    - enum_dh_groups:
        description: "Enumerate Diffie-Hellman groups"
        mode: target
        targets:
          - exchange: IKE_SA_INIT
            action: enum
            check_function: check_dh_group_support
            fields:
              - ipsec.crypto.encryption_algorithms: ["AES-GCM-256"]
              - ipsec.crypto.integrity_algorithms: ["HMAC-SHA2-256"]
              - ipsec.crypto.dh_groups:
                  - 14  # 2048-bit MODP
                  - 15  # 3072-bit MODP
                  - 16  # 4096-bit MODP
                  - 19  # 256-bit ECP
                  - 20  # 384-bit ECP
                  - 21  # 521-bit ECP
              - ipsec.crypto.prf_algorithms: ["HMAC-SHA2-256"]

  responder:
    - respond_to_enum:
        description: "Respond to algorithm enumeration attempts"
        mode: strict
        packets:
          - number: 1
            exchange: IKE_SA_INIT
            check_function: validate_proposal_response
          - number: 2
            exchange: IKE_AUTH
            check_function: validate_auth_response
            overlay_config:
              auth:
                auth_method: "ECDSA_SECP256R1_SHA256"
                certificate_auth: true

expected_outcomes:
  initiator:
    - exchange: IKE_SA_INIT
      status: success
      compliance: compliant
      checks:
        - algorithm_enumeration_complete: true
        - supported_algorithms_identified: true
        - anssi_compliant_algorithms_found: true
  responder:
    - exchange: IKE_SA_INIT
      status: success
      compliance: compliant
      checks:
        - proposal_selection_valid: true
        - algorithm_negotiation_successful: true

compliance_checks:
  - check_anssi_approved_algorithms
  - check_algorithm_strength
  - check_key_size_compliance
  - verify_no_weak_algorithms
