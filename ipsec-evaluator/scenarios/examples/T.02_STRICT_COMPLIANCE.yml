name: T.02_STRICT_COMPLIANCE
description: "Strict compliance test with predefined packet sequence"
version: "1.0"
anssi_requirement: "Full IPsec compliance validation"
tags: ["compliance", "strict", "full-sequence"]
compliance_level: "strict"

test_scenarios:
  initiator:
    - compliant_client_sequence:
        description: "Standard compliant client behavior with full exchange sequence"
        mode: strict
        packets:
          - number: 1
            exchange: IKE_SA_INIT
            check_function: validate_init_proposal
            overlay_config:
              crypto:
                encryption_algorithms: ["AES-GCM-256"]
                integrity_algorithms: ["HMAC-SHA2-256"]
                dh_groups: [19]  # ECP-256
                prf_algorithms: ["HMAC-SHA2-256"]
              network:
                nat_traversal: true
                initiator_ip: "************"
                responder_ip: "************"
          
          - number: 2
            exchange: IKE_AUTH
            check_function: validate_authentication
            overlay_config:
              auth:
                auth_method: "ECDSA_SECP256R1_SHA256"
                certificate_auth: true
              identity:
                idi_type: "ID_FQDN"
                idi_data: "client.example.com"
                idr_type: "ID_FQDN"
                idr_data: "server.example.com"
          
          - number: 3
            exchange: CREATE_CHILD_SA
            check_function: validate_child_sa_creation
            overlay_config:
              crypto:
                encryption_algorithms: ["AES-GCM-256"]
                integrity_algorithms: ["HMAC-SHA2-256"]
              ts:
                tsi_ip_range: "*************-***************"
                tsi_port_range: "0-65535"
                tsr_ip_range: "10.0.0.0-**********"
                tsr_port_range: "0-65535"
          
          - number: 4
            exchange: INFORMATIONAL
            check_function: validate_informational
            overlay_config:
              notify_type: "INITIAL_CONTACT"

  responder:
    - compliant_server_sequence:
        description: "Standard compliant server behavior"
        mode: strict
        packets:
          - number: 1
            exchange: IKE_SA_INIT
            check_function: validate_init_response
            overlay_config:
              crypto:
                encryption_algorithms: ["AES-GCM-256"]
                integrity_algorithms: ["HMAC-SHA2-256"]
                dh_groups: [19]
                prf_algorithms: ["HMAC-SHA2-256"]
          
          - number: 2
            exchange: IKE_AUTH
            check_function: validate_auth_response
            overlay_config:
              auth:
                auth_method: "ECDSA_SECP256R1_SHA256"
                certificate_auth: true
              identity:
                idi_type: "ID_FQDN"
                idi_data: "server.example.com"
          
          - number: 3
            exchange: CREATE_CHILD_SA
            check_function: validate_child_sa_response
          
          - number: 4
            exchange: INFORMATIONAL
            check_function: validate_informational_response

expected_outcomes:
  initiator:
    - exchange: IKE_SA_INIT
      status: success
      compliance: compliant
      checks:
        - proposal_sent: true
        - algorithms_compliant: true
        - dh_group_valid: true
    - exchange: IKE_AUTH
      status: success
      compliance: compliant
      checks:
        - authentication_successful: true
        - certificate_valid: true
        - identity_verified: true
    - exchange: CREATE_CHILD_SA
      status: success
      compliance: compliant
      checks:
        - child_sa_established: true
        - traffic_selectors_valid: true
        - esp_algorithms_compliant: true
    - exchange: INFORMATIONAL
      status: success
      compliance: compliant
      checks:
        - informational_processed: true

  responder:
    - exchange: IKE_SA_INIT
      status: success
      compliance: compliant
      checks:
        - proposal_accepted: true
        - response_generated: true
    - exchange: IKE_AUTH
      status: success
      compliance: compliant
      checks:
        - authentication_verified: true
        - response_sent: true
    - exchange: CREATE_CHILD_SA
      status: success
      compliance: compliant
      checks:
        - child_sa_created: true
        - response_sent: true
    - exchange: INFORMATIONAL
      status: success
      compliance: compliant
      checks:
        - informational_acknowledged: true

compliance_checks:
  - check_full_exchange_sequence
  - check_anssi_algorithm_compliance
  - check_certificate_validation
  - check_perfect_forward_secrecy
  - check_traffic_selector_compliance
  - verify_no_security_vulnerabilities
