ipsecdr is an old tool designed to perfom assessment on the ANSSI IPsecDR corpus and verify compliance. You can find relative pdf documents in the root of the workspace.

The goal is to do a full rewrite. We want to enhance optimize and build a better logic around orchestrator testers / checkers and hooks inside initiator and responder exchanges. I want aprofesional and modern python project.

The rewrite is going to be made inside ipsec-evaluator, this will be the new tool. We have started to tackle basis.
I want you to take a full understanding of ipsecdr (use tree to see project structure) and ipsec-evaluator. Then I want you to fill any blanks or discrapencies found in ipsec-evaluator. Make sure basis are working thight, orchestrator, test pools, client, server etc Look everything but dont go too deep.
 I do not want ou to produce infrstructure code, infrastructure will only be use dduring testing of the tooland i do not wwant you to care for it now.
 
Major Gaps:
Incomplete IKEv2/ESP Integration: Multiple protocol files exist but aren't properly integrated
Missing Hook Manager Implementation: Referenced but not implemented
Inconsistent Model Imports: Duplicate model files in different locations
Missing Infrastructure Components: No incus/opentofu integration
Incomplete Test Execution Flow: Orchestrator/Tester/Checker pattern partially implemented
Missing Configuration Integration: Parser exists but not fully integrated


Critical Issues Found:
Duplicate Model Structures: Models exist in both src/models/ and src/ipsec_evaluator/models/
Missing Hook Manager: Referenced everywhere but not implemented
Incomplete Protocol Integration: Multiple IKEv2 protocol files but no clear main implementation
Broken Import Paths: Inconsistent module structure
Missing Core Components: Infrastructure management, proper test execution flow
Detailed Plan:
Phase 1: Fix Core Architecture Issues
✅ Consolidate model structure (remove duplicates)
✅ Implement missing Hook Manager
✅ Fix import paths and module structure
✅ Consolidate IKEv2 protocol implementation
Phase 2: Complete Missing Components
✅ Implement proper orchestrator/tester/checker integration
✅ Add missing infrastructure components
✅ Fix scenario loading and parsing
✅ Complete configuration integration
Phase 3: Ensure Consistency
✅ Align with ipsecdr patterns while modernizing
✅ Add missing tests and validation
✅ Verify end-to-end workflow